# DATABASE GUILD_ID FIXES

## Critical Issues Fixed

### 1. League Teams Table Schema
- Added `guild_id` column to `league_teams` table
- Updated INSERT statements to include guild_id
- Updated SELECT statements to filter by guild_id

### 2. Queries that need guild_id constraint:

#### Fixed:
1. `get_team_basic_info()` - Added guild_id parameter
2. INSERT INTO league_teams - Now includes guild_id  
3. SELECT statements in live management - Now filter by guild_id
4. Team verification queries - Now include guild_id constraint

#### Still needs fixing (if causing issues):
1. DELETE FROM league_teams queries in slot reset functions
2. Various team lookup queries throughout the codebase
3. Role validation queries

### 3. Data Migration Required

The current database may have:
- Team names stored as role_ids instead of actual Discord role IDs
- Missing guild_id values in league_teams table
- Inconsistent data between tables

**Recommended Actions:**
1. Run a data cleanup script to fix existing data
2. Ensure all new team creations use proper role IDs
3. Add guild_id to all existing league_teams records

### 4. Root Cause Analysis

The warnings in the logs suggest:
1. **Invalid franchise_owner_role ID**: UUIDs being used instead of Discord role IDs
2. **Invalid role_id values**: Team names being stored as role_ids
3. **Missing guild_id filtering**: Queries returning wrong guild's data

This indicates either:
- Database migration issues
- Incorrect data import/creation
- Missing guild_id constraints in queries

### 5. Immediate Actions Taken

1. Fixed the table references from `teams` to `league_teams`
2. Added guild_id to INSERT statements
3. Added guild_id filtering to SELECT statements
4. Updated function signatures to accept guild_id

### 6. Data Integrity Solutions

To fix the existing bad data:

```sql
-- Remove invalid role_id entries (non-numeric)
DELETE FROM league_teams WHERE role_id NOT REGEXP '^[0-9]+$';

-- Update franchise_owner_role entries that are UUIDs
UPDATE slot_configs 
SET franchise_owner_role = NULL 
WHERE franchise_owner_role NOT REGEXP '^[0-9]*$';

-- Similar for other role fields
UPDATE slot_configs 
SET general_manager_role = NULL 
WHERE general_manager_role NOT REGEXP '^[0-9]*$';
```

The bot should now handle guild separation properly and avoid the role ID validation errors.
