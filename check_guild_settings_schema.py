#!/usr/bin/env python3
"""
Check the guild_settings table schema in Supabase to understand the column structure.
"""

import os
from supabase import create_client, Client

# Supabase credentials 
SUPABASE_URL = "https://qljbplxqmnkrxuqcnxuw.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFsamJwbHhxbW5rcnh1cWNueHV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ2NzI3NzIsImV4cCI6MjA1MDI0ODc3Mn0.sj2OZFiJCz2T9vKLG3xW30U7J2ZX_Z_p1xUFqn3WMhk"

def main():
    print("🔍 Checking guild_settings schema in Supabase")
    print("=" * 50)
    
    try:
        # Initialize Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Get guild_settings data to see what columns exist
        print("📋 Fetching guild_settings data...")
        guild_settings = supabase.table('guild_settings').select('*').execute()
        
        if guild_settings.data:
            print(f"📊 Found {len(guild_settings.data)} guild_settings records")
            
            # Show the keys/columns of the first record
            first_record = guild_settings.data[0]
            print("\n🔧 Available columns in guild_settings:")
            for key in sorted(first_record.keys()):
                value = first_record[key]
                print(f"  - {key}: {value} (type: {type(value).__name__})")
                
            print("\n📋 All guild_settings records:")
            for i, record in enumerate(guild_settings.data, 1):
                print(f"\n  Record {i}:")
                for key, value in record.items():
                    print(f"    {key}: {value}")
        else:
            print("📭 No guild_settings records found")
            
    except Exception as e:
        print(f"❌ Error checking guild_settings schema: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
