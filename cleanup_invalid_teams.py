#!/usr/bin/env python3
"""
Supabase Data Cleanup Script - Remove Invalid Teams
This script removes teams with invalid role_ids (team names in role_id fields)
from your Supabase database.

Upload this to your cloud server and run it to clean up the invalid data.
"""

import os
from supabase import create_client, Client

# Use the same Supabase credentials as your bot
SUPABASE_URL = "https://ueheoabegwccfkholyku.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlaGVvYWJlZ3djY2ZraG9seWt1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDM0MDEsImV4cCI6MjA2NzUxOTQwMX0.puxraTE7UGm9wkfqQvTxIp7NAINODFhMOXmgFpRPBKo"

def is_valid_discord_snowflake(role_id):
    """Check if a role_id is a valid Discord snowflake (numeric, 17+ digits)"""
    if not role_id:
        return False
    role_str = str(role_id)
    return role_str.isdigit() and len(role_str) >= 17

def main():
    print("🧹 Supabase Data Cleanup Script")
    print("=" * 50)
    print("This script will remove teams with invalid role_ids from Supabase.")
    print("Invalid teams are those where team names are stored as role_ids.")
    print()
    
    try:
        # Initialize Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Get all teams from Supabase
        print("📋 Fetching all teams from Supabase...")
        teams = supabase.table('league_teams').select('*').execute()
        
        if not teams.data:
            print("📭 No teams found in Supabase")
            return
        
        print(f"📊 Found {len(teams.data)} teams in Supabase")
        
        invalid_teams = []
        valid_teams = []
        
        # Categorize teams
        for team in teams.data:
            role_id = team.get('role_id')
            team_name = team.get('team_name', 'Unknown')
            
            if is_valid_discord_snowflake(role_id):
                valid_teams.append(team)
            else:
                invalid_teams.append(team)
                print(f"  ❌ Invalid: '{team_name}' (role_id: '{role_id}')")
        
        print(f"\n📊 Analysis Results:")
        print(f"   ✅ Valid teams: {len(valid_teams)}")
        print(f"   ❌ Invalid teams: {len(invalid_teams)}")
        
        if not invalid_teams:
            print("\n🎉 No invalid teams found! Your data is already clean.")
            return
        
        # Confirm deletion
        print(f"\n⚠️ WARNING: About to delete {len(invalid_teams)} invalid teams!")
        print("These teams have non-numeric role_ids (likely team names mistakenly stored as role_ids)")
        
        confirm = input("\nDo you want to proceed with deletion? (yes/no): ").lower().strip()
        if confirm not in ['yes', 'y']:
            print("❌ Operation cancelled")
            return
        
        # Delete invalid teams
        print("\n🗑️ Deleting invalid teams...")
        deleted_count = 0
        
        for team in invalid_teams:
            try:
                # Delete by id if available, otherwise by role_id
                if 'id' in team:
                    supabase.table('league_teams').delete().eq('id', team['id']).execute()
                else:
                    supabase.table('league_teams').delete().eq('role_id', team['role_id']).execute()
                
                deleted_count += 1
                print(f"  🗑️ Deleted: '{team.get('team_name', 'Unknown')}' (role_id: '{team.get('role_id')}')")
                
            except Exception as e:
                print(f"  ❌ Failed to delete team '{team.get('team_name', 'Unknown')}': {e}")
        
        print(f"\n✅ Cleanup completed!")
        print(f"📊 Deleted {deleted_count} invalid teams")
        print(f"📊 {len(valid_teams)} valid teams remain")
        
        # Verify cleanup
        print("\n🔍 Verifying cleanup...")
        updated_teams = supabase.table('league_teams').select('*').execute()
        remaining_invalid = [t for t in updated_teams.data if not is_valid_discord_snowflake(t.get('role_id'))]
        
        if remaining_invalid:
            print(f"⚠️ {len(remaining_invalid)} invalid teams still remain")
        else:
            print("✅ All remaining teams have valid role_ids")
        
        print("\n🔄 You can now restart your bot. The sync should work without errors.")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
