import sys
import os

# Test if we can import the bot modules
try:
    print("Testing bot imports...")
    
    # Try importing SQLite
    import sqlite3
    print("✅ SQLite import successful")
    
    # Test database connection
    if os.path.exists('transaction_bot.db'):
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        
        # Check tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ Found {len(tables)} tables in database")
        
        # Check slot_configs schema specifically
        cursor.execute("PRAGMA table_info(slot_configs)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_columns = ['weekly_games_channel', 'score_channel', 'created_at', 'updated_at']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Missing columns in slot_configs: {missing_columns}")
            
            # Try to add them
            for col in missing_columns:
                col_type = "INTEGER" if "channel" in col else "TEXT"
                try:
                    cursor.execute(f"ALTER TABLE slot_configs ADD COLUMN {col} {col_type}")
                    print(f"✅ Added column {col}")
                except Exception as e:
                    print(f"❌ Failed to add column {col}: {e}")
            
            conn.commit()
        else:
            print("✅ All required columns present in slot_configs")
        
        # Check guild_settings schema
        cursor.execute("PRAGMA table_info(guild_settings)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'transaction_log_channel' not in column_names:
            print("❌ Missing transaction_log_channel in guild_settings")
            try:
                cursor.execute("ALTER TABLE guild_settings ADD COLUMN transaction_log_channel INTEGER")
                conn.commit()
                print("✅ Added transaction_log_channel column")
            except Exception as e:
                print(f"❌ Failed to add transaction_log_channel: {e}")
        else:
            print("✅ transaction_log_channel column present in guild_settings")
        
        conn.close()
        print("✅ Database connection successful")
    else:
        print("❌ Database file not found")
    
    print("\n🚀 Bot should be ready to run!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
