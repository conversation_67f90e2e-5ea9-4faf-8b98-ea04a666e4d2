# Simple test file to check debug function syntax
async def debug():
    try:
        result = "test"
        if result:
            print("test")
        else:
            print("test2")
        
        result2 = "test2"
        if result2:
            print("test3")
        else:
            print("test4")
            
    except Exception as e:
        print(f"Error: {e}")

print("Debug test file created")
