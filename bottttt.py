import discord
from discord import app_commands
from discord.ext import commands
import sqlite3
import traceback
from datetime import datetime
from typing import Optional, <PERSON><PERSON>
import discord
from discord import app_commands
from discord.ext import commands
import sqlite3
import traceback
from datetime import datetime
from typing import Optional, <PERSON><PERSON>


# Initialize the bot with all intents
intents = discord.Intents.all()
bot = commands.Bot(command_prefix='!', intents=intents)

# Now you can use @bot.event
@bot.event
async def on_ready():
    print(f"Logged in as {bot.user.name} (ID: {bot.user.id})")
    
    # Initialize database
    init_db()
    
    # Sync commands with Discord
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")
        traceback.print_exc()# Initialize the bot with all intents
intents = discord.Intents.all()
bot = commands.Bot(command_prefix='!', intents=intents)

# Now you can use @bot.event
@bot.event
async def on_ready():
    print(f"Logged in as {bot.user.name} (ID: {bot.user.id})")
    
    # Initialize database
    init_db()
    
    # Sync commands with Discord
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")
        traceback.print_exc()

# Bot configuration
DISCORD_TOKEN = "MTM1MTY1Mjg4OTkzNTc0NTEwNA.GbAR9z.zrTEjOraY-tkj2pzg6BA5pKBikw0pie0cTZLkE"  # Paste your token between the quotes


# Initialize database with additional lookup tables
def init_db():
    conn = sqlite3.connect('league_config.db')
    cursor = conn.cursor()
    
    # Updated configs table to ensure correct column order
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS configs (
        guild_id INTEGER PRIMARY KEY,
        franchise_owner_role INTEGER,
        gm_role INTEGER,
        hc_role INTEGER,
        ac_role INTEGER,
        streamer_role INTEGER,
        referee_role INTEGER,
        free_agent_role INTEGER,
        transaction_channel INTEGER,
        gametime_channel INTEGER
    )
    ''')
    
    conn.commit()
    conn.close()

# Configuration class with enhanced database integration
class LeagueConfig:
    def __init__(self, guild_id):
        self.guild_id = guild_id
        # Initialize all attributes to None
        self.franchise_owner_role = None
        self.gm_role = None
        self.hc_role = None
        self.ac_role = None
        self.streamer_role = None
        self.referee_role = None
        self.free_agent_role = None
        self.transaction_channel = None
        self.gametime_channel = None
        self.load_from_db()
    
    def load_from_db(self):
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT * FROM configs WHERE guild_id = ?', (self.guild_id,))
            row = cursor.fetchone()
            
            if row:
                # Safely unpack values
                columns = [
                    'guild_id', 
                    'franchise_owner_role', 
                    'gm_role', 
                    'hc_role', 
                    'ac_role', 
                    'streamer_role', 
                    'referee_role', 
                    'free_agent_role',
                    'transaction_channel', 
                    'gametime_channel'
                ]
                
                # Create a dictionary from the row
                config_dict = dict(zip(columns, row))
                
                # Set attributes, converting 0 to None
                self.franchise_owner_role = config_dict['franchise_owner_role'] or None
                self.gm_role = config_dict['gm_role'] or None
                self.hc_role = config_dict['hc_role'] or None
                self.ac_role = config_dict['ac_role'] or None
                self.streamer_role = config_dict['streamer_role'] or None
                self.referee_role = config_dict['referee_role'] or None
                self.free_agent_role = config_dict['free_agent_role'] or None
                self.transaction_channel = config_dict['transaction_channel'] or None
                self.gametime_channel = config_dict['gametime_channel'] or None
            else:
                # If no configuration exists, reset all to None
                self._reset_config()
        
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            self._reset_config()
        
        finally:
            conn.close()
    
    def _reset_config(self):
        """Reset all configuration values to None"""
        self.franchise_owner_role = None
        self.gm_role = None
        self.hc_role = None
        self.ac_role = None
        self.streamer_role = None
        self.referee_role = None
        self.free_agent_role = None
        self.transaction_channel = None
        self.gametime_channel = None
    
    def save_to_db(self):
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        
        try:
            # Explicitly check for None values and replace with 0
            franchise_owner = self.franchise_owner_role or 0
            gm_role = self.gm_role or 0
            hc_role = self.hc_role or 0
            ac_role = self.ac_role or 0
            streamer_role = self.streamer_role or 0
            referee_role = self.referee_role or 0
            free_agent_role = self.free_agent_role or 0
            transaction_channel = self.transaction_channel or 0
            gametime_channel = self.gametime_channel or 0
            
            # Use REPLACE to ensure we always update the existing record
            cursor.execute('''
            REPLACE INTO configs 
            (guild_id, franchise_owner_role, gm_role, hc_role, ac_role, 
             streamer_role, referee_role, free_agent_role, 
             transaction_channel, gametime_channel) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.guild_id, 
                franchise_owner, 
                gm_role, 
                hc_role, 
                ac_role, 
                streamer_role, 
                referee_role, 
                free_agent_role,
                transaction_channel, 
                gametime_channel
            ))
            
            conn.commit()
            print("Configuration saved successfully!")
        except sqlite3.Error as e:
            print(f"Error saving configuration: {e}")
            conn.rollback()
        finally:
            conn.close()

# Create bot with necessary intents
intents = discord.Intents.default()
intents.message_content = True
intents.members = True
bot = commands.Bot(command_prefix='/', intents=intents)

@bot.tree.command(name="setup_coachroles", description="Setup coach and management roles")
@app_commands.default_permissions(administrator=True)
async def setup_coachroles(
    interaction: discord.Interaction, 
    franchise_owner: Optional[discord.Role] = None,
    general_manager: Optional[discord.Role] = None,
    head_coach: Optional[discord.Role] = None,
    assistant_coach: Optional[discord.Role] = None
):
    await interaction.response.defer(ephemeral=True)
    
    try:
        config = LeagueConfig(interaction.guild_id)
        
        # Update roles if provided
        if franchise_owner:
            config.franchise_owner_role = franchise_owner.id
        if general_manager:
            config.gm_role = general_manager.id
        if head_coach:
            config.hc_role = head_coach.id
        if assistant_coach:
            config.ac_role = assistant_coach.id
        
        config.save_to_db()
        
        embed = discord.Embed(
            title="✅ Coach Roles Configured",
            description="Your coach and management roles have been updated!",
            color=discord.Color.green()
        )
        
        for role_name, role in [
            ("Franchise Owner", franchise_owner),
            ("General Manager", general_manager),
            ("Head Coach", head_coach),
            ("Assistant Coach", assistant_coach)
        ]:
            if role:
                embed.add_field(name=role_name, value=role.mention, inline=True)
        
        if not any([franchise_owner, general_manager, head_coach, assistant_coach]):
            embed.description = "No roles were updated. Use the command with role mentions to configure."
        
        await interaction.followup.send(embed=embed, ephemeral=True)
        
    except Exception as e:
        traceback.print_exc()
        await interaction.followup.send(
            "An error occurred while setting up coach roles. Please try again.",
            ephemeral=True
        )
@bot.tree.command(name="setup_other", description="Setup other roles and channels")
@app_commands.default_permissions(administrator=True)
async def setup_other(
    interaction: discord.Interaction,
    streamer: Optional[discord.Role] = None,
    referee: Optional[discord.Role] = None,
    free_agent: Optional[discord.Role] = None,
    transaction_channel: Optional[discord.TextChannel] = None,
    gametime_channel: Optional[discord.TextChannel] = None
):
    # Get the guild configuration
    config = LeagueConfig(interaction.guild_id)
    
    # Update roles and channels if provided
    if streamer:
        config.streamer_role = streamer.id
    if referee:
        config.referee_role = referee.id
    if free_agent:
        config.free_agent_role = free_agent.id
    if transaction_channel:
        config.transaction_channel = transaction_channel.id
    if gametime_channel:
        config.gametime_channel = gametime_channel.id
    
    # Save to database
    config.save_to_db()
    
    # Create embed to confirm setup
    embed = discord.Embed(
        title="✅ Additional Roles and Channels Configured",
        description="Your additional roles and channels have been updated!",
        color=discord.Color.green()
    )
    
    # Add fields only for roles/channels that were set
    if streamer:
        embed.add_field(name="Streamer Role", value=streamer.mention, inline=True)
    if referee:
        embed.add_field(name="Referee Role", value=referee.mention, inline=True)
    if free_agent:
        embed.add_field(name="Free Agent Role", value=free_agent.mention, inline=True)
    if transaction_channel:
        embed.add_field(name="Transaction Channel", value=transaction_channel.mention, inline=True)
    if gametime_channel:
        embed.add_field(name="Game Time Channel", value=gametime_channel.mention, inline=True)
    
    # If no roles/channels were provided, add a note
    if not any([streamer, referee, free_agent, transaction_channel, gametime_channel]):
        embed.description = "No roles or channels were updated. Use the command with mentions to configure."
    
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="settings", description="Show current league configuration")
async def settings(interaction: discord.Interaction):
    try:
        config = LeagueConfig(interaction.guild_id)
        
        embed = discord.Embed(
            title="🏆 Current League Configuration",
            description="Here are the currently configured roles and channels:",
            color=discord.Color.blue()
        )
        
        def get_mention(id_value, guild):
            if not id_value:
                return "Not Set"
            element = guild.get_role(id_value) or guild.get_channel(id_value)
            return element.mention if element else "Not Found"
        
        coach_roles = (
            f"Franchise Owner: {get_mention(config.franchise_owner_role, interaction.guild)}\n"
            f"General Manager: {get_mention(config.gm_role, interaction.guild)}\n"
            f"Head Coach: {get_mention(config.hc_role, interaction.guild)}\n"
            f"Assistant Coach: {get_mention(config.ac_role, interaction.guild)}"
        )
        
        other_config = (
            f"Streamer Role: {get_mention(config.streamer_role, interaction.guild)}\n"
            f"Referee Role: {get_mention(config.referee_role, interaction.guild)}\n"
            f"Free Agent Role: {get_mention(config.free_agent_role, interaction.guild)}\n"
            f"Transaction Channel: {get_mention(config.transaction_channel, interaction.guild)}\n"
            f"Game Time Channel: {get_mention(config.gametime_channel, interaction.guild)}"
        )
        
        embed.add_field(name="📋 Coach & Management Roles", value=coach_roles, inline=False)
        embed.add_field(name="🔧 Additional Roles & Channels", value=other_config, inline=False)
        
        await interaction.response.send_message(embed=embed, ephemeral=True)
        
    except Exception as e:
        traceback.print_exc()
        await interaction.response.send_message(
            "An error occurred while fetching settings. Please try again.",
            ephemeral=True
        )

@bot.tree.command(name="disband", description="Remove team roles from members")
@app_commands.describe(
    team_role="Optional: Specific team role to disband (admin only)",
    league_type="Optional: Specify the league type to disband (admin only)"
)
async def disband(
    interaction: discord.Interaction, 
    team_role: Optional[discord.Role] = None,
    league_type: Optional[str] = None
):
    try:
        await interaction.response.defer(ephemeral=False)
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check permissions first
        member = interaction.guild.get_member(interaction.user.id)
        has_admin_perms = member.guild_permissions.administrator
        
        # Get roles from config
        cursor.execute("""
            SELECT franchise_owner_role, gm_role, league_type 
            FROM configs 
            WHERE guild_id = ?
        """, (str(interaction.guild.id),))
        config = cursor.fetchone()
        
        if not config:
            conn.close()
            return await interaction.followup.send(
                'Server is not set up yet. Please use /setup_coachroles first.',
                ephemeral=True
            )
        
        franchise_owner_role_id, gm_role_id, current_league_type = config
        
        # Check user permissions
        has_fo_role = franchise_owner_role_id and any(role.id == int(franchise_owner_role_id) for role in member.roles)
        has_gm_role = gm_role_id and any(role.id == int(gm_role_id) for role in member.roles)
        
        if not (has_fo_role or has_gm_role or has_admin_perms):
            conn.close()
            return await interaction.followup.send(
                'You need to be a Franchise Owner, General Manager, or have administrator permissions to use this command.',
                ephemeral=True
            )
        
        # If specific team/league type provided, require admin
        if (team_role or league_type) and not has_admin_perms:
            conn.close()
            return await interaction.followup.send(
                'Only administrators can disband a specific team or league type.',
                ephemeral=True
            )
        
        # Get roles to remove from all relevant tables
        role_ids_to_remove = set()
        tables_to_check = ['league_teams', 'teams', 'nfl_teams']
        
        if team_role:
            # Check if role exists in any team table
            role_found = False
            for table in tables_to_check:
                cursor.execute(f"""
                    SELECT role_id FROM {table} 
                    WHERE role_id = ? AND guild_id = ?
                """, (str(team_role.id), str(interaction.guild.id)))
                if cursor.fetchone():
                    role_found = True
                    role_ids_to_remove.add(str(team_role.id))
                    break
            
            if not role_found:
                conn.close()
                return await interaction.followup.send(
                    f'The role {team_role.name} is not registered as a team role.',
                    ephemeral=True
                )
        else:
            # Get all team roles based on league type
            for table in tables_to_check:
                if league_type:
                    cursor.execute(f"""
                        SELECT role_id FROM {table} 
                        WHERE guild_id = ? AND 
                        (LOWER(league_type) = ? OR LOWER(league) = ?)
                    """, (str(interaction.guild.id), league_type.lower(), league_type.lower()))
                else:
                    cursor.execute(f"""
                        SELECT role_id FROM {table} 
                        WHERE guild_id = ?
                    """, (str(interaction.guild.id),))
                
                roles = cursor.fetchall()
                role_ids_to_remove.update(role[0] for role in roles)
        
        if not role_ids_to_remove:
            conn.close()
            return await interaction.followup.send(
                'No team roles found to disband.',
                ephemeral=True
            )
        
        # Remove roles from members
        count = 0
        for member in interaction.guild.members:
            roles_to_remove = []
            for role in member.roles:
                if str(role.id) in role_ids_to_remove:
                    roles_to_remove.append(role)
            
            if roles_to_remove:
                try:
                    await member.remove_roles(*roles_to_remove)
                    count += 1
                except discord.Forbidden:
                    await interaction.followup.send(
                        f"Missing permissions to remove roles from {member.display_name}",
                        ephemeral=True
                    )
        
        # Remove from all database tables
        for table in tables_to_check:
            if team_role:
                cursor.execute(f"""
                    DELETE FROM {table} 
                    WHERE role_id = ? AND guild_id = ?
                """, (str(team_role.id), str(interaction.guild.id)))
            elif league_type:
                cursor.execute(f"""
                    DELETE FROM {table} 
                    WHERE guild_id = ? AND 
                    (LOWER(league_type) = ? OR LOWER(league) = ?)
                """, (str(interaction.guild.id), league_type.lower(), league_type.lower()))
            else:
                cursor.execute(f"""
                    DELETE FROM {table} 
                    WHERE guild_id = ?
                """, (str(interaction.guild.id),))
        
        conn.commit()
        
        # Construct response message
        if team_role:
            msg = f"Successfully removed team role from {count} members. Team {team_role.name} has been disbanded."
        elif league_type:
            msg = f"Successfully removed {league_type} league team roles from {count} members."
        else:
            msg = f"Successfully removed all team roles from {count} members."
        
        await interaction.followup.send(msg, ephemeral=False)
        
    except Exception as error:
        print(f"Error disbanding team: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            'There was an error while disbanding the team. Please try again later.',
            ephemeral=True
        )
    finally:
        try:
            conn.close()
        except:
            pass
@bot.tree.command(name="franchise_owners", description="List all franchise owners and their teams")
async def franchise_owners(interaction: discord.Interaction):
    conn = None
    try:
        # Defer the response to prevent timeout
        await interaction.response.defer(ephemeral=False)
        
        # Initialize connection to database
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        
        # Get franchise owner role from both configs and league_config tables
        cursor.execute('''
            SELECT c.franchise_owner_role, lc.franchise_owner_role 
            FROM configs c 
            LEFT JOIN league_config lc ON c.guild_id = lc.guild_id 
            WHERE c.guild_id = ? OR lc.guild_id = ?
        ''', (str(interaction.guild.id), str(interaction.guild.id)))
        result = cursor.fetchone()
        
        if not result or (not result[0] and not result[1]):
            return await interaction.followup.send(
                'Franchise owner role is not set up. Please use /setup_league or /setup_coachroles first.',
                ephemeral=True
            )
        
        # Use the first non-null role ID found
        franchise_owner_role_id = result[0] if result[0] else result[1]
        
        # Get all teams from both league_teams and teams tables
        teams = []
        
        # Try league_teams first
        cursor.execute("""
            SELECT role_id, team_name, emoji, league_type 
            FROM league_teams 
            WHERE guild_id = ?
        """, (str(interaction.guild.id),))
        teams.extend(cursor.fetchall())
        
        # Then try the teams table
        try:
            cursor.execute("""
                SELECT role_id, team_name, emoji, league 
                FROM teams 
                WHERE guild_id = ?
            """, (str(interaction.guild.id),))
            teams.extend(cursor.fetchall())
        except sqlite3.OperationalError:
            # teams table might not exist
            pass
        
        # Handle empty teams case
        if not teams:
            return await interaction.followup.send(
                'No teams found in the database. Please create teams first using /search_teams, /add_team, or /auto_setup.',
                ephemeral=True
            )
            
        # Get franchise owners and their teams
        try:
            fo_role = interaction.guild.get_role(int(franchise_owner_role_id))
            if not fo_role:
                return await interaction.followup.send(
                    'Franchise owner role not found. The role may have been deleted.',
                    ephemeral=True
                )
        except (ValueError, TypeError):
            return await interaction.followup.send(
                'Invalid franchise owner role ID. Please use /setup_league or /setup_coachroles to set it correctly.',
                ephemeral=True
            )
        
        # Collect owner data first
        owner_data = []
        for member in fo_role.members:
            member_teams = []
            for team in teams:
                try:
                    team_role = interaction.guild.get_role(int(team[0]))
                    if team_role and team_role in member.roles:
                        emoji = team[2] if team[2] else ""
                        team_name = team[1] if team[1] else "Unnamed Team"
                        league_type = team[3] if team[3] else "Generic"
                        member_teams.append(f"{emoji} {team_name} ({league_type})")
                except (ValueError, TypeError):
                    continue
            
            if member_teams:
                owner_data.append((member.display_name, "\n".join(member_teams)))
        
        # Sort owners alphabetically
        owner_data.sort(key=lambda x: x[0].lower())
        
        # Handle case where no owners are found
        if not owner_data:
            embed = discord.Embed(
                title="Franchise Owners",
                description="No franchise owners found with team roles.",
                color=discord.Color.blue(),
                timestamp=datetime.utcnow()
            )
            await interaction.followup.send(embed=embed)
            return
        
        # Create paginated embeds
        embeds = []
        page_size = 25
        total_pages = (len(owner_data) + page_size - 1) // page_size
        
        for page_num in range(total_pages):
            start_idx = page_num * page_size
            end_idx = min(start_idx + page_size, len(owner_data))
            current_page_data = owner_data[start_idx:end_idx]
            
            page_embed = discord.Embed(
                title=f"Franchise Owners (Page {page_num + 1}/{total_pages})",
                description=f"List of franchise owners and their teams ({len(owner_data)} total)",
                color=discord.Color.blue(),
                timestamp=datetime.utcnow()
            )
            
            for name, value in current_page_data:
                page_embed.add_field(name=name, value=value, inline=False)
            
            embeds.append(page_embed)
        
        # Send the first embed with a view for pagination if needed
        if len(embeds) > 1:
            view = PaginationView(embeds)
            await interaction.followup.send(embed=embeds[0], view=view)
        else:
            await interaction.followup.send(embed=embeds[0])
                
    except Exception as error:
        print(f"Error listing franchise owners: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            'There was an error while listing franchise owners. Please try again later.',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()
@bot.tree.command(name="general_managers", description="List all general managers and their teams")
async def general_managers(interaction: discord.Interaction):
    conn = None
    try:
        # Defer the response to prevent timeout
        await interaction.response.defer(ephemeral=False)
        
        # Initialize connection to database
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        
        # Get GM role from both configs and league_config tables
        cursor.execute('''
            SELECT c.gm_role, lc.gm_role 
            FROM configs c 
            LEFT JOIN league_config lc ON c.guild_id = lc.guild_id 
            WHERE c.guild_id = ? OR lc.guild_id = ?
        ''', (str(interaction.guild.id), str(interaction.guild.id)))
        result = cursor.fetchone()
        
        if not result or (not result[0] and not result[1]):
            return await interaction.followup.send(
                'General manager role is not set up. Please use /setup_league or /setup_coachroles first.',
                ephemeral=True
            )
        
        # Use the first non-null role ID found
        gm_role_id = result[0] if result[0] else result[1]
        
        # Get all teams from both league_teams and teams tables
        teams = []
        
        # Try league_teams first
        cursor.execute("""
            SELECT role_id, team_name, emoji, league_type 
            FROM league_teams 
            WHERE guild_id = ?
        """, (str(interaction.guild.id),))
        teams.extend(cursor.fetchall())
        
        # Then try the teams table
        try:
            cursor.execute("""
                SELECT role_id, team_name, emoji, league 
                FROM teams 
                WHERE guild_id = ?
            """, (str(interaction.guild.id),))
            teams.extend(cursor.fetchall())
        except sqlite3.OperationalError:
            # teams table might not exist
            pass
        
        # Handle empty teams case
        if not teams:
            return await interaction.followup.send(
                'No teams found in the database. Please create teams first using /search_teams, /add_team, or /auto_setup.',
                ephemeral=True
            )
            
        # Get GM role from guild
        try:
            gm_role = interaction.guild.get_role(int(gm_role_id))
            if not gm_role:
                return await interaction.followup.send(
                    'General manager role not found. The role may have been deleted.',
                    ephemeral=True
                )
        except (ValueError, TypeError):
            return await interaction.followup.send(
                'Invalid general manager role ID. Please use /setup_league or /setup_coachroles to set it correctly.',
                ephemeral=True
            )
        
        # Collect GM data first
        gm_data = []
        for member in gm_role.members:
            member_teams = []
            for team in teams:
                try:
                    team_role = interaction.guild.get_role(int(team[0]))
                    if team_role and team_role in member.roles:
                        emoji = team[2] if team[2] else ""
                        team_name = team[1] if team[1] else "Unnamed Team"
                        league_type = team[3] if team[3] else "Generic"
                        member_teams.append(f"{emoji} {team_name} ({league_type})")
                except (ValueError, TypeError):
                    continue
            
            if member_teams:
                gm_data.append((member.display_name, "\n".join(member_teams)))
        
        # Sort GMs alphabetically
        gm_data.sort(key=lambda x: x[0].lower())
        
        # Handle case where no GMs are found
        if not gm_data:
            embed = discord.Embed(
                title="General Managers",
                description="No general managers found with team roles.",
                color=discord.Color.green(),
                timestamp=datetime.utcnow()
            )
            await interaction.followup.send(embed=embed)
            return
        
        # Create paginated embeds
        embeds = []
        page_size = 25
        total_pages = (len(gm_data) + page_size - 1) // page_size
        
        for page_num in range(total_pages):
            start_idx = page_num * page_size
            end_idx = min(start_idx + page_size, len(gm_data))
            current_page_data = gm_data[start_idx:end_idx]
            
            page_embed = discord.Embed(
                title=f"General Managers (Page {page_num + 1}/{total_pages})",
                description=f"List of general managers and their teams ({len(gm_data)} total)",
                color=discord.Color.green(),
                timestamp=datetime.utcnow()
            )
            
            for name, value in current_page_data:
                page_embed.add_field(name=name, value=value, inline=False)
            
            embeds.append(page_embed)
        
        # Send the first embed with a view for pagination if needed
        if len(embeds) > 1:
            view = PaginationView(embeds)
            await interaction.followup.send(embed=embeds[0], view=view)
        else:
            await interaction.followup.send(embed=embeds[0])
                
    except Exception as error:
        print(f"Error listing general managers: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            'There was an error while listing general managers. Please try again later.',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()

# Pagination view for multiple pages of content
class PaginationView(discord.ui.View):
    def __init__(self, embeds):
        super().__init__(timeout=180)
        self.embeds = embeds
        self.current_page = 0
        self.total_pages = len(embeds)
        
        # Update button states based on initial page
        self.update_buttons()
    
    def update_buttons(self):
        # Get the buttons by their custom_id
        prev_button = discord.utils.get(self.children, custom_id="prev_page")
        next_button = discord.utils.get(self.children, custom_id="next_page")
        
        # Update disabled state
        if prev_button:
            prev_button.disabled = self.current_page == 0
        if next_button:
            next_button.disabled = self.current_page == self.total_pages - 1
    
    @discord.ui.button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page")
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_page = max(0, self.current_page - 1)
        self.update_buttons()
        await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
    
    @discord.ui.button(label="Next", style=discord.ButtonStyle.secondary, custom_id="next_page")
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_page = min(self.total_pages - 1, self.current_page + 1)
        self.update_buttons()
        await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)

@bot.tree.command(name="add_team", description="Add a new team to the server")
@app_commands.describe(
    team_role="The role for the team",
    team_name="The name of the team",
    emoji="The emoji for the team"
)
async def add_team(interaction: discord.Interaction, team_role: discord.Role, team_name: str, emoji: str):
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if team already exists with this role in this guild
        cursor.execute('''
        SELECT team_name FROM league_teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        existing_team = cursor.fetchone()
        if existing_team:
            return await interaction.followup.send(
                f'This role is already assigned to team "{existing_team[0]}". Please use a different role.',
                ephemeral=True
            )
        
        # Create the unified teams table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS teams (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            role_id TEXT NOT NULL,
            guild_id TEXT NOT NULL,
            team_name TEXT NOT NULL,
            emoji TEXT NOT NULL,
            sport TEXT,
            league TEXT,
            conference TEXT,
            division TEXT,
            UNIQUE(role_id, guild_id)
        )
        ''')
        
        # Insert new team into both tables
        cursor.execute('''
        INSERT INTO league_teams (role_id, guild_id, team_name, emoji) 
        VALUES (?, ?, ?, ?)
        ''', (str(team_role.id), str(interaction.guild.id), team_name, emoji))
        
        cursor.execute('''
        INSERT INTO teams (role_id, guild_id, team_name, emoji) 
        VALUES (?, ?, ?, ?)
        ''', (str(team_role.id), str(interaction.guild.id), team_name, emoji))
        
        conn.commit()
        
        # Use followup instead of response since we deferred
        await interaction.followup.send(
            f'Team {team_name} {emoji} has been added successfully!',
            ephemeral=True
        )
    except sqlite3.IntegrityError as integrity_error:
        print(f"Database integrity error adding team: {integrity_error}")
        await interaction.followup.send(
            f'This role or team name is already in use. Please try again with a different role or team name.',
            ephemeral=True
        )
    except Exception as error:
        print(f"Error adding team: {error}")
        traceback.print_exc()  # Added traceback for better debugging
        await interaction.followup.send(
            f'There was an error adding the team: {error}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()
@bot.tree.command(name="sign", description="Sign a player to your team")
@app_commands.describe(
    player="The player to sign", 
    team="The team you are signing the player to"
)
async def sign(
    interaction: discord.Interaction, 
    player: discord.User, 
    team: discord.Role
):
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        
        # Get the member objects
        member = interaction.guild.get_member(interaction.user.id)
        player_member = interaction.guild.get_member(player.id)
        
        if not member or not player_member:
            return await interaction.followup.send(
                'Could not find member information. Please try again.',
                ephemeral=True
            )
        
        # Get configured roles from both configs and league_config tables
        cursor.execute('''
            SELECT 
                COALESCE(c.franchise_owner_role, lc.franchise_owner_role) as franchise_owner_role,
                COALESCE(c.gm_role, lc.gm_role) as gm_role,
                COALESCE(c.hc_role, lc.hc_role) as hc_role,
                COALESCE(c.ac_role, lc.ac_role) as ac_role,
                COALESCE(c.free_agent_role, lc.free_agent_role) as free_agent_role
            FROM configs c
            LEFT JOIN league_config lc ON c.guild_id = lc.guild_id
            WHERE c.guild_id = ? OR lc.guild_id = ?
        ''', (str(interaction.guild.id), str(interaction.guild.id)))
        
        roles = cursor.fetchone()
        
        if not roles:
            return await interaction.followup.send(
                'League roles are not configured. Please use /setup_league first.',
                ephemeral=True
            )
        
        franchise_owner_role_id, gm_role_id, hc_role_id, ac_role_id, free_agent_role_id = roles
        
        # Check if user has permission
        has_permission = False
        if member.guild_permissions.administrator:
            has_permission = True
        else:
            allowed_role_ids = [
                franchise_owner_role_id,
                gm_role_id,
                hc_role_id,
                ac_role_id
            ]
            allowed_role_ids = [str(role_id) for role_id in allowed_role_ids if role_id]
            
            for role in member.roles:
                if str(role.id) in allowed_role_ids:
                    has_permission = True
                    break
        
        if not has_permission:
            return await interaction.followup.send(
                'You do not have permission to sign players.',
                ephemeral=True
            )
        
        # Check if player has free agent role
        if not free_agent_role_id:
            return await interaction.followup.send(
                'Free agent role is not configured. Please use /setup_league first.',
                ephemeral=True
            )
        
        free_agent_role = interaction.guild.get_role(int(free_agent_role_id))
        if not free_agent_role or free_agent_role not in player_member.roles:
            return await interaction.followup.send(
                f'{player.display_name} is not a free agent.',
                ephemeral=True
            )
        
        # Check if team exists in the teams table
        cursor.execute('''
            SELECT team_name, emoji, sport, conference, division
            FROM teams 
            WHERE role_id = ? AND guild_id = ?
        ''', (str(team.id), str(interaction.guild.id)))
        
        team_info = cursor.fetchone()
        if not team_info:
            return await interaction.followup.send(
                f'The team {team.name} is not registered in the database. Please add it first using /search_teams.',
                ephemeral=True
            )
        
        # Check if player is already on a team
        cursor.execute('''
            SELECT t.team_name 
            FROM teams t
            INNER JOIN discord_roles dr ON t.role_id = dr.role_id
            WHERE dr.member_id = ? AND t.guild_id = ?
        ''', (str(player.id), str(interaction.guild.id)))
        
        if cursor.fetchone():
            return await interaction.followup.send(
                f'{player.display_name} is already on a team.',
                ephemeral=True
            )
        
        # Create signing embed
        current_time = datetime.utcnow()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S UTC")
        
        sign_embed = discord.Embed(
            title=f"Player Transaction",
            description=f"📝 {player.mention} has been signed to {team_info[1]} {team_info[0]}",
            color=discord.Color.green(),
            timestamp=current_time
        )
        
        # Add team details
        team_details = []
        if team_info[2]:  # sport
            team_details.append(f"Sport: {team_info[2]}")
        if team_info[3]:  # conference
            team_details.append(f"Conference: {team_info[3]}")
        if team_info[4]:  # division
            team_details.append(f"Division: {team_info[4]}")
        
        if team_details:
            sign_embed.add_field(
                name="Team Details",
                value="\n".join(team_details),
                inline=False
            )
        
        sign_embed.set_footer(
            text=f"Signed by {interaction.user.display_name} | {formatted_time}",
            icon_url=interaction.user.display_avatar.url
        )
        
        # Execute the transaction
        try:
            # Remove free agent role
            await player_member.remove_roles(free_agent_role)
            # Add team role
            await player_member.add_roles(team)
            
            # Log transaction
            cursor.execute('''
                INSERT INTO transactions (
                    guild_id, player_id, team_id, transaction_type,
                    executor_id, timestamp, additional_details
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                str(interaction.guild.id),
                str(player.id),
                str(team.id),
                'sign',
                str(interaction.user.id),
                formatted_time,
                f"Team: {team_info[0]}, Sport: {team_info[2] or 'Unknown'}"
            ))
            
            conn.commit()
            
            # Send to transaction channel if configured
            cursor.execute('''
                SELECT transaction_channel 
                FROM configs 
                WHERE guild_id = ?
            ''', (str(interaction.guild.id),))
            
            channel_id = cursor.fetchone()
            if channel_id and channel_id[0]:
                try:
                    channel = interaction.guild.get_channel(int(channel_id[0]))
                    if channel:
                        await channel.send(embed=sign_embed)
                except Exception as e:
                    print(f"Error sending to transaction channel: {e}")
            
            await interaction.followup.send(
                f"Successfully signed {player.display_name} to {team_info[1]} {team_info[0]}!",
                ephemeral=False
            )
            
        except Exception as e:
            await interaction.followup.send(
                f"Error executing transaction: {str(e)}",
                ephemeral=True
            )
            
    except Exception as error:
        print(f"Error in sign command: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error processing the command: {str(error)}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()
@bot.tree.command(name="release", description="Release a player from your team")
@app_commands.describe(
    player="The player to release"
)
async def release(
    interaction: discord.Interaction, 
    player: discord.User
):
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        
        # Get the member objects
        member = interaction.guild.get_member(interaction.user.id)
        player_member = interaction.guild.get_member(player.id)
        
        if not member or not player_member:
            return await interaction.followup.send(
                'Could not find member information. Please try again.',
                ephemeral=True
            )
        
        # Get configured roles from both configs and league_config tables
        cursor.execute('''
            SELECT 
                COALESCE(c.franchise_owner_role, lc.franchise_owner_role) as franchise_owner_role,
                COALESCE(c.gm_role, lc.gm_role) as gm_role,
                COALESCE(c.hc_role, lc.hc_role) as hc_role,
                COALESCE(c.ac_role, lc.ac_role) as ac_role,
                COALESCE(c.free_agent_role, lc.free_agent_role) as free_agent_role
            FROM configs c
            LEFT JOIN league_config lc ON c.guild_id = lc.guild_id
            WHERE c.guild_id = ? OR lc.guild_id = ?
        ''', (str(interaction.guild.id), str(interaction.guild.id)))
        
        roles = cursor.fetchone()
        
        if not roles:
            return await interaction.followup.send(
                'League roles are not configured. Please use /setup_league first.',
                ephemeral=True
            )
        
        franchise_owner_role_id, gm_role_id, hc_role_id, ac_role_id, free_agent_role_id = roles
        
        # Check if user has permission
        has_permission = False
        if member.guild_permissions.administrator:
            has_permission = True
        else:
            allowed_role_ids = [
                franchise_owner_role_id,
                gm_role_id,
                hc_role_id,
                ac_role_id
            ]
            allowed_role_ids = [str(role_id) for role_id in allowed_role_ids if role_id]
            
            for role in member.roles:
                if str(role.id) in allowed_role_ids:
                    has_permission = True
                    break
        
        if not has_permission:
            return await interaction.followup.send(
                'You do not have permission to release players.',
                ephemeral=True
            )

        # Find the team role of the user executing the command
        user_team_role = None
        cursor.execute('''
            SELECT role_id, team_name, emoji, sport, conference, division
            FROM teams 
            WHERE guild_id = ?
        ''', (str(interaction.guild.id),))
        
        team_roles = cursor.fetchall()
        for team_role_data in team_roles:
            role = interaction.guild.get_role(int(team_role_data[0]))
            if role and role in member.roles:
                user_team_role = role
                team_info = team_role_data
                break

        if not user_team_role:
            return await interaction.followup.send(
                'You must be part of a team to release players.',
                ephemeral=True
            )

        # Check if player is on the user's team
        if user_team_role not in player_member.roles:
            return await interaction.followup.send(
                f'{player.display_name} is not on your team.',
                ephemeral=True
            )
        
        # Create release embed
        current_time = datetime.utcnow()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S UTC")
        
        release_embed = discord.Embed(
            title=f"Player Transaction",
            description=f"📝 {player.mention} has been released from {team_info[2]} {team_info[1]}",
            color=discord.Color.red(),
            timestamp=current_time
        )
        
        # Add team details
        team_details = []
        if team_info[3]:  # sport
            team_details.append(f"Sport: {team_info[3]}")
        if team_info[4]:  # conference
            team_details.append(f"Conference: {team_info[4]}")
        if team_info[5]:  # division
            team_details.append(f"Division: {team_info[5]}")
        
        if team_details:
            release_embed.add_field(
                name="Team Details",
                value="\n".join(team_details),
                inline=False
            )
        
        release_embed.set_footer(
            text=f"Released by {interaction.user.display_name} | {formatted_time}",
            icon_url=interaction.user.display_avatar.url
        )
        
        # Execute the transaction
        try:
            # Remove team role
            await player_member.remove_roles(user_team_role)
            # Add free agent role
            free_agent_role = interaction.guild.get_role(int(free_agent_role_id))
            if free_agent_role:
                await player_member.add_roles(free_agent_role)
            
            # Log transaction
            cursor.execute('''
                INSERT INTO transactions (
                    guild_id, player_id, team_id, transaction_type,
                    executor_id, timestamp, additional_details
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                str(interaction.guild.id),
                str(player.id),
                str(user_team_role.id),
                'release',
                str(interaction.user.id),
                formatted_time,
                f"Team: {team_info[1]}, Sport: {team_info[3] or 'Unknown'}"
            ))
            
            conn.commit()
            
            # Send to transaction channel if configured
            cursor.execute('''
                SELECT transaction_channel 
                FROM configs 
                WHERE guild_id = ?
            ''', (str(interaction.guild.id),))
            
            channel_id = cursor.fetchone()
            if channel_id and channel_id[0]:
                try:
                    channel = interaction.guild.get_channel(int(channel_id[0]))
                    if channel:
                        await channel.send(embed=release_embed)
                except Exception as e:
                    print(f"Error sending to transaction channel: {e}")
            
            await interaction.followup.send(
                f"Successfully released {player.display_name} from {team_info[2]} {team_info[1]}!",
                ephemeral=False
            )
            
        except Exception as e:
            await interaction.followup.send(
                f"Error executing transaction: {str(e)}",
                ephemeral=True
            )
            
    except Exception as error:
        print(f"Error in release command: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error processing the command: {str(error)}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()


import discord
from discord import app_commands
from discord.ext import commands
import sqlite3
import traceback

# Assuming these are defined elsewhere in your main bot setup
# Make sure this is properly set up in your main bot initialization
bot = commands.Bot(command_prefix='!', intents=discord.Intents.all())
DB_FILE = 'league_database.db'

class LeagueConfig:
    def __init__(self, guild_id):
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Comprehensive league configuration table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS league_config (
            guild_id TEXT PRIMARY KEY,
            league_type TEXT DEFAULT 'generic',
            franchise_owner_role TEXT,
            gm_role TEXT,
            transaction_channel TEXT
        )''')
        
        # Ensure Teams table exists with comprehensive schema
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS Teams (
            role_id TEXT PRIMARY KEY,
            guild_id TEXT,
            team_name TEXT NOT NULL,
            league_type TEXT,
            sport TEXT,
            emoji TEXT,
            conference TEXT,
            division TEXT,
            additional_info TEXT
        )''')
        
        # Fetch configuration
        cursor.execute("""
        SELECT 
            franchise_owner_role, 
            gm_role, 
            transaction_channel 
        FROM league_config 
        WHERE guild_id = ?
        """, (str(guild_id),))
        result = cursor.fetchone()
        
        # Safely convert to int, handling None cases
        self.franchise_owner_role = int(result[0]) if result and result[0] else None
        self.gm_role = int(result[1]) if result and result[1] else None
        self.transaction_channel = int(result[2]) if result and result[2] else None
        
        conn.close()

import discord
from discord import app_commands
from discord.ext import commands
import sqlite3
import traceback

# Assuming these are defined elsewhere in your main bot setup
# Make sure this is properly set up in your main bot initialization
bot = commands.Bot(command_prefix='!', intents=discord.Intents.all())
DB_FILE = 'league_database.db'

class LeagueConfig:
    def __init__(self, guild_id):
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Comprehensive league configuration table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS league_config (
            guild_id TEXT PRIMARY KEY,
            league_type TEXT DEFAULT 'generic',
            franchise_owner_role TEXT,
            gm_role TEXT,
            transaction_channel TEXT
        )''')
        
        # Ensure Teams table exists with comprehensive schema
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS Teams (
            role_id TEXT PRIMARY KEY,
            guild_id TEXT,
            team_name TEXT NOT NULL,
            league_type TEXT,
            sport TEXT,
            emoji TEXT,
            conference TEXT,
            division TEXT,
            additional_info TEXT
        )''')
        
        # Fetch configuration
        cursor.execute("""
        SELECT 
            franchise_owner_role, 
            gm_role, 
            transaction_channel 
        FROM league_config 
        WHERE guild_id = ?
        """, (str(guild_id),))
        result = cursor.fetchone()
        
        # Safely convert to int, handling None cases
        self.franchise_owner_role = int(result[0]) if result and result[0] else None
        self.gm_role = int(result[1]) if result and result[1] else None
        self.transaction_channel = int(result[2]) if result and result[2] else None
        
        conn.close()


@bot.tree.command(name="search_teams", description="Comprehensive team role search and management")
@app_commands.checks.has_permissions(administrator=True)
async def search_teams(interaction: discord.Interaction):
    """
    Advanced team search and management command
    - Searches for team roles in the server
    - Identifies existing and new team roles
    - Associates teams with configuration
    - Provides detailed embed with team information
    """
    # Defer response to handle potentially long processing time
    await interaction.response.defer(ephemeral=True)
    
    # Database connection
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    try:
        # Prepare an embed to display team information
        team_embed = discord.Embed(
            title="📋 Server Team Roles Search",
            description="Comprehensive search of team roles in the server",
            color=discord.Color.blue()
        )
        
        # Fetch all roles in the guild
        guild_roles = interaction.guild.roles
        
        # Track found teams
        found_teams = []
        
        # Comprehensive predefined teams dictionary
        predefined_teams = {
            "football": [
                {"name": "Arizona Cardinals", "league": "NFL", "conference": "NFC", "division": "West", "emoji": "🏈"},
                {"name": "Atlanta Falcons", "league": "NFL", "conference": "NFC", "division": "South", "emoji": "🏈"},
                {"name": "Baltimore Ravens", "league": "NFL", "conference": "AFC", "division": "North", "emoji": "🏈"},
                {"name": "Buffalo Bills", "league": "NFL", "conference": "AFC", "division": "East", "emoji": "🏈"},
                {"name": "Carolina Panthers", "league": "NFL", "conference": "NFC", "division": "South", "emoji": "🏈"},
                {"name": "Chicago Bears", "league": "NFL", "conference": "NFC", "division": "North", "emoji": "🏈"},
                {"name": "Cincinnati Bengals", "league": "NFL", "conference": "AFC", "division": "North", "emoji": "🏈"},
                {"name": "Cleveland Browns", "league": "NFL", "conference": "AFC", "division": "North", "emoji": "🏈"},
                {"name": "Dallas Cowboys", "league": "NFL", "conference": "NFC", "division": "East", "emoji": "🏈"},
                {"name": "Denver Broncos", "league": "NFL", "conference": "AFC", "division": "West", "emoji": "🏈"},
                {"name": "Detroit Lions", "league": "NFL", "conference": "NFC", "division": "North", "emoji": "🏈"},
                {"name": "Green Bay Packers", "league": "NFL", "conference": "NFC", "division": "North", "emoji": "🏈"},
                {"name": "Houston Texans", "league": "NFL", "conference": "AFC", "division": "South", "emoji": "🏈"},
                {"name": "Indianapolis Colts", "league": "NFL", "conference": "AFC", "division": "South", "emoji": "🏈"},
                {"name": "Jacksonville Jaguars", "league": "NFL", "conference": "AFC", "division": "South", "emoji": "🏈"},
                {"name": "Kansas City Chiefs", "league": "NFL", "conference": "AFC", "division": "West", "emoji": "🏈"},
                {"name": "Las Vegas Raiders", "league": "NFL", "conference": "AFC", "division": "West", "emoji": "🏈"},
                {"name": "Los Angeles Chargers", "league": "NFL", "conference": "AFC", "division": "West", "emoji": "🏈"},
                {"name": "Los Angeles Rams", "league": "NFL", "conference": "NFC", "division": "West", "emoji": "🏈"},
                {"name": "Miami Dolphins", "league": "NFL", "conference": "AFC", "division": "East", "emoji": "🏈"},
                {"name": "Minnesota Vikings", "league": "NFL", "conference": "NFC", "division": "North", "emoji": "🏈"},
                {"name": "New England Patriots", "league": "NFL", "conference": "AFC", "division": "East", "emoji": "🏈"},
                {"name": "New Orleans Saints", "league": "NFL", "conference": "NFC", "division": "South", "emoji": "🏈"},
                {"name": "New York Giants", "league": "NFL", "conference": "NFC", "division": "East", "emoji": "🏈"},
                {"name": "New York Jets", "league": "NFL", "conference": "AFC", "division": "East", "emoji": "🏈"},
                {"name": "Philadelphia Eagles", "league": "NFL", "conference": "NFC", "division": "East", "emoji": "🏈"},
                {"name": "Pittsburgh Steelers", "league": "NFL", "conference": "AFC", "division": "North", "emoji": "🏈"},
                {"name": "San Francisco 49ers", "league": "NFL", "conference": "NFC", "division": "West", "emoji": "🏈"},
                {"name": "Seattle Seahawks", "league": "NFL", "conference": "NFC", "division": "West", "emoji": "🏈"},
                {"name": "Tampa Bay Buccaneers", "league": "NFL", "conference": "NFC", "division": "South", "emoji": "🏈"},
                {"name": "Tennessee Titans", "league": "NFL", "conference": "AFC", "division": "South", "emoji": "🏈"},
                {"name": "Washington Commanders", "league": "NFL", "conference": "NFC", "division": "East", "emoji": "🏈"}
            ]
        }

        # Search for potential team roles
        for role in guild_roles:
            # Skip default roles and very generic roles
            if role.name.lower() in ['@everyone', 'admin', 'moderator', 'bot']:
                continue
            
            # Check if role matches any predefined team
            team_match = None
            for sport, teams in predefined_teams.items():
                for team in teams:
                    if role.name.lower() == team["name"].lower():
                        team_match = team
                        break
                if team_match:
                    break
            
            # Check if role is already in Teams database
            cursor.execute("""
            SELECT team_name, league_type, sport, emoji, conference, division 
            FROM Teams 
            WHERE role_id = ? AND guild_id = ?
            """, (str(role.id), str(interaction.guild.id)))
            
            team_info = cursor.fetchone()
            
            # Prepare team details
            team_details = {
                'role': role,
                'registered': bool(team_info),
                'name': team_info[0] if team_info else (team_match["name"] if team_match else role.name),
                'league_type': team_info[1] if team_info else (team_match["league"] if team_match else 'Unspecified'),
                'sport': team_info[2] if team_info else (sport if team_match else 'Unknown'),
                'emoji': team_info[3] if team_info else (team_match["emoji"] if team_match else '🏈'),
                'conference': team_info[4] if team_info else (team_match["conference"] if team_match else 'N/A'),
                'division': team_info[5] if team_info else (team_match["division"] if team_match else 'N/A')
            }
            
            found_teams.append(team_details)
        
        # Sort teams by registration status and name
        found_teams.sort(key=lambda x: (not x['registered'], x['name']))
        
        # Add teams to embed
        for team in found_teams:
            status = "✅ Registered" if team['registered'] else "❌ Unregistered"
            team_embed.add_field(
                name=f"{team['emoji']} {team['name']} {status}",
                value=(
                    f"**League:** {team['league_type']}\n"
                    f"**Sport:** {team['sport']}\n"
                    f"**Conference:** {team['conference']}\n"
                    f"**Division:** {team['division']}"
                ),
                inline=False
            )
        
        # Send the embed
        await interaction.followup.send(embed=team_embed, ephemeral=True)
    
    except Exception as error:
        print(f"Error in search_teams: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'An error occurred while searching teams: {error}',
            ephemeral=True
        )
    
    finally:
        conn.close()

# Error handler for command permission errors
@search_teams.error
async def search_teams_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    if isinstance(error, app_commands.MissingPermissions):
        await interaction.response.send_message(
            "You need administrator permissions to use this command.",
            ephemeral=True
        )


@bot.tree.command(name="schedule", description="Schedule a game with an opponent")
@app_commands.describe(
    opponent_team="Mention the opponent team role",
    time="Enter the time (e.g., 8:00)",
    am_or_pm="Select AM or PM",
    day="Select the day for the game",
    timezone="Select the timezone for the game"
)
@app_commands.choices(
    am_or_pm=[
        app_commands.Choice(name="AM", value="AM"),
        app_commands.Choice(name="PM", value="PM")
    ],
    day=[
        app_commands.Choice(name="Today", value="today"),
        app_commands.Choice(name="Tomorrow", value="tomorrow"),
        app_commands.Choice(name="In 2 days", value="in_2_days"),
        app_commands.Choice(name="In 3 days", value="in_3_days")
    ],
    timezone=[
        app_commands.Choice(name="Eastern", value="US/Eastern"),
        app_commands.Choice(name="Central", value="US/Central"),
        app_commands.Choice(name="Mountain", value="US/Mountain"),
        app_commands.Choice(name="Pacific", value="US/Pacific")
    ]
)
async def schedule(
    interaction: Interaction, 
    opponent_team: discord.Role, 
    time: str, 
    am_or_pm: app_commands.Choice[str],
    day: app_commands.Choice[str],
    timezone: app_commands.Choice[str]
):
    conn = None
    try:
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        
        # Get configured roles from both configs and league_config tables
        cursor.execute('''
            SELECT 
                COALESCE(c.franchise_owner_role, lc.franchise_owner_role) as franchise_owner_role,
                COALESCE(c.gm_role, lc.gm_role) as gm_role,
                COALESCE(c.hc_role, lc.hc_role) as hc_role,
                COALESCE(c.ac_role, lc.ac_role) as ac_role,
                COALESCE(c.referee_role, lc.referee_role) as referee_role,
                COALESCE(c.streamer_role, lc.streamer_role) as streamer_role,
                COALESCE(c.gametime_channel, lc.gametime_channel) as gametime_channel
            FROM configs c
            LEFT JOIN league_config lc ON c.guild_id = lc.guild_id
            WHERE c.guild_id = ? OR lc.guild_id = ?
        ''', (str(interaction.guild.id), str(interaction.guild.id)))
        
        config = cursor.fetchone()
        
        if not config:
            return await interaction.response.send_message(
                "Server not configured. Please use /setup first.",
                ephemeral=True
            )

        franchise_owner_role, gm_role, hc_role, ac_role, referee_role, streamer_role, gametime_channel = config
        
        # Check if user has permission
        has_permission = False
        if interaction.user.guild_permissions.administrator:
            has_permission = True
        else:
            allowed_role_ids = [
                franchise_owner_role,
                gm_role,
                hc_role,
                ac_role
            ]
            allowed_role_ids = [str(role_id) for role_id in allowed_role_ids if role_id]
            
            for role in interaction.user.roles:
                if str(role.id) in allowed_role_ids:
                    has_permission = True
                    break

        if not has_permission:
            return await interaction.response.send_message(
                "You don't have permission to schedule games.",
                ephemeral=True
            )

        # Find user's team
        user_team_role = None
        user_team_info = None
        cursor.execute('''
            SELECT role_id, team_name, emoji, sport, conference, division
            FROM teams 
            WHERE guild_id = ?
        ''', (str(interaction.guild.id),))
        
        teams = cursor.fetchall()
        user_roles = [str(role.id) for role in interaction.user.roles]
        
        for team in teams:
            if str(team[0]) in user_roles:
                user_team_role = interaction.guild.get_role(int(team[0]))
                user_team_info = {
                    "name": team[1],
                    "emoji": team[2],
                    "sport": team[3],
                    "conference": team[4],
                    "division": team[5]
                }
                break

        if not user_team_role or not user_team_info:
            return await interaction.response.send_message(
                "You don't have a team role assigned.",
                ephemeral=True
            )

        # Get opponent team info
        cursor.execute('''
            SELECT team_name, emoji, sport, conference, division
            FROM teams 
            WHERE role_id = ? AND guild_id = ?
        ''', (str(opponent_team.id), str(interaction.guild.id)))
        
        opponent_info = cursor.fetchone()
        if not opponent_info:
            return await interaction.response.send_message(
                "The mentioned role is not a valid team.",
                ephemeral=True
            )

        # Parse time and create game datetime
        try:
            hour, minute = map(int, time.split(':'))
            
            if am_or_pm.value == "PM" and hour != 12:
                hour += 12
            elif am_or_pm.value == "AM" and hour == 12:
                hour = 0
            
            days_mapping = {
                "today": 0,
                "tomorrow": 1,
                "in_2_days": 2,
                "in_3_days": 3
            }
            
            today = datetime.now()
            game_date = today + timedelta(days=days_mapping[day.value])
            game_datetime = game_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            user_timezone = pytz.timezone(timezone.value)
            game_datetime = user_timezone.localize(game_datetime)
            
            # Format times for all timezones
            timezones = ["US/Eastern", "US/Central", "US/Mountain", "US/Pacific"]
            time_display = ""
            for tz in timezones:
                tz_datetime = game_datetime.astimezone(pytz.timezone(tz))
                time_display += f"{tz_datetime.strftime('%I:%M %p')} {tz.split('/')[-1]}\n"
            
            # Calculate countdown
            now = datetime.now(pytz.utc)
            delta = game_datetime.astimezone(pytz.utc) - now
            days = delta.days
            hours, remainder = divmod(delta.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            countdown = f"{days}d {hours}h {minutes}m"

            # Get game channel
            if not gametime_channel:
                return await interaction.response.send_message(
                    "Game time channel not configured.",
                    ephemeral=True
                )
            
            game_channel = interaction.guild.get_channel(int(gametime_channel))
            if not game_channel:
                return await interaction.response.send_message(
                    "Game time channel not found.",
                    ephemeral=True
                )

            # Create the message
            message_content = (
                f"**{interaction.guild.name}**\n"
                f"{user_team_info['emoji']} {user_team_role.mention} vs {opponent_info[1]} {opponent_team.mention}\n"
                f":clock: {time_display}:stopwatch: {countdown}\n"
                f"🕶 No referees assigned\n"
                f"🎥 No streamer assigned"
            )

            class GameView(View):
                def __init__(self):
                    super().__init__(timeout=None)
                    self.referees = []
                    self.streamer = None

                @discord.ui.button(label="Referee", style=ButtonStyle.primary)
                async def referee_button(self, ref_interaction: Interaction, button: Button):
                    if not referee_role or str(referee_role) not in [str(role.id) for role in ref_interaction.user.roles]:
                        await ref_interaction.response.send_message(
                            "You don't have the referee role.",
                            ephemeral=True
                        )
                        return
                    
                    if ref_interaction.user.id in self.referees:
                        self.referees.remove(ref_interaction.user.id)
                        ref_text = "🕶 No referees assigned" if not self.referees else f"🕶 {' '.join([f'<@{ref_id}>' for ref_id in self.referees])}"
                        message_lines = ref_interaction.message.content.split('\n')
                        for i, line in enumerate(message_lines):
                            if "🕶" in line:
                                message_lines[i] = ref_text
                        await ref_interaction.message.edit(content='\n'.join(message_lines))
                        await ref_interaction.response.send_message(
                            "You've been removed as a referee.",
                            ephemeral=True
                        )
                    else:
                        if len(self.referees) < 2:
                            self.referees.append(ref_interaction.user.id)
                            ref_mentions = " ".join([f"<@{ref_id}>" for ref_id in self.referees])
                            ref_text = f"🕶 {ref_mentions}"
                            message_lines = ref_interaction.message.content.split('\n')
                            for i, line in enumerate(message_lines):
                                if "🕶" in line:
                                    message_lines[i] = ref_text
                            await ref_interaction.message.edit(content='\n'.join(message_lines))
                            await ref_interaction.response.send_message(
                                "You've been assigned as a referee.",
                                ephemeral=True
                            )
                        else:
                            await ref_interaction.response.send_message(
                                "There are already 2 referees assigned.",
                                ephemeral=True
                            )

                @discord.ui.button(label="Streamer", style=ButtonStyle.primary)
                async def streamer_button(self, stream_interaction: Interaction, button: Button):
                    if not streamer_role or str(streamer_role) not in [str(role.id) for role in stream_interaction.user.roles]:
                        await stream_interaction.response.send_message(
                            "You don't have the streamer role.",
                            ephemeral=True
                        )
                        return
                    
                    if self.streamer == stream_interaction.user.id:
                        self.streamer = None
                        streamer_text = "🎥 No streamer assigned"
                    else:
                        self.streamer = stream_interaction.user.id
                        streamer_text = f"🎥 <@{self.streamer}>"
                    
                    message_lines = stream_interaction.message.content.split('\n')
                    for i, line in enumerate(message_lines):
                        if "🎥" in line:
                            message_lines[i] = streamer_text
                    await stream_interaction.message.edit(content='\n'.join(message_lines))
                    await stream_interaction.response.send_message(
                        "You've been " + ("removed as" if self.streamer is None else "assigned as") + " the streamer.",
                        ephemeral=True
                    )

            game_view = GameView()
            await game_channel.send(content=message_content, view=game_view)
            await interaction.response.send_message(
                f"Game scheduled successfully in {game_channel.mention}!",
                ephemeral=True
            )

        except ValueError:
            await interaction.response.send_message(
                "Invalid time format. Please use HH:MM format (e.g., 8:00).",
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(
                f"An error occurred: {str(e)}",
                ephemeral=True
            )

    except Exception as e:
        await interaction.response.send_message(
            f"An error occurred: {str(e)}",
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()
@bot.tree.command(name="remove_team", description="Remove a team from the server")
@app_commands.describe(
    team_role="The role of the team you want to remove",
    remove_role="Whether to delete the Discord role as well"
)
async def remove_team(
    interaction: discord.Interaction, 
    team_role: discord.Role,
    remove_role: bool = False
):
    # Verify the user has admin permissions
    if not interaction.user.guild_permissions.administrator:
        return await interaction.response.send_message(
            'You need administrator permissions to use this command.',
            ephemeral=True
        )
    
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Get team info from teams table
        cursor.execute('''
        SELECT team_name, emoji FROM teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        team_info = cursor.fetchone()
        
        if not team_info:
            return await interaction.followup.send(
                f'No team found with role {team_role.mention}. Please verify the role is linked to a team.',
                ephemeral=True
            )
        
        team_name, emoji = team_info
        
        # Remove from teams table
        cursor.execute('''
        DELETE FROM teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        conn.commit()
        
        # Remove the Discord role if requested
        role_status = ""
        if remove_role:
            try:
                await team_role.delete(reason="Team removed via /remove_team command")
                role_status = " The team role has also been deleted."
            except discord.Forbidden:
                role_status = " Failed to delete the team role due to insufficient permissions."
            except Exception as e:
                role_status = f" Failed to delete the team role: {str(e)}"
        
        # Build response message
        response = (f'Team "{team_name}" ({emoji}) with role {team_role.mention} '
                   f'has been successfully removed from the database.{role_status}')
        
        await interaction.followup.send(response, ephemeral=True)
        
    except Exception as error:
        print(f"Error removing team: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error removing the team: {error}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()

@bot.tree.command(name="edit_team", description="Edit an existing team's information")
@app_commands.describe(
    team_role="The role of the team you want to edit",
    new_name="The new name for the team (optional)",
    new_emoji="The new emoji for the team (optional)"
)
async def edit_team(
    interaction: discord.Interaction, 
    team_role: discord.Role, 
    new_name: str = None, 
    new_emoji: str = None
):
    # Verify the user has admin permissions
    if not interaction.user.guild_permissions.administrator:
        return await interaction.response.send_message(
            'You need administrator permissions to use this command.',
            ephemeral=True
        )
    
    # Check if at least one parameter to edit was provided
    if not new_name and not new_emoji:
        return await interaction.response.send_message(
            'You must provide at least one property to update (name or emoji).',
            ephemeral=True
        )
    
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if the team exists in teams table
        cursor.execute('''
        SELECT team_name, emoji FROM teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        team_info = cursor.fetchone()
        if not team_info:
            return await interaction.followup.send(
                f'No team found with role {team_role.mention}. Please verify the role is linked to a team.',
                ephemeral=True
            )
        
        current_name, current_emoji = team_info
        
        # Determine values to update
        name_to_use = new_name if new_name else current_name
        emoji_to_use = new_emoji if new_emoji else current_emoji
        
        # Update the teams table
        cursor.execute('''
        UPDATE teams
        SET team_name = ?, emoji = ?
        WHERE role_id = ? AND guild_id = ?
        ''', (name_to_use, emoji_to_use, str(team_role.id), str(interaction.guild.id)))
        
        conn.commit()
        
        # Build a message about what was updated
        updates = []
        if new_name:
            updates.append(f'name from "{current_name}" to "{new_name}"')
        if new_emoji:
            updates.append(f'emoji from {current_emoji} to {new_emoji}')
        
        update_message = ", ".join(updates)
        
        await interaction.followup.send(
            f'Team {name_to_use} {emoji_to_use} has been updated! Changed {update_message}.',
            ephemeral=True
        )
    except Exception as error:
        print(f"Error editing team: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error editing the team: {error}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()

# First, add this class definition somewhere before your commands
class TeamListPaginator(discord.ui.View):
    def __init__(self, embeds):
        super().__init__(timeout=300)  # 5-minute timeout
        self.embeds = embeds
        self.current_page = 0
        self.total_pages = len(embeds)
        
    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.gray)
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer()
    
    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.gray)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer()

import discord
from discord import app_commands
from discord.ext import commands
import sqlite3
import traceback
from datetime import datetime

# Assuming these are defined elsewhere in your main bot setup
bot = commands.Bot(command_prefix='!', intents=discord.Intents.all())
DB_FILE = 'league_database.db'

class TeamListPaginator(discord.ui.View):
    def __init__(self, embeds):
        super().__init__()
        self.embeds = embeds
        self.current_page = 0

    @discord.ui.button(label="Previous", style=discord.ButtonStyle.gray)
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_page = max(0, self.current_page - 1)
        await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)

    @discord.ui.button(label="Next", style=discord.ButtonStyle.gray)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_page = min(len(self.embeds) - 1, self.current_page + 1)
        await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)

@bot.tree.command(name="list_teams", description="List all registered teams in the server")
async def list_teams(interaction: Interaction):
    """Lists all teams that have been registered through search_teams"""
    await interaction.response.defer(ephemeral=True)
    
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    try:
        # Get all teams from the database for this guild
        cursor.execute("""
        SELECT 
            role_id, team_name, league_type, sport, 
            emoji, conference, division 
        FROM Teams 
        WHERE guild_id = ?
        ORDER BY conference, division, team_name
        """, (str(interaction.guild.id),))
        
        teams = cursor.fetchall()
        
        if not teams:
            return await interaction.followup.send(
                "No teams found. Use `/search_teams` first to register teams.",
                ephemeral=True
            )
        
        # Create embed for team listing
        embed = discord.Embed(
            title="🏆 Registered Teams",
            description=f"Total Teams: {len(teams)}",
            color=discord.Color.blue()
        )
        
        # Group teams by conference and division
        team_groups = {}
        for team in teams:
            role_id, name, league, sport, emoji, conf, div = team
            
            # Get the actual role object
            role = interaction.guild.get_role(int(role_id))
            if not role:
                continue
                
            conf = conf or "Unassigned"
            div = div or "Unassigned"
            
            if conf not in team_groups:
                team_groups[conf] = {}
            if div not in team_groups[conf]:
                team_groups[conf][div] = []
                
            team_groups[conf][div].append({
                'name': name,
                'emoji': emoji or "🏆",
                'league': league,
                'sport': sport,
                'role': role
            })
        
        # Add fields for each conference and division
        for conf in sorted(team_groups.keys()):
            conf_teams = []
            for div in sorted(team_groups[conf].keys()):
                teams_in_div = team_groups[conf][div]
                if teams_in_div:
                    conf_teams.append(f"**{div}**")
                    for team in sorted(teams_in_div, key=lambda x: x['name']):
                        conf_teams.append(
                            f"{team['emoji']} {team['name']} "
                            f"({team['sport']} - {team['league']})"
                        )
            
            if conf_teams:
                embed.add_field(
                    name=f"Conference: {conf}",
                    value="\n".join(conf_teams),
                    inline=False
                )
        
        await interaction.followup.send(embed=embed, ephemeral=True)
        
    except Exception as error:
        print(f"Error in list_teams: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'An error occurred while listing teams: {error}',
            ephemeral=True
        )
    
    finally:
        conn.close()

bot.run(DISCORD_TOKEN)
