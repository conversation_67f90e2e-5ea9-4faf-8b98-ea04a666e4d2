# =========================
# SUPABASE COMPATIBILITY FIXES
# This file contains the fixes to make the bot work with Supabase
# =========================

# Fix 1: Replace all direct cursor calls with database adapter calls
# Find and replace pattern: bot.cursor.execute( -> bot.db.execute(
# Find and replace pattern: bot.conn.commit() -> bot.db.commit()

# Fix 2: Update the DatabaseAdapter class with better error handling
IMPROVED_DATABASE_ADAPTER = '''
class DatabaseAdapter:
    """Database adapter that works with both SQLite and Supabase with improved error handling"""
    
    def __init__(self):
        self.db_type = DATABASE_TYPE_SETTING
        self.supabase_client = None
        self.sqlite_conn = None
        self.sqlite_cursor = None
        self.retry_count = 3
        self.retry_delay = 1.0
        
        print(f"🗄️ Database Type: {self.db_type}")
        
        if self.db_type == 'supabase' and SUPABASE_AVAILABLE:
            self.setup_supabase()
        else:
            self.setup_sqlite()
    
    def setup_supabase(self):
        """Initialize Supabase connection with retry logic"""
        if not SUPABASE_URL_SETTING or not SUPABASE_KEY_SETTING:
            print("⚠️ Supabase URL or KEY not configured")
            self.db_type = 'sqlite'
            self.setup_sqlite()
            return
        
        try:
            from supabase import create_client
            self.supabase_client = create_client(SUPABASE_URL_SETTING, SUPABASE_KEY_SETTING)
            print("✅ Connected to Supabase database")
            # Test connection
            test_result = self.supabase_client.table('guild_settings').select('*').limit(1).execute()
            print("✅ Supabase database connection verified")
        except Exception as e:
            print(f"❌ Failed to connect to Supabase: {e}")
            print("   Falling back to SQLite...")
            self.db_type = 'sqlite'
            self.setup_sqlite()
    
    def setup_sqlite(self):
        """Initialize SQLite connection"""
        try:
            self.sqlite_conn = sqlite3.connect('transaction_bot.db', timeout=30.0)
            self.sqlite_cursor = self.sqlite_conn.cursor()
            print("✅ Connected to SQLite database")
        except Exception as e:
            print(f"❌ Failed to connect to SQLite: {e}")
            raise
    
    def execute(self, query: str, params: tuple = None):
        """Execute a query with retry logic and error handling"""
        for attempt in range(self.retry_count):
            try:
                if self.db_type == 'supabase':
                    return self.execute_supabase(query, params)
                else:
                    return self.execute_sqlite(query, params)
            except Exception as e:
                if attempt == self.retry_count - 1:
                    print(f"❌ Database operation failed after {self.retry_count} attempts: {e}")
                    print(f"   Query: {query}")
                    print(f"   Params: {params}")
                    raise
                print(f"⚠️ Database operation failed (attempt {attempt + 1}/{self.retry_count}): {e}")
                import time
                time.sleep(self.retry_delay * (attempt + 1))
    
    def execute_sqlite(self, query: str, params: tuple = None):
        """Execute SQLite query"""
        try:
            if params:
                self.sqlite_cursor.execute(query, params)
            else:
                self.sqlite_cursor.execute(query)
            return self.sqlite_cursor.fetchall()
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                import time
                time.sleep(0.1)
                return self.execute_sqlite(query, params)
            raise
        except Exception as e:
            print(f"SQLite error: {e}")
            raise
    
    def normalize_id(self, id_value):
        """Normalize Discord IDs to strings for consistent storage"""
        if id_value is None:
            return None
        return str(id_value)
    
    def execute_supabase(self, query: str, params: tuple = None):
        """Execute Supabase query with improved error handling"""
        try:
            query_upper = query.upper().strip()
            
            if query_upper.startswith('SELECT'):
                return self.handle_select_query(query, params)
            elif query_upper.startswith('INSERT'):
                return self.handle_insert_query(query, params)
            elif query_upper.startswith('UPDATE'):
                return self.handle_update_query(query, params)
            elif query_upper.startswith('DELETE'):
                return self.handle_delete_query(query, params)
            elif query_upper.startswith(('CREATE TABLE', 'ALTER TABLE', 'DROP TABLE')):
                # Skip DDL statements for Supabase
                print(f"Skipping DDL statement for Supabase: {query}")
                return []
            elif query_upper.startswith('PRAGMA'):
                # Skip SQLite-specific statements
                return []
            else:
                print(f"Unsupported query type for Supabase: {query}")
                return []
        except Exception as e:
            print(f"Supabase error: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            raise
    
    def handle_select_query(self, query: str, params: tuple = None):
        """Handle SELECT queries for Supabase with comprehensive table support"""
        query_lower = query.lower()
        
        # Handle COUNT queries specially
        if 'select count(*)' in query_lower:
            return self.handle_count_query(query_lower, params)
        
        # Extract table name
        table_name = self.extract_table_name(query_lower)
        if not table_name:
            print(f"Could not extract table name from query: {query}")
            return []
        
        try:
            table = self.supabase_client.table(table_name)
            
            # Apply WHERE conditions
            table = self.apply_where_conditions(table, query_lower, params)
            
            # Execute query
            result = table.execute()
            
            # Convert result to match SQLite format
            return self.convert_result_to_tuples(result.data, table_name)
            
        except Exception as e:
            print(f"Error in SELECT query for table {table_name}: {e}")
            raise
    
    def handle_count_query(self, query_lower: str, params: tuple = None):
        """Handle COUNT(*) queries"""
        table_name = self.extract_table_name(query_lower)
        if not table_name:
            return [(0,)]
        
        try:
            table = self.supabase_client.table(table_name)
            table = self.apply_where_conditions(table, query_lower, params)
            result = table.select('*', count='exact').execute()
            return [(result.count,)]
        except Exception as e:
            print(f"Error in COUNT query: {e}")
            return [(0,)]
    
    def extract_table_name(self, query_lower: str):
        """Extract table name from SQL query"""
        try:
            if 'from ' in query_lower:
                # Find the FROM clause
                from_index = query_lower.find('from ') + 5
                remaining = query_lower[from_index:].strip()
                # Get the first word (table name)
                table_name = remaining.split()[0]
                # Remove any trailing conditions
                table_name = table_name.split(' ')[0].split('(')[0].strip()
                return table_name
            elif 'update ' in query_lower:
                # For UPDATE queries
                update_index = query_lower.find('update ') + 7
                remaining = query_lower[update_index:].strip()
                table_name = remaining.split()[0]
                return table_name
            elif 'into ' in query_lower:
                # For INSERT queries
                into_index = query_lower.find('into ') + 5
                remaining = query_lower[into_index:].strip()
                table_name = remaining.split()[0]
                return table_name
        except:
            pass
        return None
    
    def apply_where_conditions(self, table, query_lower: str, params: tuple = None):
        """Apply WHERE conditions to Supabase query builder"""
        if not params or 'where' not in query_lower:
            return table
        
        param_index = 0
        
        # Handle various WHERE conditions
        conditions = [
            ('guild_id = ?', 'guild_id'),
            ('slot_id = ?', 'slot_id'),
            ('role_id = ?', 'role_id'),
            ('is_default = ?', 'is_default'),
            ('status = ?', 'status'),
            ('player_id = ?', 'player_id'),
            ('contract_id = ?', 'contract_id'),
            ('game_id = ?', 'game_id'),
            ('user_id = ?', 'user_id'),
            ('team_role_id = ?', 'team_role_id'),
            ('channel_id = ?', 'channel_id'),
            ('message_id = ?', 'message_id')
        ]
        
        for condition, field in conditions:
            if condition in query_lower and param_index < len(params):
                value = params[param_index]
                if value is not None:
                    if field in ['guild_id', 'role_id', 'team_role_id', 'channel_id', 'message_id', 'user_id', 'player_id']:
                        value = self.normalize_id(value)
                    table = table.eq(field, value)
                param_index += 1
        
        return table
    
    def convert_result_to_tuples(self, data: list, table_name: str):
        """Convert Supabase result to SQLite-like tuple format"""
        if not data:
            return []
        
        # Define common field orders for consistent tuple conversion
        field_orders = {
            'guild_settings': ['guild_id', 'admin_role', 'log_channel', 'application_channel', 
                             'suspended_role', 'suspended_channel', 'application_blacklist_role', 
                             'transaction_log_channel'],
            'slots': ['slot_id', 'guild_id', 'slot_name', 'description', 'is_default', 'weeks_per_season'],
            'slot_configs': ['slot_id', 'franchise_owner_role', 'general_manager_role', 'head_coach_role',
                           'assistant_coach_role', 'free_agent_role', 'transaction_channel', 'gametime_channel',
                           'referee_role', 'streamer_role', 'trade_channel', 'draft_channel', 'pickup_host_role',
                           'pickup_channel', 'score_channel'],
            'league_teams': ['id', 'role_id', 'slot_id', 'team_name', 'emoji'],
            'contracts': ['contract_id', 'guild_id', 'slot_id', 'player_id', 'team_role_id', 'contract_amount',
                         'contract_length', 'time_unit', 'start_date', 'end_date', 'status'],
            'slot_command_settings': ['slot_id', 'guild_id', 'sign_enabled', 'release_enabled', 'offer_enabled',
                                    'roster_cap', 'money_cap_enabled', 'money_cap_amount'],
            'slot_demand_config': ['slot_id', 'demands_enabled', 'demand_limit', 'demand_channel',
                                 'five_demands_role', 'four_demands_role', 'three_demands_role',
                                 'two_demands_role', 'one_demand_role', 'no_demands_role'],
            'gametimes': ['game_id', 'guild_id', 'slot_id', 'team1_role_id', 'team1_name', 'team1_emoji',
                         'team2_role_id', 'team2_name', 'team2_emoji', 'scheduled_time', 'timezone', 'status'],
            'transactions': ['transaction_id', 'guild_id', 'slot_id', 'transaction_type', 'description',
                           'performed_by', 'timestamp'],
            'applications': ['application_id', 'guild_id', 'slot_id', 'user_id', 'team_role_id',
                           'application_type', 'status'],
            'disband_transactions': ['disband_id', 'guild_id', 'slot_id', 'team_role_id', 'team_name',
                                   'disbanded_by', 'disbanded_at'],
            'live_management_lists': ['guild_id', 'channel_id', 'slot_id', 'display_type', 'auto_update',
                                    'include_empty', 'league_style', 'secondary_label', 'message_ids',
                                    'last_updated', 'active']
        }
        
        field_order = field_orders.get(table_name, list(data[0].keys()) if data else [])
        
        result = []
        for row in data:
            tuple_row = tuple(row.get(field) for field in field_order)
            result.append(tuple_row)
        
        return result
    
    def handle_insert_query(self, query: str, params: tuple = None):
        """Handle INSERT queries for Supabase"""
        query_lower = query.lower()
        table_name = self.extract_table_name(query_lower)
        
        if not table_name or not params:
            print(f"Invalid INSERT query: {query}")
            return []
        
        try:
            # Handle INSERT OR IGNORE
            if 'insert or ignore' in query_lower:
                existing = self.check_record_exists(table_name, params)
                if existing:
                    return []
            
            # Build data dictionary
            data = self.build_insert_data(table_name, params)
            
            # Ensure guild exists for foreign key constraints
            if 'guild_id' in data:
                self.ensure_guild_exists(data['guild_id'])
            
            # Execute insert
            table = self.supabase_client.table(table_name)
            result = table.insert(data).execute()
            return []
            
        except Exception as e:
            print(f"Error in INSERT query for table {table_name}: {e}")
            raise
    
    def check_record_exists(self, table_name: str, params: tuple):
        """Check if record exists for INSERT OR IGNORE"""
        try:
            table = self.supabase_client.table(table_name)
            
            # Check based on primary key or unique constraints
            if table_name == 'guild_settings':
                result = table.select('guild_id').eq('guild_id', self.normalize_id(params[0])).execute()
            elif table_name == 'slots':
                result = table.select('slot_id').eq('slot_id', params[0]).execute()
            elif table_name == 'slot_configs':
                result = table.select('slot_id').eq('slot_id', params[0]).execute()
            elif table_name == 'slot_command_settings':
                result = table.select('slot_id').eq('slot_id', params[0]).execute()
            elif table_name == 'slot_demand_config':
                result = table.select('slot_id').eq('slot_id', params[0]).execute()
            elif table_name == 'league_teams':
                result = table.select('role_id').eq('role_id', self.normalize_id(params[0])).execute()
            else:
                return False
            
            return len(result.data) > 0
        except:
            return False
    
    def build_insert_data(self, table_name: str, params: tuple):
        """Build data dictionary for INSERT operations"""
        data = {}
        
        # Define parameter mappings for each table
        if table_name == 'guild_settings':
            fields = ['guild_id', 'admin_role', 'log_channel', 'application_channel', 
                     'suspended_role', 'suspended_channel', 'application_blacklist_role',
                     'transaction_log_channel']
            for i, field in enumerate(fields):
                if i < len(params):
                    value = params[i]
                    if field == 'guild_id':
                        data[field] = self.normalize_id(value)
                    elif value is not None:
                        data[field] = self.normalize_id(value) if 'role' in field or 'channel' in field else value
        
        elif table_name == 'slots':
            fields = ['slot_id', 'guild_id', 'slot_name', 'description', 'is_default', 'weeks_per_season']
            for i, field in enumerate(fields):
                if i < len(params) and params[i] is not None:
                    value = params[i]
                    if field == 'guild_id':
                        data[field] = self.normalize_id(value)
                    else:
                        data[field] = value
        
        elif table_name == 'league_teams':
            fields = ['role_id', 'slot_id', 'team_name', 'emoji']
            for i, field in enumerate(fields):
                if i < len(params):
                    value = params[i]
                    if field == 'role_id':
                        data[field] = self.normalize_id(value)
                    elif value is not None:
                        data[field] = value
        
        elif table_name == 'slot_configs':
            data['slot_id'] = params[0] if params else None
            # Add other fields as needed based on the INSERT query structure
        
        elif table_name == 'slot_command_settings':
            fields = ['slot_id', 'guild_id', 'sign_enabled', 'release_enabled', 'offer_enabled',
                     'roster_cap', 'money_cap_enabled', 'money_cap_amount']
            for i, field in enumerate(fields):
                if i < len(params):
                    value = params[i]
                    if field == 'guild_id':
                        data[field] = self.normalize_id(value)
                    elif value is not None:
                        data[field] = value
        
        elif table_name == 'slot_demand_config':
            data['slot_id'] = params[0] if params else None
            # Add other fields as needed
        
        # Add more table mappings as needed
        
        return data
    
    def ensure_guild_exists(self, guild_id: str):
        """Ensure guild_settings record exists"""
        try:
            guild_table = self.supabase_client.table('guild_settings')
            existing = guild_table.select('guild_id').eq('guild_id', guild_id).execute()
            if not existing.data:
                guild_table.insert({'guild_id': guild_id}).execute()
        except Exception as e:
            print(f"Error ensuring guild exists: {e}")
    
    def handle_update_query(self, query: str, params: tuple = None):
        """Handle UPDATE queries for Supabase"""
        query_lower = query.lower()
        table_name = self.extract_table_name(query_lower)
        
        if not table_name or not params:
            return []
        
        try:
            table = self.supabase_client.table(table_name)
            
            # Extract what's being updated and conditions
            update_data, where_data = self.parse_update_query(query_lower, params)
            
            # Build query
            query_builder = table.update(update_data)
            for field, value in where_data.items():
                if field in ['guild_id', 'role_id', 'team_role_id', 'channel_id', 'message_id', 'user_id', 'player_id']:
                    value = self.normalize_id(value)
                query_builder = query_builder.eq(field, value)
            
            result = query_builder.execute()
            return []
            
        except Exception as e:
            print(f"Error in UPDATE query for table {table_name}: {e}")
            raise
    
    def parse_update_query(self, query_lower: str, params: tuple):
        """Parse UPDATE query to extract SET and WHERE clauses"""
        update_data = {}
        where_data = {}
        
        # This is a simplified parser - enhance based on your specific queries
        param_index = 0
        
        # Common UPDATE patterns
        if 'set ' in query_lower:
            # Extract field being updated (simplified)
            if param_index < len(params):
                # You'll need to enhance this based on your specific UPDATE queries
                # For now, assume first parameter is the value being set
                update_data['updated_field'] = params[param_index]
                param_index += 1
        
        # Extract WHERE clause
        if 'where ' in query_lower:
            if 'guild_id = ?' in query_lower and param_index < len(params):
                where_data['guild_id'] = params[param_index]
            elif 'slot_id = ?' in query_lower and param_index < len(params):
                where_data['slot_id'] = params[param_index]
            elif 'role_id = ?' in query_lower and param_index < len(params):
                where_data['role_id'] = params[param_index]
        
        return update_data, where_data
    
    def handle_delete_query(self, query: str, params: tuple = None):
        """Handle DELETE queries for Supabase"""
        query_lower = query.lower()
        table_name = self.extract_table_name(query_lower)
        
        if not table_name:
            return []
        
        try:
            table = self.supabase_client.table(table_name)
            
            # Apply WHERE conditions for DELETE
            if params and 'where' in query_lower:
                if 'guild_id = ?' in query_lower:
                    table.delete().eq('guild_id', self.normalize_id(params[0])).execute()
                elif 'slot_id = ?' in query_lower:
                    table.delete().eq('slot_id', params[0]).execute()
                elif 'role_id = ?' in query_lower:
                    table.delete().eq('role_id', self.normalize_id(params[0])).execute()
                elif 'contract_id = ?' in query_lower:
                    table.delete().eq('contract_id', params[0]).execute()
                elif 'game_id = ?' in query_lower:
                    table.delete().eq('game_id', params[0]).execute()
            
            return []
            
        except Exception as e:
            print(f"Error in DELETE query for table {table_name}: {e}")
            raise
    
    def fetchone(self):
        """SQLite compatibility method"""
        if self.db_type == 'sqlite':
            return self.sqlite_cursor.fetchone()
        return None
    
    def fetchall(self):
        """SQLite compatibility method"""
        if self.db_type == 'sqlite':
            return self.sqlite_cursor.fetchall()
        return []
    
    def commit(self):
        """Commit changes - SQLite compatibility"""
        if self.db_type == 'sqlite':
            self.sqlite_conn.commit()
        # Supabase auto-commits
    
    def close(self):
        """Close connection"""
        if self.db_type == 'sqlite' and self.sqlite_conn:
            self.sqlite_conn.close()
'''

# Fix 3: Add Supabase installation check
INSTALLATION_CHECK = '''
# Check for required packages
def check_dependencies():
    """Check if all required packages are installed"""
    missing_packages = []
    
    try:
        import discord
        print("✅ discord.py installed")
    except ImportError:
        missing_packages.append("discord.py")
    
    try:
        import supabase
        print("✅ supabase-py installed")
        global SUPABASE_AVAILABLE
        SUPABASE_AVAILABLE = True
    except ImportError:
        print("⚠️ supabase-py not installed")
        missing_packages.append("supabase")
        SUPABASE_AVAILABLE = False
    
    try:
        import gspread
        print("✅ gspread installed")
        global GOOGLE_SHEETS_AVAILABLE
        GOOGLE_SHEETS_AVAILABLE = True
    except ImportError:
        print("⚠️ gspread not installed (optional)")
        GOOGLE_SHEETS_AVAILABLE = False
    
    if missing_packages:
        print(f"❌ Missing required packages: {missing_packages}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    return True

# Call dependency check at startup
if not check_dependencies():
    print("Please install missing dependencies before running the bot.")
    exit(1)
'''

# Fix 4: Add data migration utility
MIGRATION_UTILITY = '''
async def migrate_sqlite_to_supabase():
    """Migrate data from SQLite to Supabase"""
    if DATABASE_TYPE_SETTING != 'supabase':
        print("Migration only available when using Supabase")
        return False
    
    if not SUPABASE_AVAILABLE or not SUPABASE_URL_SETTING or not SUPABASE_KEY_SETTING:
        print("Supabase not properly configured")
        return False
    
    try:
        print("🔄 Starting SQLite to Supabase migration...")
        
        # Create SQLite connection for reading
        sqlite_conn = sqlite3.connect('transaction_bot.db', timeout=30.0)
        sqlite_cursor = sqlite_conn.cursor()
        
        # Create Supabase client
        from supabase import create_client
        supabase = create_client(SUPABASE_URL_SETTING, SUPABASE_KEY_SETTING)
        
        # Migration tables and their priority order
        migration_tables = [
            'guild_settings',
            'slots', 
            'slot_configs',
            'slot_command_settings',
            'slot_demand_config',
            'league_teams',
            'contracts',
            'gametimes',
            'transactions',
            'applications',
            'disband_transactions',
            'live_management_lists'
        ]
        
        migrated_counts = {}
        
        for table_name in migration_tables:
            try:
                print(f"📋 Migrating {table_name}...")
                
                # Check if table exists in SQLite
                sqlite_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if not sqlite_cursor.fetchone():
                    print(f"   Table {table_name} not found in SQLite, skipping...")
                    continue
                
                # Get all data from SQLite
                sqlite_cursor.execute(f"SELECT * FROM {table_name}")
                rows = sqlite_cursor.fetchall()
                
                if not rows:
                    print(f"   No data found in {table_name}")
                    migrated_counts[table_name] = 0
                    continue
                
                # Get column names
                sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [column[1] for column in sqlite_cursor.fetchall()]
                
                # Convert rows to dictionaries
                data_to_migrate = []
                for row in rows:
                    row_dict = {}
                    for i, column in enumerate(columns):
                        value = row[i] if i < len(row) else None
                        # Normalize Discord IDs to strings
                        if column in ['guild_id', 'role_id', 'team_role_id', 'channel_id', 'message_id', 'user_id', 'player_id'] and value is not None:
                            value = str(value)
                        row_dict[column] = value
                    data_to_migrate.append(row_dict)
                
                # Insert data into Supabase in batches
                batch_size = 100
                migrated_count = 0
                
                for i in range(0, len(data_to_migrate), batch_size):
                    batch = data_to_migrate[i:i + batch_size]
                    try:
                        result = supabase.table(table_name).upsert(batch).execute()
                        migrated_count += len(batch)
                        print(f"   Migrated batch {i//batch_size + 1}: {len(batch)} records")
                    except Exception as e:
                        print(f"   Error migrating batch for {table_name}: {e}")
                        # Try individual inserts for this batch
                        for record in batch:
                            try:
                                supabase.table(table_name).upsert(record).execute()
                                migrated_count += 1
                            except Exception as record_error:
                                print(f"   Failed to migrate record: {record_error}")
                
                migrated_counts[table_name] = migrated_count
                print(f"   ✅ Migrated {migrated_count} records from {table_name}")
                
            except Exception as e:
                print(f"   ❌ Error migrating {table_name}: {e}")
                migrated_counts[table_name] = 0
        
        sqlite_cursor.close()
        sqlite_conn.close()
        
        # Print migration summary
        print("\\n📊 Migration Summary:")
        total_migrated = 0
        for table_name, count in migrated_counts.items():
            print(f"   {table_name}: {count} records")
            total_migrated += count
        
        print(f"\\n✅ Migration completed! Total records migrated: {total_migrated}")
        
        # Validate migration
        print("🔍 Validating migration...")
        validation_passed = True
        for table_name in migration_tables:
            try:
                result = supabase.table(table_name).select('*', count='exact').execute()
                supabase_count = result.count
                sqlite_count = migrated_counts.get(table_name, 0)
                if supabase_count >= sqlite_count:
                    print(f"   ✅ {table_name}: {supabase_count} records in Supabase")
                else:
                    print(f"   ⚠️ {table_name}: Expected {sqlite_count}, found {supabase_count}")
                    validation_passed = False
            except Exception as e:
                print(f"   ❌ Error validating {table_name}: {e}")
                validation_passed = False
        
        if validation_passed:
            print("\\n✅ Migration validation passed!")
        else:
            print("\\n⚠️ Migration validation found issues - please check manually")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        traceback.print_exc()
        return False
'''

print("✅ Database compatibility fixes created!")
print("\\nNext steps to apply the fixes:")
print("1. Replace the DatabaseAdapter class in your main file with the improved version")
print("2. Replace all bot.cursor.execute() calls with bot.db.execute() calls") 
print("3. Add the installation check and migration utility")
print("4. Test with SQLite first, then switch to Supabase")
print("5. Run the migration utility to transfer data to Supabase")
