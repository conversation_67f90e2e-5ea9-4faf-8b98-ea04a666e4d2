import sqlite3
import os

# Check if database exists
db_path = 'transaction_bot.db'
if not os.path.exists(db_path):
    print(f"❌ Database file {db_path} not found")
    exit(1)

# Connect to database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Check slot_configs table schema
print("🔍 Checking slot_configs table schema...")
cursor.execute("PRAGMA table_info(slot_configs)")
columns = cursor.fetchall()

print(f"Found {len(columns)} columns in slot_configs table:")
for col in columns:
    print(f"  - {col[1]} ({col[2]})")

# Check if weekly_games_channel column exists
has_weekly_games_channel = any(col[1] == 'weekly_games_channel' for col in columns)
print(f"\nweekly_games_channel column exists: {has_weekly_games_channel}")

# Check guild_settings table schema
print("\n🔍 Checking guild_settings table schema...")
cursor.execute("PRAGMA table_info(guild_settings)")
columns = cursor.fetchall()

print(f"Found {len(columns)} columns in guild_settings table:")
for col in columns:
    print(f"  - {col[1]} ({col[2]})")

# Check if transaction_log_channel column exists
has_transaction_log_channel = any(col[1] == 'transaction_log_channel' for col in columns)
print(f"\ntransaction_log_channel column exists: {has_transaction_log_channel}")

# Check teams table schema
print("\n🔍 Checking teams table schema...")
cursor.execute("PRAGMA table_info(teams)")
columns = cursor.fetchall()

print(f"Found {len(columns)} columns in teams table:")
for col in columns:
    print(f"  - {col[1]} ({col[2]})")

conn.close()
print("\n✅ Schema check completed")
