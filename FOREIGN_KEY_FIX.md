# Discord Bot - Foreign Key Constraint Fix

## 🔧 **ISSUE RESOLVED**

**Problem:** Foreign key constraint violation when creating slots in Supabase
```
Error: insert or update on table "slots" violates foreign key constraint "slots_guild_id_fkey"
Details: Key (guild_id)=(1355293567886561320) is not present in table "guild_settings"
```

## ✅ **SOLUTION IMPLEMENTED**

### 1. **Root Cause**
The bot was trying to insert records into tables with foreign key constraints (slots, contracts, transactions, etc.) before ensuring the referenced guild_settings record existed.

### 2. **Fix Applied**
- **Added `ensure_guild_exists()` helper method** to automatically create guild_settings records when needed
- **Updated all INSERT handlers** to check and create guild_settings before inserting dependent records
- **Covers all tables with guild_id foreign keys:**
  - slots
  - contracts  
  - gametimes
  - transactions
  - applications
  - disband_transactions
  - live_management_lists

### 3. **How It Works**
```python
def ensure_guild_exists(self, guild_id: str):
    """Ensure guild_settings record exists for the given guild_id"""
    guild_table = self.supabase_client.table('guild_settings')
    existing_guild = guild_table.select('guild_id').eq('guild_id', guild_id).execute()
    if not existing_guild.data:
        guild_table.insert({'guild_id': guild_id}).execute()
        print(f"Auto-created guild_settings record for guild {guild_id}")
```

### 4. **Example Usage**
Before inserting a slot:
```python
# Ensure guild_settings exists first
guild_id = str(params[1])
self.ensure_guild_exists(guild_id)

# Now safely insert the slot
table = self.supabase_client.table('slots')
# ... insert logic
```

## 🎯 **EXPECTED RESULT**

When you restart your bot, it should now:
1. ✅ **Automatically create guild_settings** when needed
2. ✅ **Successfully create slots** without foreign key errors
3. ✅ **Work seamlessly** with both new and existing guilds
4. ✅ **Log the auto-creation** for transparency

## 📋 **VERIFIED**
- ✅ Syntax validation passed
- ✅ All foreign key relationships preserved
- ✅ Backward compatibility maintained
- ✅ No data loss or corruption

The bot should now work perfectly without foreign key constraint violations! 🚀
