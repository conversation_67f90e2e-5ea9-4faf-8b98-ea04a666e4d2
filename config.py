# =========================
# CONFIGURATION FILE
# =========================

# Discord Bot Token
DISCORD_BOT_TOKEN = "MTM3NTUyNjc2NTM5NjYyNzU4OA.GcfSiu.H5CT6K8gsj9gIblua9j6ssoBMqYebVjq4McT2k"

# Database Type - Set to 'supabase' to use cloud database, 'sqlite' for local
DATABASE_TYPE = "sqlite"

# Supabase Configuration (fill these in when you're ready to use Supabase)
SUPABASE_URL = "https://ueheoabegwccfkholyku.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.puxraTE7UGm9wkfqQvTxIp7NAINODFhMOXmgFpRPBKo"

# Google Sheets Configuration (optional)
GOOGLE_SERVICE_ACCOUNT_JSON = ""

# Bot Owner ID for owner-only commands
BOT_OWNER_ID = 123456789012345678  # Replace with your Discord user ID
