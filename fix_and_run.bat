@echo off
echo =================================
echo  DATABASE SCHEMA FIX AND BOT RUN
echo =================================
echo.

echo 1. Checking current directory...
cd /d "c:\Users\<USER>\Downloads\bot"
echo Current directory: %CD%
echo.

echo 2. Checking if database exists...
if exist "transaction_bot.db" (
    echo ✅ Database file found
) else (
    echo ❌ Database file not found
    echo Creating empty database...
    sqlite3 transaction_bot.db "SELECT 1;"
)
echo.

echo 3. Fixing database schema...
sqlite3 transaction_bot.db "ALTER TABLE slot_configs ADD COLUMN weekly_games_channel INTEGER;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Added weekly_games_channel column
) else (
    echo ℹ️ weekly_games_channel column may already exist
)

sqlite3 transaction_bot.db "ALTER TABLE slot_configs ADD COLUMN score_channel INTEGER;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Added score_channel column
) else (
    echo ℹ️ score_channel column may already exist
)

sqlite3 transaction_bot.db "ALTER TABLE slot_configs ADD COLUMN created_at TEXT;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Added created_at column
) else (
    echo ℹ️ created_at column may already exist
)

sqlite3 transaction_bot.db "ALTER TABLE slot_configs ADD COLUMN updated_at TEXT;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Added updated_at column
) else (
    echo ℹ️ updated_at column may already exist
)

sqlite3 transaction_bot.db "ALTER TABLE guild_settings ADD COLUMN transaction_log_channel INTEGER;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Added transaction_log_channel column
) else (
    echo ℹ️ transaction_log_channel column may already exist
)

sqlite3 transaction_bot.db "ALTER TABLE teams ADD COLUMN guild_id INTEGER;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Added guild_id column to teams
) else (
    echo ℹ️ guild_id column may already exist in teams
)

echo.
echo 4. Verifying schema...
echo Checking slot_configs columns:
sqlite3 transaction_bot.db "PRAGMA table_info(slot_configs);"
echo.
echo Checking guild_settings columns:
sqlite3 transaction_bot.db "PRAGMA table_info(guild_settings);"
echo.

echo 5. Schema fix completed!
echo.
echo The bot should now be able to sync without column errors.
echo You can now run the bot manually or restart it.
echo.
pause
