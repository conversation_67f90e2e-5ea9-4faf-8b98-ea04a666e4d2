# Supabase Data Corruption Issue - RESOLVED

## Issue Description
The `/list_teams` command was failing due to corrupted data in the Supabase database where:

1. **Team names were stored in `role_id` fields** instead of numeric Discord role IDs
   - Example: `role_id` contained "Washington Commanders" instead of a numeric Discord snowflake
2. **Slot IDs were stored in role fields** in slot configurations
   - Example: `franchise_owner_role` contained slot UUID instead of role ID
3. **Invalid data causing sync/validation failures**

## Error Log Example
```
Warning: Invalid role_id 'Washington Commanders' for team 'f683f88dbc164976a0029d8f21b93c9c' in slot '1384680184526934149', skipping...
Warning: Invalid franchise_owner_role ID 'f683f88dbc164976a0029d8f21b93c9c', skipping...
```

## Root Cause
This corruption likely occurred during data migration or backup operations where:
- Column mappings were incorrect
- Data validation was insufficient
- Non-numeric values were inserted into role ID fields

## Solution Implemented

### 1. Created Data Cleanup Script
- **File**: `fix_supabase_data_corruption.py`
- **Purpose**: Clean up invalid data directly in Supabase
- **Actions**:
  - Remove teams with non-numeric role IDs
  - Clear invalid role IDs in slot configs
  - Clean guild settings with invalid role references

### 2. Enhanced Data Validation
**Updated sync function** (`sync_supabase_data_on_startup`):
- Stronger validation: `len(str(role_id)) >= 17` (Discord snowflakes are 17+ digits)
- Better error messages with team names
- Skip invalid entries with detailed logging

**Updated backup function** (`backup_data_to_supabase`):
- Validate all role IDs before uploading to Supabase
- Prevent corruption at the source
- Only backup valid Discord snowflakes

### 3. Validation Function
Added `validate_role_id()` helper:
```python
def validate_role_id(role_id):
    return role_id if role_id and str(role_id).isdigit() and len(str(role_id)) >= 17 else None
```

## Steps to Fix Your Database

### Immediate Fix:
1. **Edit the cleanup script** (`fix_supabase_data_corruption.py`)
   - Add your actual Supabase URL and KEY
2. **Run the cleanup script**:
   ```bash
   python fix_supabase_data_corruption.py
   ```
3. **Re-setup your teams**:
   ```
   /autosetup  # In Discord to recreate valid teams
   ```
4. **Sync the cleaned data**:
   ```
   /sync_data force=True  # As bot owner to pull clean data
   ```

### Verification:
- Check Supabase dashboard to ensure corrupted entries are removed
- Run `/list_teams` to verify it works without errors
- Monitor logs for any remaining validation warnings

## Prevention
The enhanced validation now prevents this corruption from happening again by:
- Validating all role IDs as 17+ digit Discord snowflakes
- Blocking invalid data during sync/backup operations
- Providing clear error messages for debugging

## Files Modified
- `import sqlite3.py` - Enhanced validation in sync/backup functions
- `fix_supabase_data_corruption.py` - New cleanup script
- `SUPABASE_DATA_CORRUPTION_FIX.md` - This documentation

## Status: ✅ RESOLVED
The bot now has robust data validation and a cleanup script to fix the corrupted Supabase data.
