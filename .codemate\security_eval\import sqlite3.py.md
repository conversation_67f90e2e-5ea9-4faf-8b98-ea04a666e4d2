# Security Vulnerability Report

## Overview

The provided code is for a feature-rich Discord bot handling various league management and transaction workflows, with support for both SQLite and Supabase as databases.

This review identifies **security vulnerabilities only** based on the code provided. Each issue will explain **risk, evidence/location, and recommended fix**.

---

## 1. **Hardcoded Secrets and Credentials**

### a. Discord Bot Token Hardcoded

**Location:**
```python
# Discord Bot Token
DISCORD_BOT_TOKEN = "MTM3NTUyNjc2NTM5NjYyNzU4OA.GcfSiu.H5CT6K8gsj9gIblua9j6ssoBMqYebVjq4McT2k"  # Your existing token
```

**Risk:**  
Leaking the bot token (especially one written in documentation or code) allows anyone to take control of the bot and its permissions across all servers it is installed on.

**Fix:**  
- Never hard-code tokens in code repositories or files.
- Load from environment variables (`os.environ["DISCORD_BOT_TOKEN"]`).
- Immediately regenerate and revoke the token if this code has been shared.

---

### b. Database and API Keys Hardcoded

**Location:**
```python
SUPABASE_URL = "https://ueheoabegwccfkholyku.supabase.co"  # Your project URL
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.puxraTE7UGm9wkfqQvTxIp7NAINODFhMOXmgFpRPBKo"  # Your anon key
GOOGLE_SERVICE_ACCOUNT_JSON = ""  # Your service account JSON as a string (alternative to JSON file)
```

**Risk:**  
Exposes your Supabase and Google Cloud credentials to anyone who can read the code, allowing full database and resource access.

**Fix:**  
- Always remove sensitive keys from source code.
- Use environment variables and dotenv files, and do not check them into version control.
- Rotate compromised keys immediately.

---

## 2. **SQL Injection Risks**

### a. Unvalidated Column Names in UPDATE statements

**Location:**  
```python
await view.save_single_slot_config_item(self.table_name, self.custom_id, selected_role_id)
#...
query = f'''
    INSERT INTO {table_name} (slot_id, {key}) VALUES (?,?)
    ON CONFLICT(slot_id) DO UPDATE SET {key} = excluded.{key}
'''
self.bot.db.execute(query, (self.slot_id, value))
```
And:
```python
self.db.execute(f"UPDATE guild_settings SET {key} =? WHERE guild_id =?", (value, guild_id))
```

**Risk:**  
If `key` or `table_name` is user-controllable, this creates a blind SQL injection vector. While in many places, allowable values are constrained (e.g., `valid_keys`), a *single oversight* could lead to RCE on the database.

**Fix:**  
- Use **whitelists** for all column and table names (it looks like you already have some, ensure they cover every case).
- Never allow user-supplied values as direct SQL column/table names without validation.
- Use parameterized queries for values (not identifiers).

---

### b. Dynamic SQL for Team Roles

**Location:**  
```python
placeholders = ','.join(['?' for _ in user_roles])
result = bot.db.execute(f"""
    SELECT lt.role_id, lt.team_name, lt.emoji, s.slot_id, s.slot_name
    FROM league_teams lt
    JOIN slots s ON lt.slot_id = s.slot_id
    WHERE s.guild_id =? AND lt.role_id IN ({placeholders})
""", (guild_id, *[str(r) for r in user_roles]))
```

**Risk:**  
The `placeholders` variable is constructed based on the length of `user_roles`, which is "safe" if the only thing being inserted dynamically is the number of `?`, but *if* user_roles was in any way not well-formed, it could lead to malformed SQL.

**Fix:**  
- Never allow row or column data (i.e., role names) to be interpolated directly; only use SQL parameters for user input.
- Sanitize and validate everything, especially anything used in an f-string that affects SQL queries.

---

### c. Unsafe Migration/Alter Statements

**Location:**  
```python
self.cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type}")
```

**Risk:**  
If table or column names are ever built from user input (e.g., through an admin UI or future extension), this is a vector.

**Fix:**  
- Validate that `table_name` and `col_name` are from a hardcoded set, never user-provided.

---

## 3. **Insufficient Permissions Checks/Bypass**

### a. Admin Commands

Several command handlers (e.g., `/global_setup`, `/setup`, `/create_slot`, `/delete_slot`, `/reset`) use the following permission check:

```python
if not await has_bot_management_permission(interaction):
    await interaction.response.send_message(
        "You need server administrator permissions or the configured Bot Admin Role to use this command.",
        ephemeral=True
    )
    return
```

**Risk:**  
If any route or logic allows bypassing this check, users could escalate privileges or destroy data.

**Fix:**  
- Rigorously enforce this check on any sensitive action (don't "trust" handlers to always be called with prior checks).
- Consider using Discord's 'app_commands.checks' with explicit role/permission decorators.

---

### b. Composable UI Views/Buttons

Some Views associate actions (like destructive deletes/resets) to button presses. The callback defers to `if intx.user != interaction.user:` check.

**Risk:**  
If for any reason, the `interaction` object is mismatched (e.g., through concurrency, message editing), another user could theoretically "race" to press the button and trigger the action.

**Fix:**  
- Persist the user id server-side on creation, and always compare the actual Discord user id from the interaction—not just the object.
- Always validate permissions on callbacks.

---

## 4. **Sensitive Data Disclosure**

### a. Error/Exception Feedback

**Location:**  
Throughout code, internal errors are often printed and in some cases could be returned to the user.

Example:
```python
except Exception as e:
    print(f"Error creating slot: {e}")
    traceback.print_exc()
    await interaction.followup.send("An error occurred while creating the slot. Please try again.", ephemeral=True)
```

**Risk:**  
While most of these catch and show generic errors, in some exception handlers (e.g., in `/offer` and elsewhere), caught exception strings are inserted back into ephemeral Discord messages. This may accidentally leak sensitive stack traces or data.

**Fix:**  
- Never show full exception messages to end users.
- Always use generic error output for users, and log details to a secure log server or internal protected system only.

---

## 5. **Open Field Values / No Data Validation**

Many inputs (e.g., slot names, team names, descriptions) are accepted and inserted into the DB or sent as Discord embed values without checks for malicious content (HTML, SQL meta-characters, excessive length, etc.).

**Risks:**
- **Resource Exhaustion:** Users can set extremely large names or descriptions, which could cause performance issues or fill up Discord embed limits.
- **Cross-Channel Linking/Spam/Phishing:** Slots or teams could be named `@everyone`, links, or other misleading data.
- **Future Expansion (in a web UI):** If ever rendered in a web context, lack of escaping could lead to XSS.

**Fix:**  
- Add limits and character filters for all user-contributed data—validate on *input*, not just for display.

---

## 6. **Improper Ephemeral Data Usage**

In complex Views, followups and edits, there can be mismatches between ephemeral or public responses, leading to accidental data leaks into public channels.

**Risk:**  
Sensitive info returned (e.g., error traces, slot info, user-specific details) as a public message rather than ephemeral.

**Fix:**  
- Always set `ephemeral=True` for mod/admin commands, or any outputs that may contain user-identifiable or configuration details.

---

## 7. **Insufficient Auditing/Logging**

**Location:**  
Sensitive operations (e.g., mass resets, deletes) are logged to a channel if configured. However, if the setup wizard was not completed, or if the channel disappears, no warning is issued except a Python `print()`.

**Risk:**  
Critical actions may go completely unlogged, preventing attribution/audit after incidents.

**Fix:**  
- Ensure ALL destructive actions are logged somewhere out-of-band (e.g., error logs or an immutable audit file).
- Consider storing in DB a local log or backup for destructive actions.

---

## 8. **Potential Race Conditions (Non-Atomic Multi-Step Actions)**

**Location:**  
- Creating new slots, teams, or configs often involves several separate insert/update calls.
- No transaction wrapping (`BEGIN/COMMIT/ROLLBACK`) beyond manually issuing `.commit()`.

**Risk:**  
If two admins try to create or edit a slot at the same time, intermediate database state could lead to orphan data, privilege escalations, or lost/inconsistent configuration.

**Fix:**  
- Use proper database transactions (atomic blocks) for all multi-step modifications.

---

## 9. **Improper / Incomplete Discord Permissions Checks for Role/Channel Operations**

**Location:**  
Before adding/removing roles or joining/leaving channels, the bot does not always check if it has the correct permissions (beyond basic error catching).

**Risk:**  
If roles/channels are reordered or permissions change, attempts to manipulate roles might silently fail or result in partial/inaccurate changes. On heavily permissioned servers, this could be exploited to "trap" the bot, resulting in lost access or a denial of service (DoS) for other management features.

**Fix:**  
- Proactively check permissions before attempting role/channel changes. Notify admins of permission problems.

---

## 10. **Use of Weak/Custom Input Parsing for Commands**

**Location:**  
Some commands use regular expressions to parse strings for things like activity level, contract terms, or links.

**Risk:**  
If regex patterns are not properly constrained, a malicious user could submit specially crafted input to cause excessive backtracking (ReDoS), buffer issues, or simply bypass data validation.

**Fix:**  
- Carefully validate and bound all regexes.
- Use libraries' safe parsing functions.

---

# Conclusion

**Summary:**  
The codebase is generally robust, but exposes several critical vulnerabilities, mainly stemming from **insecure secret management**, **potential SQL injection from unsafe use of dynamically constructed SQL**, **insufficient permissions and validation checks**, and **possible sensitive data leakage**.

---

# Recommendations Checklist

- [ ] **Move all sensitive credentials to environment variables.**
- [ ] **Enforce strict validation/whitelisting for dynamic SQL query parts (columns, tables).**
- [ ] **Add full input validation and length restrictions for user-provided data.**
- [ ] **Never show detailed error/exception data to end users.**
- [ ] **Require explicit permissions checking on all destructive operations, including in callbacks.**
- [ ] **Wrap multi-step DB operations in atomic transactions.**
- [ ] **Audit Discord bot permissions before performing role/channel management.**
- [ ] **Log all destructive actions, even if Discord logging is unavailable.**

---

## **Critical actions: IMMEDIATELY REVOKE AND ROTATE ALL HARD-CODED TOKENS/SECRETS SHOWN.**  
If you have deployed or made this code public, treat all hardcoded credentials as compromised!

---

*Contact a security professional for a deeper security audit and remediation, especially before making production deployments.*