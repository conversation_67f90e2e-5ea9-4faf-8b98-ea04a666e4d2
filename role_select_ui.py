import discord
from discord import app_commands
from discord.ext import commands
from typing import List, Optional

class RoleSelectUI(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    # Modal with text input for role selection
    class RoleSelectModal(discord.ui.Modal):
        def __init__(self, title="Select a Role"):
            super().__init__(title=title)
            
            # Add a text input for the role name
            self.role_input = discord.ui.TextInput(
                label="Start typing a role name",
                placeholder="Type to search for roles...",
                required=True,
                min_length=1,
                max_length=100
            )
            self.add_item(self.role_input)
            
            # Store the selected role ID
            self.selected_role_id = None
        
        async def on_submit(self, interaction: discord.Interaction):
            # This will be called when the user submits the modal
            # We'll handle this in the command
            pass

    # Select menu for roles
    class RoleSelectView(discord.ui.View):
        def __init__(self, roles, original_interaction):
            super().__init__(timeout=60)
            self.original_interaction = original_interaction
            self.selected_role_id = None
            
            # Create a select menu with the roles
            self.select = discord.ui.Select(
                placeholder="Select a role",
                min_values=1,
                max_values=1,
                options=[
                    discord.SelectOption(
                        label=role.name,
                        value=str(role.id),
                        description=f"Members: {len(role.members)}"
                    ) for role in roles[:25]  # Discord limits to 25 options
                ]
            )
            
            # Set the callback
            self.select.callback = self.select_callback
            self.add_item(self.select)
            
            # Add a cancel button
            self.cancel_button = discord.ui.Button(
                style=discord.ButtonStyle.secondary,
                label="Cancel",
                custom_id="cancel"
            )
            self.cancel_button.callback = self.cancel_callback
            self.add_item(self.cancel_button)
        
        async def select_callback(self, interaction: discord.Interaction):
            self.selected_role_id = self.select.values[0]
            # Get the role object
            role = interaction.guild.get_role(int(self.selected_role_id))
            
            if role:
                await interaction.response.send_message(f"You selected: {role.mention}", ephemeral=True)
                # Stop listening for interactions
                self.stop()
            else:
                await interaction.response.send_message("Role not found.", ephemeral=True)
        
        async def cancel_callback(self, interaction: discord.Interaction):
            await interaction.response.send_message("Selection cancelled.", ephemeral=True)
            self.stop()

    @app_commands.command(name="select_role", description="Select a role using a dynamic menu")
    async def select_role(self, interaction: discord.Interaction):
        await interaction.response.send_message(
            "Click the button below to search for a role:",
            view=self.RoleSearchView(self),
            ephemeral=True
        )

    # View with a button to open the role search modal
    class RoleSearchView(discord.ui.View):
        def __init__(self, cog):
            super().__init__(timeout=180)
            self.cog = cog
        
        @discord.ui.button(label="Search Roles", style=discord.ButtonStyle.primary)
        async def search_button(self, interaction: discord.Interaction, button: discord.ui.Button):
            # Create and show the modal
            modal = self.cog.RoleSelectModal()
            await interaction.response.send_modal(modal)
            
            # Wait for the modal to be submitted
            await modal.wait()
            
            # After submission, filter roles based on the input
            search_term = modal.role_input.value.lower()
            roles = interaction.guild.roles
            
            # Filter and sort roles
            matching_roles = [role for role in roles if search_term in role.name.lower() and not role.is_default()]
            
            # Sort by relevance
            exact_matches = [role for role in matching_roles if role.name.lower() == search_term]
            starts_with = [role for role in matching_roles if role.name.lower().startswith(search_term) and role not in exact_matches]
            contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
            
            sorted_roles = exact_matches + starts_with + contains
            
            if sorted_roles:
                # Show the select menu with matching roles
                view = self.cog.RoleSelectView(sorted_roles, interaction)
                await interaction.followup.send(
                    f"Found {len(sorted_roles)} matching roles. Select one:",
                    view=view,
                    ephemeral=True
                )
            else:
                await interaction.followup.send("No matching roles found.", ephemeral=True)

    # Command to send a message with a role mention using the UI
    @app_commands.command(name="message_with_role", description="Send a message mentioning a role")
    @app_commands.describe(message="The message to send")
    async def message_with_role(self, interaction: discord.Interaction, message: str):
        # Create a custom view for this specific command
        class MessageRoleView(discord.ui.View):
            def __init__(self, cog, message_content):
                super().__init__(timeout=180)
                self.cog = cog
                self.message_content = message_content
            
            @discord.ui.button(label="Add Role Mention", style=discord.ButtonStyle.primary)
            async def add_role_button(self, interaction: discord.Interaction, button: discord.ui.Button):
                # Create and show the modal
                modal = self.cog.RoleSelectModal(title="Select Role to Mention")
                await interaction.response.send_modal(modal)
                
                # Wait for the modal to be submitted
                await modal.wait()
                
                # After submission, filter roles based on the input
                search_term = modal.role_input.value.lower()
                roles = interaction.guild.roles
                
                # Filter and sort roles
                matching_roles = [role for role in roles if search_term in role.name.lower() and not role.is_default()]
                
                # Sort by relevance
                exact_matches = [role for role in matching_roles if role.name.lower() == search_term]
                starts_with = [role for role in matching_roles if role.name.lower().startswith(search_term) and role not in exact_matches]
                contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
                
                sorted_roles = exact_matches + starts_with + contains
                
                if sorted_roles:
                    # Create a select menu for the matching roles
                    class RoleMentionSelect(discord.ui.Select):
                        def __init__(self, roles, message_content):
                            self.message_content = message_content
                            options = [
                                discord.SelectOption(
                                    label=role.name,
                                    value=str(role.id),
                                    description=f"Members: {len(role.members)}"
                                ) for role in roles[:25]
                            ]
                            super().__init__(placeholder="Select a role to mention", options=options)
                        
                        async def callback(self, interaction: discord.Interaction):
                            role_id = self.values[0]
                            role = interaction.guild.get_role(int(role_id))
                            
                            if role:
                                # Send the message with the role mention
                                await interaction.channel.send(f"{self.message_content} {role.mention}")
                                await interaction.response.send_message("Message sent with role mention!", ephemeral=True)
                            else:
                                await interaction.response.send_message("Role not found.", ephemeral=True)
                    
                    # Create a view with the select menu
                    select_view = discord.ui.View(timeout=60)
                    select_view.add_item(RoleMentionSelect(sorted_roles, self.message_content))
                    
                    await interaction.followup.send(
                        f"Found {len(sorted_roles)} matching roles. Select one to mention:",
                        view=select_view,
                        ephemeral=True
                    )
                else:
                    await interaction.followup.send("No matching roles found.", ephemeral=True)
        
        # Send the initial message with the button
        await interaction.response.send_message(
            "Click the button below to add a role mention to your message:",
            view=MessageRoleView(self, message),
            ephemeral=True
        )

async def setup(bot):
    await bot.add_cog(RoleSelectUI(bot))
