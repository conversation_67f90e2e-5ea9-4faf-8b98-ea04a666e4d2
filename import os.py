import os
import discord
from discord.ext import commands
import asyncio
import re
import pandas as pd
from google.oauth2 import service_account
from googleapiclient.discovery import build
import logging
import json
import io

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('football_stats_bot')

# Bot Configuration
SPREADSHEET_ID = "1NS7wksiq_0caC_P4rJHtcZvIlqbJH6H5ST_keUtCsVc"  # Your Google Sheet ID
STATS_CHANNEL_ID = int(os.getenv('STATS_CHANNEL_ID', 0))  # Channel to monitor for stats
BOT_TOKEN = 'MTM2MjkyMTAyMjQ1MzU4Mzk2Mg.Gw_yMa.7T1jT-HHBLMYdKurheO3vx6Eo45w17dfGajyZ8'  # Bot token - replace with your token

# Google Sheets service account credentials
SERVICE_ACCOUNT_INFO = *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Sheet name mapping - position representation
SHEET_NAME_MAPPING = {
    'Passing': 'QB Stats',
    'Rushing': 'RB Stats',
    'Receiving': 'WR Stats',
    'Cornerback': 'DB Stats',
    'DLine': 'DE Stats',
    'SpecialTeams': 'Kicker Stats'
}

# Initialize Discord bot with intents
intents = discord.Intents.default()
intents.messages = True
intents.message_content = True
bot = commands.Bot(command_prefix='!', intents=intents)

# Google Sheets API setup
def setup_sheets_api():
    try:
        # Use the hardcoded service account info
        credentials = service_account.Credentials.from_service_account_info(
            SERVICE_ACCOUNT_INFO, 
            scopes=['https://www.googleapis.com/auth/spreadsheets'])
        
        # Log credentials for debugging
        logger.info(f"Using service account: {SERVICE_ACCOUNT_INFO['client_email']}")
        logger.info(f"Project ID: {SERVICE_ACCOUNT_INFO['project_id']}")
        
        # Build the service with explicit project ID
        return build('sheets', 'v4', 
                    credentials=credentials,
                    discoveryServiceUrl='https://sheets.googleapis.com/$discovery/rest?version=v4')
    except Exception as e:
        logger.error(f"Failed to set up Google Sheets API: {e}")
        return None
        
# Bot ready event
@bot.event
async def on_ready():
    logger.info(f'{bot.user.name} has connected to Discord!')
    
    # Verify channel exists
    channel = bot.get_channel(STATS_CHANNEL_ID)
    if not channel:
        logger.error(f"Could not find the stats channel with ID {STATS_CHANNEL_ID}")
    else:
        logger.info(f"Successfully connected to stats channel: {channel.name}")
    
    # Test Google Sheets connection
    sheets_service = setup_sheets_api()
    if sheets_service:
        try:
            logger.info(f"Testing connection to spreadsheet: {SPREADSHEET_ID}")
            result = sheets_service.spreadsheets().values().get(
                spreadsheetId=SPREADSHEET_ID, 
                range='Sheet1!A1:A2'
            ).execute()
            logger.info("Successfully connected to Google Sheets API")
        except Exception as e:
            logger.error(f"Google Sheets connection test failed: {e}")
            # Try to get specific error details
            if hasattr(e, 'content'):
                try:
                    error_details = json.loads(e.content.decode('utf-8'))
                    logger.error(f"Error details: {error_details}")
                except:
                    pass

# Function to extract code blocks from a message
def extract_code_blocks(message_content):
    # Match code blocks with or without language specifier
    pattern = r'```(?:\w+\n)?(.*?)```'
    code_blocks = re.findall(pattern, message_content, re.DOTALL)
    return code_blocks

# Function to clean up dataframe - fix unnamed columns
def clean_dataframe(df):
    # Clean up column names - remove unnamed columns or handle them properly
    new_columns = []
    seen_columns = set()
    
    for col in df.columns:
        # Clean the column name
        cleaned_col = col.strip().replace('"', '')
        
        # Check if it's an unnamed column
        if 'unnamed' in cleaned_col.lower() or not cleaned_col:
            # Skip unnamed columns
            continue
            
        # Make sure we don't have duplicate column names
        if cleaned_col in seen_columns:
            counter = 1
            while f"{cleaned_col}_{counter}" in seen_columns:
                counter += 1
            cleaned_col = f"{cleaned_col}_{counter}"
            
        seen_columns.add(cleaned_col)
        new_columns.append(cleaned_col)
    
    # Create a new dataframe with just the named columns
    clean_df = df[[col for col in df.columns if col.strip().replace('"', '') in new_columns]]
    clean_df.columns = new_columns
    
    return clean_df

# Function to calculate player rankings for different stat categories
def calculate_rankings(data_dict):
    rankings = {}
    
    # Define ranking criteria for each category
    ranking_criteria = {
        'Passing': [('Passing Yards', True), ('Touchdowns', True), ('Completions', True)],
        'Rushing': [('Rushing Yards', True), ('Touchdowns', True), ('Average Yards', True)],
        'Receiving': [('Receiving Yards', True), ('Touchdowns', True), ('Receptions', True)],
        'Cornerback': [('Interceptions', True), ('Pass Break Ups', True), ('Tackles', True)],
        'DLine': [('Sacks', True), ('Tackles For Loss', True), ('QB Pressures', True)],
        'SpecialTeams': [('Field Goal Percent', True), ('Kick Return Yards', True), ('Punt Return Yards', True)]
    }
    
    # Calculate ranks for each category
    for category, criteria in ranking_criteria.items():
        if category not in data_dict:
            continue
            
        df = data_dict[category]
        
        # Clean up the dataframe - remove unnamed columns
        df = clean_dataframe(df)
        
        # Create a ranking column initialized with zeros
        df['Rank Score'] = 0
        
        # Calculate rank score based on criteria
        for column, higher_is_better in criteria:
            if column in df.columns:
                # Convert to numeric if needed
                if df[column].dtype == 'object':
                    try:
                        if column == 'Field Goal Percent':
                            # Handle percentage format (e.g., '100.0%' -> 100.0)
                            df[column] = df[column].str.rstrip('%').astype('float')
                        else:
                            df[column] = pd.to_numeric(df[column], errors='coerce')
                    except:
                        logger.warning(f"Could not convert {column} to numeric in {category}")
                        continue
                
                # Normalize the column to 0-1 range and add to rank score
                if df[column].max() > df[column].min():
                    if higher_is_better:
                        df['Rank Score'] += (df[column] - df[column].min()) / (df[column].max() - df[column].min())
                    else:
                        df['Rank Score'] += (df[column].max() - df[column]) / (df[column].max() - df[column].min())
        
        # Check if all stats are zero for each player before assigning rank
        # Create a temporary df without user/team columns and rank score for zero check
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        numeric_cols = [col for col in numeric_cols if col != 'Rank Score' and col != 'Rank']
        
        # Function to check if a player has all zero stats
        def all_stats_zero(row, columns):
            return all(row[col] == 0 for col in columns if col in row.index)
        
        # Apply the check to each row
        df['All_Zero'] = df.apply(lambda row: all_stats_zero(row, numeric_cols), axis=1)
        
        # Only assign ranks to players with non-zero stats
        df['Rank'] = 0  # Initialize all ranks to 0
        df.loc[~df['All_Zero'], 'Rank'] = df.loc[~df['All_Zero'], 'Rank Score'].rank(method='dense', ascending=False).astype(int)
        
        # Remove the temporary columns
        df = df.drop(columns=['Rank Score', 'All_Zero'])
        
        # Store back in the dictionary
        data_dict[category] = df
        
    return data_dict

# Process CSV with football stats
def process_football_stats(content):
    # Split the content by section headers
    sections = re.split(r'\n\n([A-Za-z]+)\n', content)
    
    # Initialize dictionary to store DataFrames
    data_dict = {}
    
    # Process each section
    for i in range(1, len(sections), 2):
        if i + 1 >= len(sections):
            continue
            
        section_name = sections[i]
        section_data = sections[i+1]
        
        # Skip defense sheet as requested
        if section_name.lower() == 'defense':
            logger.info("Skipping Defense section as requested")
            continue
            
        # Parse the CSV data
        try:
            # Add more robust CSV parsing
            df = pd.read_csv(io.StringIO(section_data), quotechar='"', on_bad_lines='skip')
            
            # Clean up dataframe and remove unnamed columns
            df = clean_dataframe(df)
            
            # Identify user column - it could be 'Username' or the first column
            user_column = 'Username'
            if user_column not in df.columns and len(df.columns) > 0:
                for col in df.columns:
                    if any(user_term in col.lower() for user_term in ['user', 'name', 'player']):
                        user_column = col
                        break
                if user_column == 'Username' and len(df.columns) > 0:
                    user_column = df.columns[0]
                logger.info(f"Using '{user_column}' as the user identifier column")
            
            # Identify team column
            team_column = None
            for col in df.columns:
                if any(team_term in col.lower() for team_term in ['team', 'emoji']):
                    team_column = col
                    break
            
            # Add team column if it doesn't exist
            if team_column is None:
                df['Team'] = ''
                team_column = 'Team'
            
            # Convert numeric columns
            for col in df.columns:
                if col not in [user_column, team_column] and col.lower() not in ['team', 'team emoji', 'emoji']:
                    try:
                        # Remove percentage signs if present
                        if 'Percent' in col:
                            df[col] = df[col].astype(str).str.rstrip('%').replace('', '0')
                        
                        # Convert to numeric
                        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
                    except Exception as e:
                        logger.warning(f"Could not convert column {col} to numeric: {e}")
            
            # Store in dictionary
            data_dict[section_name] = df
            
            logger.info(f"Processed {section_name} section: {len(df)} rows")
        except Exception as e:
            logger.error(f"Error processing {section_name} section: {e}")
            logger.error(f"Section data: {section_data[:100]}...")  # Log first 100 chars for debugging
    
    # Calculate rankings
    data_dict = calculate_rankings(data_dict)
    
    return data_dict

# Function to update Google Sheets with football stats
async def update_football_stats_sheet(data_dict):
    sheets_service = setup_sheets_api()
    if not sheets_service:
        return False, "Failed to connect to Google Sheets API"
    
    try:
        logger.info(f"Updating spreadsheet: {SPREADSHEET_ID}")
        
        # Get existing sheet names
        sheet_metadata = sheets_service.spreadsheets().get(spreadsheetId=SPREADSHEET_ID).execute()
        existing_sheets = {sheet.get("properties", {}).get("title", ""): sheet.get("properties", {}).get("sheetId", 0) 
                          for sheet in sheet_metadata.get('sheets', [])}
        
        # For each category, update a separate sheet
        for category, df in data_dict.items():
            # Skip if dataframe is empty
            if df.empty:
                logger.warning(f"Skipping empty dataframe for category: {category}")
                continue
            
            # Get the correct sheet name from mapping
            sheet_name = SHEET_NAME_MAPPING.get(category, category)
            
            # Clean up the dataframe
            # Identify user column - could be 'Username' or the first column
            user_column = 'Username'
            if user_column not in df.columns and len(df.columns) > 0:
                for col in df.columns:
                    if any(user_term in col.lower() for user_term in ['user', 'name', 'player']):
                        user_column = col
                        break
                if user_column == 'Username' and len(df.columns) > 0:
                    user_column = df.columns[0]
            
            # Identify team column
            team_column = None
            for col in df.columns:
                if any(team_term in col.lower() for team_term in ['team', 'emoji']):
                    team_column = col
                    break
            
            # Add team column if it doesn't exist
            if team_column is None:
                df['Team'] = ''
                team_column = 'Team'
            
            # Check if user column exists
            if user_column in df.columns:
                df = df.copy()  # Create a copy to avoid SettingWithCopyWarning
                
                # Clean and sanitize data
                for col in df.columns:
                    # Replace NaN values with 0 for numeric columns
                    if pd.api.types.is_numeric_dtype(df[col]):
                        df[col] = df[col].fillna(0)
                    # Ensure string columns are properly sanitized
                    elif pd.api.types.is_object_dtype(df[col]):
                        df[col] = df[col].astype(str).fillna("").str.replace('"', "'").str.strip()
                
                # Sort by rank if available, otherwise sort by a relevant stat
                if 'Rank' in df.columns:
                    df = df.sort_values('Rank')
                elif category == 'Passing' and 'Passing Yards' in df.columns:
                    df = df.sort_values('Passing Yards', ascending=False)
                elif category == 'Rushing' and 'Rushing Yards' in df.columns:
                    df = df.sort_values('Rushing Yards', ascending=False)
                elif category == 'Receiving' and 'Receiving Yards' in df.columns:
                    df = df.sort_values('Receiving Yards', ascending=False)
                elif category == 'Cornerback' and 'Interceptions' in df.columns:
                    df = df.sort_values('Interceptions', ascending=False)
                elif category == 'DLine' and 'Sacks' in df.columns:
                    df = df.sort_values('Sacks', ascending=False)
                elif category == 'SpecialTeams' and 'Field Goal Percent' in df.columns:
                    df = df.sort_values('Field Goal Percent', ascending=False)
                
                # First check if the sheet exists and get existing data
                try:
                    # Check if the sheet exists under the new name
                    logger.info(f"Checking if sheet '{sheet_name}' exists")
                    existing_data = None
                    sheet_exists = sheet_name in existing_sheets
                    
                    if sheet_exists:
                        try:
                            result = sheets_service.spreadsheets().values().get(
                                spreadsheetId=SPREADSHEET_ID,
                                range=f"'{sheet_name}'!A1:Z1000"  # Adjust range as needed
                            ).execute()
                            existing_data = result.get('values', [])
                            logger.info(f"Sheet '{sheet_name}' exists with {len(existing_data)} rows")
                        except Exception as e:
                            logger.info(f"Sheet '{sheet_name}' exists but couldn't get data: {str(e)}")
                            existing_data = []
                
                    # Create the sheet if it doesn't exist
                    if not sheet_exists:
                        logger.info(f"Creating new sheet '{sheet_name}'")
                        request_body = {
                            'requests': [{
                                'addSheet': {
                                    'properties': {
                                        'title': sheet_name
                                    }
                                }
                            }]
                        }
                        sheets_service.spreadsheets().batchUpdate(
                            spreadsheetId=SPREADSHEET_ID,
                            body=request_body
                        ).execute()
                        logger.info(f"Created sheet '{sheet_name}'")
                        
                        # Update existing sheets dictionary
                        sheet_metadata = sheets_service.spreadsheets().get(spreadsheetId=SPREADSHEET_ID).execute()
                        existing_sheets = {sheet.get("properties", {}).get("title", ""): sheet.get("properties", {}).get("sheetId", 0) 
                                          for sheet in sheet_metadata.get('sheets', [])}
                    
                    # Restructure the data frame to have columns in the order: Rank, Team, Username, ...rest
                    # Make a copy of the columns excluding Rank, Team, and Username
                    remaining_cols = [col for col in df.columns if col not in ['Rank', team_column, user_column]]
                    
                    # Create the new column order
                    new_column_order = ['Rank', team_column, user_column] + remaining_cols
                    
                    # Only include columns that actually exist in the dataframe
                    valid_columns = [col for col in new_column_order if col in df.columns]
                    
                    # Reorder the dataframe
                    df = df[valid_columns]
                    
                    # Prepare the values list with row 1 blank for title
                    values = [[""] * len(df.columns)]  # Row 1 is blank for title
                    values.append(df.columns.tolist())  # Row 2 has column headers
                    
                    # Add data rows
                    for _, row in df.iterrows():
                        values.append([row[col] for col in df.columns])
                    
                    # Update the sheet
                    logger.info(f"Updating sheet '{sheet_name}' with {len(values)} rows")
                    # Clear the sheet first to avoid issues with mismatched columns
                    sheets_service.spreadsheets().values().clear(
                        spreadsheetId=SPREADSHEET_ID,
                        range=f"'{sheet_name}'!A1:Z1000",  # Adjust range as needed
                        body={}
                    ).execute()
                    
                    # Now update with new data
                    result = sheets_service.spreadsheets().values().update(
                        spreadsheetId=SPREADSHEET_ID,
                        range=f"'{sheet_name}'!A1",
                        valueInputOption='USER_ENTERED',
                        body={'values': values}
                    ).execute()
                    
                    logger.info(f"Updated {sheet_name} sheet: {result.get('updatedCells')} cells")
                except Exception as e:
                    logger.error(f"Error updating sheet '{sheet_name}': {e}")
                    if hasattr(e, 'content'):
                        try:
                            error_details = json.loads(e.content.decode('utf-8'))
                            logger.error(f"Error details: {error_details}")
                        except:
                            pass
                    continue  # Continue with next category
        
        return True, f"Updated {len(data_dict)} stat sheets in Google Sheets"
    
    except Exception as e:
        logger.error(f"Failed to update football stats: {e}")
        # Try to get specific error details
        if hasattr(e, 'content'):
            try:
                error_details = json.loads(e.content.decode('utf-8'))
                logger.error(f"Error details: {error_details}")
                return False, f"Failed to update football stats: {error_details.get('error', {}).get('message', str(e))}"
            except:
                pass
        return False, f"Failed to update football stats: {str(e)}"

# Message handler for the stats channel
@bot.event
async def on_message(message):
    # Process commands first
    await bot.process_commands(message)
    
    # Only process messages in the designated stats channel
    if message.channel.id != STATS_CHANNEL_ID:
        return
    
    logger.info(f"Processing message in stats channel from {message.author}")
    
    # Check for attachments first
    if message.attachments:
        for attachment in message.attachments:
            if attachment.filename.endswith('.csv'):
                try:
                    # Download the file
                    content = await attachment.read()
                    content_str = content.decode('utf-8')
                    
                    # Process the stats
                    data_dict = process_football_stats(content_str)
                    
                    # Update Google Sheets
                    success, result = await update_football_stats_sheet(data_dict)
                    
                    if success:
                        await message.channel.send(f"✅ Successfully processed football stats from {attachment.filename} and updated Google Sheets")
                    else:
                        await message.channel.send(f"❌ Failed to update stats: {result}")
                    
                    return
                except Exception as e:
                    logger.error(f"Failed to process attachment {attachment.filename}: {e}")
                    await message.channel.send(f"❌ Error processing {attachment.filename}: {str(e)}")
    
    # Check for code blocks in the message
    code_blocks = extract_code_blocks(message.content)
    
    if code_blocks:
        for code_block in code_blocks:
            try:
                # Check if this looks like football stats (contains section headers)
                if re.search(r'(Passing|Rushing|Receiving|Cornerback|DLine|SpecialTeams)', code_block):
                    # Process the stats
                    data_dict = process_football_stats(code_block)
                    
                    # Update Google Sheets
                    success, result = await update_football_stats_sheet(data_dict)
                    
                    if success:
                        await message.channel.send(f"✅ Successfully processed football stats from code block and updated Google Sheets")
                    else:
                        await message.channel.send(f"❌ Failed to update stats: {result}")
                    
                    return
            except Exception as e:
                logger.error(f"Failed to process code block: {e}")
                continue
    
    # If none of the above, check if the message content directly contains football stats
    content = message.content
    if re.search(r'(Passing|Rushing|Receiving|Cornerback|DLine|SpecialTeams)', content):
        try:
            # Process the stats
            data_dict = process_football_stats(content)
            
            # Update Google Sheets
            success, result = await update_football_stats_sheet(data_dict)
            
            if success:
                await message.channel.send(f"✅ Successfully processed football stats and updated Google Sheets")
            else:
                await message.channel.send(f"❌ Failed to update stats: {result}")
        except Exception as e:
            logger.error(f"Failed to process message content: {e}")
            await message.channel.send(f"❌ Error processing stats: {str(e)}")

# Command to set the stats channel
@bot.command(name='setstatchannel')
@commands.has_permissions(administrator=True)
async def set_stats_channel(ctx):
    global STATS_CHANNEL_ID
    STATS_CHANNEL_ID = ctx.channel.id
    
    # Save to environment variable if possible
    # In most hosting platforms, this won't persist between restarts
    os.environ['STATS_CHANNEL_ID'] = str(STATS_CHANNEL_ID)
    
    await ctx.send(f"✅ Stats channel set to {ctx.channel.name} (ID: {ctx.channel.id})")
    logger.info(f"Stats channel set to {ctx.channel.name} (ID: {ctx.channel.id})")

# Command to test the connection to Google Sheets
@bot.command(name='testsheets')
@commands.has_permissions(administrator=True)
async def test_sheets_connection(ctx):
    sheets_service = setup_sheets_api()
    if not sheets_service:
        await ctx.send("❌ Failed to connect to Google Sheets API. Check your credentials.")
        return
    
    try:
        # Test reading from the sheet
        logger.info(f"Testing connection to spreadsheet: {SPREADSHEET_ID}")
        await ctx.send(f"Testing connection to Google Sheets (ID: {SPREADSHEET_ID})...")
        
        # First, get the spreadsheet metadata to see what sheets are available
        sheet_metadata = sheets_service.spreadsheets().get(spreadsheetId=SPREADSHEET_ID).execute()
        sheets = sheet_metadata.get('sheets', '')
        sheet_names = [s.get("properties", {}).get("title", "") for s in sheets]
        
        logger.info(f"Found sheets: {', '.join(sheet_names)}")
        
        # If Sheet1 exists, try to read from it
        test_sheet = "Sheet1" if "Sheet1" in sheet_names else sheet_names[0] if sheet_names else None
        
        if test_sheet:
            result = sheets_service.spreadsheets().values().get(
                spreadsheetId=SPREADSHEET_ID, 
                range=f'{test_sheet}!A1:A2'
            ).execute()
            await ctx.send(f"✅ Successfully connected to Google Sheets!\nAvailable sheets: {', '.join(sheet_names)}")
        else:
            await ctx.send(f"⚠️ Connected to spreadsheet but no sheets found. The bot will create sheets when data is processed.")
    
    except Exception as e:
        error_msg = f"❌ Error testing Google Sheets connection: {str(e)}"
        logger.error(error_msg)
        
        # Try to get more detailed error information
        if hasattr(e, 'content'):
            try:
                error_details = json.loads(e.content.decode('utf-8'))
                detailed_error = f"\nError details: {json.dumps(error_details, indent=2)}"
                error_msg += detailed_error
                logger.error(detailed_error)
            except:
                pass
                
        await ctx.send(error_msg)

# Command to manually process stats
@bot.command(name='processstats')
@commands.has_permissions(administrator=True)
async def process_stats(ctx):
    # Check for attachments in the command message
    if ctx.message.attachments:
        for attachment in ctx.message.attachments:
            if attachment.filename.endswith('.csv'):
                try:
                    # Download the file
                    content = await attachment.read()
                    content_str = content.decode('utf-8')
                    
                    # Process the stats
                    data_dict = process_football_stats(content_str)
                    
                    # Update Google Sheets
                    success, result = await update_football_stats_sheet(data_dict)
                    
                    if success:
                        await ctx.send(f"✅ Successfully processed football stats from {attachment.filename} and updated Google Sheets")
                    else:
                        await ctx.send(f"❌ Failed to update stats: {result}")
                    
                    return
                except Exception as e:
                    logger.error(f"Failed to process attachment {attachment.filename}: {e}")
                    await ctx.send(f"❌ Error processing {attachment.filename}: {str(e)}")
    else:
        await ctx.send("❓ Please attach a CSV file containing football stats to use this command.")

# Command to display help information
@bot.command(name='statshelp')
async def stats_help(ctx):
    help_text = """
**Football Stats Bot Commands**

`!setstatchannel` - Set the current channel as the stats monitoring channel
`!testsheets` - Test the connection to Google Sheets
`!processstats` - Manually process stats from an attached CSV file
`!statshelp` - Show this help message

**How to Submit Stats**
1. Send a CSV file in the designated stats channel
2. Or paste stats data as a code block using triple backticks

The bot will automatically process the football stats, calculate rankings, and update the Google Sheet.

**Sheet Layout**
- Row 1: Reserved for custom titles
- Row 2: Stats categories
- Column A: Ranking
- Column B: Team/Emoji
- Column C: Usernames
- Remaining columns: Stats
"""
    await ctx.send(help_text)

# Error handler for the bot
@bot.event
async def on_command_error(ctx, error):
    if isinstance(error, commands.MissingPermissions):
        await ctx.send("❌ You don't have permission to use this command.")
    else:
        await ctx.send(f"❌ An error occurred: {str(error)}")
        logger.error(f"Command error: {error}")

# Run the bot
if __name__ == "__main__":
    try:
        logger.info("Starting Discord bot...")
        bot.run(BOT_TOKEN)
    except Exception as e:
        logger.critical(f"Failed to start the bot: {e}")