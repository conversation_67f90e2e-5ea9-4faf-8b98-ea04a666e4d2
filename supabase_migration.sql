-- Supabase Migration Script
-- This script creates all the necessary tables for the Discord bot

-- Enable Row Level Security (RLS) for all tables
-- You'll need to configure RLS policies in the Supabase dashboard

-- 1. Guild Settings Table
CREATE TABLE IF NOT EXISTS guild_settings (
    guild_id TEXT PRIMARY KEY,
    admin_role TEXT,
    transaction_channel TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Slots Table
CREATE TABLE IF NOT EXISTS slots (
    slot_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (guild_id) REFERENCES guild_settings(guild_id)
);

-- 3. Slot Configurations Table
CREATE TABLE IF NOT EXISTS slot_configs (
    slot_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    franchise_owner_role TEXT,
    general_manager_role TEXT,
    head_coach_role TEXT,
    assistant_coach_role TEXT,
    free_agent_role TEXT,
    transaction_channel TEXT,
    admin_role TEXT,
    gametime_channel TEXT,
    referee_role TEXT,
    streamer_role TEXT,
    score_channel TEXT,
    roster_cap INTEGER DEFAULT 25,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 4. League Teams Table
CREATE TABLE IF NOT EXISTS league_teams (
    id SERIAL PRIMARY KEY,
    role_id TEXT NOT NULL,
    slot_id TEXT,
    team_name TEXT NOT NULL,
    emoji TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 5. Transactions Table
CREATE TABLE IF NOT EXISTS transactions (
    transaction_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT,
    transaction_type TEXT NOT NULL,
    description TEXT,
    performed_by TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    affected_users_count INTEGER DEFAULT 0,
    player_id TEXT,
    from_team_role TEXT,
    to_team_role TEXT,
    details TEXT,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 6. Contracts Table
CREATE TABLE IF NOT EXISTS contracts (
    contract_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    player_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    contract_amount INTEGER NOT NULL,
    contract_length INTEGER NOT NULL,
    time_unit TEXT NOT NULL DEFAULT 'years',
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 7. Applications Table
CREATE TABLE IF NOT EXISTS applications (
    application_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    application_type TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processed_by TEXT,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 8. Slot Command Settings Table
CREATE TABLE IF NOT EXISTS slot_command_settings (
    slot_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    roster_cap INTEGER DEFAULT 25,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 9. Slot Demand Config Table
CREATE TABLE IF NOT EXISTS slot_demand_config (
    slot_id TEXT PRIMARY KEY,
    demands_enabled BOOLEAN DEFAULT false,
    demand_limit INTEGER DEFAULT 3,
    demand_channel TEXT,
    five_demands_role TEXT,
    four_demands_role TEXT,
    three_demands_role TEXT,
    two_demands_role TEXT,
    one_demand_role TEXT,
    no_demands_role TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 10. Game Times Table
CREATE TABLE IF NOT EXISTS gametimes (
    game_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    team1_role_id TEXT NOT NULL,
    team1_name TEXT NOT NULL,
    team1_emoji TEXT,
    team2_role_id TEXT NOT NULL,
    team2_name TEXT NOT NULL,
    team2_emoji TEXT,
    scheduled_time BIGINT NOT NULL,
    timezone TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    channel_id TEXT NOT NULL,
    message_id TEXT NOT NULL,
    created_at BIGINT NOT NULL,
    created_by TEXT NOT NULL,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 11. Disband Transactions Table
CREATE TABLE IF NOT EXISTS disband_transactions (
    disband_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    team_name TEXT NOT NULL,
    disbanded_by TEXT NOT NULL,
    disbanded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    moved_to_fa_role TEXT,
    affected_users TEXT, -- JSON array of user IDs
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- 12. Live Management Lists Table
CREATE TABLE IF NOT EXISTS live_management_lists (
    guild_id TEXT NOT NULL,
    channel_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    display_type TEXT NOT NULL,
    auto_update TEXT DEFAULT 'yes',
    include_empty TEXT DEFAULT 'yes',
    league_style TEXT DEFAULT 'nfl',
    secondary_label TEXT DEFAULT 'AD',
    message_ids TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    active TEXT DEFAULT 'yes',
    PRIMARY KEY (guild_id, channel_id, slot_id),
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_guild_settings_guild_id ON guild_settings(guild_id);
CREATE INDEX IF NOT EXISTS idx_slots_guild_id ON slots(guild_id);
CREATE INDEX IF NOT EXISTS idx_slot_configs_guild_id ON slot_configs(guild_id);
CREATE INDEX IF NOT EXISTS idx_league_teams_slot_id ON league_teams(slot_id);
CREATE INDEX IF NOT EXISTS idx_league_teams_role_id ON league_teams(role_id);
CREATE INDEX IF NOT EXISTS idx_transactions_guild_id ON transactions(guild_id);
CREATE INDEX IF NOT EXISTS idx_transactions_slot_id ON transactions(slot_id);
CREATE INDEX IF NOT EXISTS idx_contracts_guild_id ON contracts(guild_id);
CREATE INDEX IF NOT EXISTS idx_contracts_slot_id ON contracts(slot_id);
CREATE INDEX IF NOT EXISTS idx_contracts_player_id ON contracts(player_id);
CREATE INDEX IF NOT EXISTS idx_applications_guild_id ON applications(guild_id);
CREATE INDEX IF NOT EXISTS idx_applications_slot_id ON applications(slot_id);
CREATE INDEX IF NOT EXISTS idx_gametimes_guild_id ON gametimes(guild_id);
CREATE INDEX IF NOT EXISTS idx_gametimes_slot_id ON gametimes(slot_id);
CREATE INDEX IF NOT EXISTS idx_gametimes_status ON gametimes(status);
