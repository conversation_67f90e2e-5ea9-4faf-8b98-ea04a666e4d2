import asyncio
import sqlite3
import os
from supabase import create_client

# Configuration
SUPABASE_URL = "https://hzthhxpmbhbwyzkmtqng.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6dGhoeHBtYmhid3l6a210cW5nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQxODE1NzAsImV4cCI6MjA0OTc1NzU3MH0.Kqv5LN0hCRUhwOJFqJ4oDkNjD9zlKOgF8tLf6f66GKo"

async def run_manual_sync():
    """
    Run a manual sync to add missing columns to SQLite database
    """
    print("🔄 Starting manual Supabase-to-SQLite sync...")
    
    # Initialize Supabase client
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Create SQLite connection
    local_conn = sqlite3.connect('transaction_bot.db', timeout=30.0)
    local_conn.execute("PRAGMA busy_timeout = 30000")
    local_cursor = local_conn.cursor()
    
    try:
        # Check and add missing columns
        print("🔧 Checking for missing columns...")
        
        # Check guild_settings for transaction_log_channel
        try:
            local_cursor.execute("SELECT transaction_log_channel FROM guild_settings LIMIT 1")
            print("✅ transaction_log_channel column already exists in guild_settings")
        except sqlite3.OperationalError:
            print("🔧 Adding transaction_log_channel column to guild_settings...")
            local_cursor.execute("ALTER TABLE guild_settings ADD COLUMN transaction_log_channel INTEGER")
            local_conn.commit()
            print("✅ Successfully added transaction_log_channel column")
        
        # Check slot_configs for weekly_games_channel
        try:
            local_cursor.execute("SELECT weekly_games_channel FROM slot_configs LIMIT 1")
            print("✅ weekly_games_channel column already exists in slot_configs")
        except sqlite3.OperationalError:
            print("🔧 Adding weekly_games_channel column to slot_configs...")
            local_cursor.execute("ALTER TABLE slot_configs ADD COLUMN weekly_games_channel INTEGER")
            local_conn.commit()
            print("✅ Successfully added weekly_games_channel column")
        
        # Also add missing columns from Supabase schema
        try:
            local_cursor.execute("SELECT score_channel FROM slot_configs LIMIT 1")
            print("✅ score_channel column already exists in slot_configs")
        except sqlite3.OperationalError:
            print("🔧 Adding score_channel column to slot_configs...")
            local_cursor.execute("ALTER TABLE slot_configs ADD COLUMN score_channel INTEGER")
            local_conn.commit()
            print("✅ Successfully added score_channel column")
        
        try:
            local_cursor.execute("SELECT created_at FROM slot_configs LIMIT 1")
            print("✅ created_at column already exists in slot_configs")
        except sqlite3.OperationalError:
            print("🔧 Adding created_at column to slot_configs...")
            local_cursor.execute("ALTER TABLE slot_configs ADD COLUMN created_at TEXT")
            local_conn.commit()
            print("✅ Successfully added created_at column")
        
        try:
            local_cursor.execute("SELECT updated_at FROM slot_configs LIMIT 1")
            print("✅ updated_at column already exists in slot_configs")
        except sqlite3.OperationalError:
            print("🔧 Adding updated_at column to slot_configs...")
            local_cursor.execute("ALTER TABLE slot_configs ADD COLUMN updated_at TEXT")
            local_conn.commit()
            print("✅ Successfully added updated_at column")
        
        # Check teams table for guild_id if it doesn't exist
        try:
            local_cursor.execute("SELECT guild_id FROM teams LIMIT 1")
            print("✅ guild_id column already exists in teams")
        except sqlite3.OperationalError:
            print("🔧 Adding guild_id column to teams...")
            local_cursor.execute("ALTER TABLE teams ADD COLUMN guild_id INTEGER")
            local_conn.commit()
            print("✅ Successfully added guild_id column to teams")
        
        print("\n🔍 Running a test sync of guild settings...")
        # Try to sync one guild setting to test the migration
        guild_settings = supabase.table('guild_settings').select('*').limit(1).execute()
        if guild_settings.data:
            setting = guild_settings.data[0]
            guild_id = setting.get('guild_id')
            if guild_id:
                try:
                    local_cursor.execute("""
                        INSERT OR REPLACE INTO guild_settings 
                        (guild_id, admin_role, log_channel, application_channel, 
                         suspended_role, suspended_channel, application_blacklist_role, transaction_log_channel)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        guild_id,
                        setting.get('admin_role'),
                        setting.get('log_channel'),
                        setting.get('application_channel'),
                        setting.get('suspended_role'),
                        setting.get('suspended_channel'),
                        setting.get('application_blacklist_role'),
                        setting.get('transaction_log_channel')
                    ))
                    local_conn.commit()
                    print(f"✅ Successfully synced guild settings for guild {guild_id}")
                except Exception as e:
                    print(f"❌ Failed to sync guild settings: {e}")
        
        print("\n🔍 Running a test sync of slot configs...")
        # Try to sync one slot config to test the migration
        slot_configs = supabase.table('slot_configs').select('*').limit(1).execute()
        if slot_configs.data:
            config = slot_configs.data[0]
            try:
                local_cursor.execute("""
                    INSERT OR REPLACE INTO slot_configs 
                    (slot_id, franchise_owner_role, general_manager_role, head_coach_role,
                     assistant_coach_role, free_agent_role, trade_channel, transaction_channel,
                     score_channel, weekly_games_channel, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    config['slot_id'],
                    config.get('franchise_owner_role'),
                    config.get('general_manager_role'),
                    config.get('head_coach_role'),
                    config.get('assistant_coach_role'),
                    config.get('free_agent_role'),
                    config.get('trade_channel'),
                    config.get('transaction_channel'),
                    config.get('score_channel'),
                    config.get('weekly_games_channel'),
                    config.get('created_at'),
                    config.get('updated_at')
                ))
                local_conn.commit()
                print(f"✅ Successfully synced slot config for slot {config['slot_id']}")
            except Exception as e:
                print(f"❌ Failed to sync slot config: {e}")
        
        local_conn.commit()
        print("\n✅ Manual sync completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during sync: {e}")
        local_conn.rollback()
    finally:
        local_conn.close()

if __name__ == "__main__":
    asyncio.run(run_manual_sync())
