#!/usr/bin/env python3
"""
Check Current Supabase Database Status
"""

def check_database_status():
    try:
        from supabase import create_client
        
        SUPABASE_URL = "https://ueheoabegwccfkholyku.supabase.co"
        SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlaGVvYWJlZ3djY2ZraG9seWt1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDM0MDEsImV4cCI6MjA2NzUxOTQwMX0.puxraTE7UGm9wkfqQvTxIp7NAINODFhMOXmgFpRPBKo"
        
        print("🔗 Connecting to Supabase...")
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Check teams
        print("🏈 Checking league_teams table...")
        teams_response = supabase.table('league_teams').select('*').execute()
        teams = teams_response.data
        
        print(f"📊 Total teams in database: {len(teams)}")
        
        if len(teams) > 0:
            print("\n📋 Sample teams:")
            for i, team in enumerate(teams[:10]):  # Show first 10
                print(f"  {i+1}. {team.get('team_name', 'Unknown')} (Role ID: {team.get('role_id', 'Unknown')})")
            
            if len(teams) > 10:
                print(f"  ... and {len(teams) - 10} more teams")
        else:
            print("❌ No teams found in database!")
            print("\nThis means either:")
            print("1. The duplicate removal script deleted everything (oops!)")
            print("2. The database was already empty")
            print("\n🔧 To fix this, you should:")
            print("1. Run /autosetup in Discord to recreate your teams")
            print("2. Or restore from a backup if you have one")
        
        # Check slots
        print("\n🎰 Checking slots table...")
        slots_response = supabase.table('slots').select('*').execute()
        slots = slots_response.data
        print(f"📊 Total slots: {len(slots)}")
        
        # Check configs  
        print("\n⚙️ Checking slot_configs table...")
        configs_response = supabase.table('slot_configs').select('*').execute()
        configs = configs_response.data
        print(f"📊 Total configs: {len(configs)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🔍 Supabase Database Status Check")
    print("=" * 40)
    check_database_status()
