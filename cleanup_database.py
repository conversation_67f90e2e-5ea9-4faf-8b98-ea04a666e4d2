#!/usr/bin/env python3
"""
Database cleanup script to fix role ID and guild ID issues
"""

import sqlite3
import os
import re

DB_FILE = "transaction_bot.db"

def cleanup_database():
    """Clean up invalid data in the database"""
    if not os.path.exists(DB_FILE):
        print(f"Database file {DB_FILE} not found!")
        return
    
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    print("🧹 Starting database cleanup...")
    
    try:
        # 1. Remove league_teams entries with invalid role_ids (non-numeric)
        print("1. Cleaning invalid role_ids in league_teams...")
        cursor.execute("DELETE FROM league_teams WHERE role_id NOT GLOB '[0-9]*' OR role_id = ''")
        deleted_teams = cursor.rowcount
        print(f"   Removed {deleted_teams} invalid team entries")
        
        # 2. Clean invalid role IDs in slot_configs
        print("2. Cleaning invalid role IDs in slot_configs...")
        
        role_fields = [
            'franchise_owner_role',
            'general_manager_role', 
            'head_coach_role',
            'assistant_coach_role'
        ]
        
        for field in role_fields:
            cursor.execute(f"UPDATE slot_configs SET {field} = NULL WHERE {field} NOT GLOB '[0-9]*' OR {field} = ''")
            updated = cursor.rowcount
            print(f"   Cleaned {updated} invalid {field} entries")
        
        # 3. Add guild_id to league_teams if missing (use a default or remove entries)
        print("3. Checking guild_id in league_teams...")
        cursor.execute("PRAGMA table_info(league_teams)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'guild_id' not in columns:
            print("   Adding guild_id column to league_teams...")
            cursor.execute("ALTER TABLE league_teams ADD COLUMN guild_id INTEGER")
        
        # Remove entries without guild_id (they're orphaned)
        cursor.execute("DELETE FROM league_teams WHERE guild_id IS NULL")
        orphaned = cursor.rowcount
        print(f"   Removed {orphaned} orphaned team entries without guild_id")
        
        # 4. Verify data integrity
        print("4. Verifying data integrity...")
        cursor.execute("SELECT COUNT(*) FROM league_teams")
        teams_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM slot_configs")
        configs_count = cursor.fetchone()[0]
        
        print(f"   Final counts: {teams_count} teams, {configs_count} slot configs")
        
        conn.commit()
        print("✅ Database cleanup completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    cleanup_database()
