@app_commands.choices(league_type=[
    app_commands.Choice(name="All", value="all"),  # Added "All" option
    app_commands.Choice(name="Generic", value="generic"),
    app_commands.Choice(name="NFL", value="nfl"),  # Removed redundant "Football"
    app_commands.Choice(name="Basketball", value="basketball")
])
@app_commands.choices(sort_by=[
    app_commands.Choice(name="Alphabetical", value="alpha"),
    app_commands.Choice(name="Member Count (High to Low)", value="members_desc"),
    app_commands.Choice(name="Member Count (Low to High)", value="members_asc")
])
async def list_teams(
    interaction: discord.Interaction, 
    league_type: app_commands.Choice[str],  # Made required
    public: bool = False,
    sort_by: app_commands.Choice[str] = None
):