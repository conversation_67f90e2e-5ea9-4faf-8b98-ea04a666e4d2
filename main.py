import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Bot configuration
DISCORD_TOKEN = os.getenv("DISCORD_TOKEN")

# Create bot with necessary intents
intents = discord.Intents.default()
intents.message_content = True
intents.members = True  # Required for role-related functionality
bot = commands.Bot(command_prefix='!', intents=intents)

# List of cogs to load
COGS = [
    'role_autocomplete',
    'role_select_ui',
    'realtime_role_search'
]

@bot.event
async def on_ready():
    print(f"Logged in as {bot.user.name} (ID: {bot.user.id})")

    # Load all cogs
    for cog in COGS:
        try:
            await bot.load_extension(cog)
            print(f"Loaded extension: {cog}")
        except Exception as e:
            print(f"Failed to load extension {cog}: {e}")

    # Sync commands with Discord
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")

@bot.tree.command(name="ping", description="Check if the bot is responsive")
async def ping(interaction: discord.Interaction):
    await interaction.response.send_message("Pong! Bot is online and responsive.")

# Run the bot
if __name__ == "__main__":
    bot.run(DISCORD_TOKEN)
