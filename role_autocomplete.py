import discord
from discord import app_commands
from discord.ext import commands
from typing import List, Optional

class RoleAutocomplete(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    # Autocomplete function for roles
    async def role_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        # Get all roles in the guild
        roles = interaction.guild.roles
        
        # Filter roles based on the current input (case-insensitive)
        if current:
            matching_roles = [
                role for role in roles 
                if current.lower() in role.name.lower() and not role.is_default()
            ]
        else:
            # If no input yet, return some roles (excluding @everyone)
            matching_roles = [role for role in roles if not role.is_default()][:25]
        
        # Sort by relevance (exact matches first, then starts with, then contains)
        exact_matches = [role for role in matching_roles if role.name.lower() == current.lower()]
        starts_with = [role for role in matching_roles if role.name.lower().startswith(current.lower()) and role not in exact_matches]
        contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
        
        sorted_roles = exact_matches + starts_with + contains
        
        # Return up to 25 choices (Discord's limit)
        return [
            app_commands.Choice(name=role.name, value=str(role.id))
            for role in sorted_roles[:25]
        ]

    # Command that uses the autocomplete
    @app_commands.command(name="mention_role", description="Mention a role by typing its name")
    @app_commands.autocomplete(role=role_autocomplete)
    async def mention_role(self, interaction: discord.Interaction, role: str):
        # Get the role from the ID
        role_obj = interaction.guild.get_role(int(role))
        
        if role_obj:
            await interaction.response.send_message(f"You selected: {role_obj.mention}")
        else:
            await interaction.response.send_message("Role not found.", ephemeral=True)

    # Command to send a message with role mention
    @app_commands.command(name="send_with_role", description="Send a message with a role mention")
    @app_commands.autocomplete(role=role_autocomplete)
    async def send_with_role(self, interaction: discord.Interaction, role: str, message: str):
        # Get the role from the ID
        role_obj = interaction.guild.get_role(int(role))
        
        if role_obj:
            await interaction.response.send_message(f"{message} {role_obj.mention}")
        else:
            await interaction.response.send_message("Role not found.", ephemeral=True)

async def setup(bot):
    await bot.add_cog(RoleAutocomplete(bot))
