import sqlite3
import os
import traceback
import re
import unicodedata
import uuid # Import uuid for generating unique slot IDs

from discord import ui, Embed, SelectOption
from discord.ext import commands
import discord
from discord import app_commands
import asyncio
from typing import Optional, Any, Dict, List, Tuple

# Define the main bot class
class TransactionBot(commands.Bot):
    """
    A Discord bot for managing league transactions, team settings, and server configuration.
    It uses SQLite for persistent storage of server settings, team information, and transactions.
    Now supports multiple "slots" per guild for different league configurations.
    """
    def __init__(self):
        # Configure intents for the bot
        intents = discord.Intents.default()
        intents.members = True # Essential for role and member management
        intents.message_content = True # Not strictly needed for slash commands

        # Initialize the commands.Bot with a command prefix and intents
        super().__init__(command_prefix='/', intents=intents)

        # Initialize database connection and cursor to None
        self.conn = None
        self.cursor = None
        # Set up the database tables when the bot initializes
        self.setup_database()

        # Cache for global guild settings to reduce DB reads
        self._global_guild_settings_cache = {} # guild_id: {setting_name: value}

    async def setup_hook(self):
        """
        Called once the bot is ready. Used to sync application commands (slash commands)
        with Discord.
        """
        print("Attempting to sync application commands...")
        await self.tree.sync()
        print("Application commands synced.")

    def setup_database(self):
        """
        Establishes a connection to the SQLite database and creates necessary tables
        if they do not already exist. This includes tables for guild global configuration,
        slots, slot-specific configurations, teams, transactions, applications, and league teams.
        It also handles adding new columns to existing tables and recreating tables if schema
        inconsistencies (like missing PRIMARY KEY) are detected for new slot-related tables.
        """
        if self.conn:
            self.conn.close()
            print("Closed existing database connection.")

        db_path = 'transaction_bot.db'
        db_exists = os.path.exists(db_path)
        if not db_exists:
            print(f"Database file '{db_path}' not found. It will be created.")
        else:
            print(f"Connecting to existing database file: '{db_path}'.")

        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()

        # -----------------------------------------------------------
        # 1. Guild Global Settings (replaces old server_config for global aspects)
        # These settings are per guild, not per slot.
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS guild_settings (
            guild_id INTEGER PRIMARY KEY,
            admin_role INTEGER,
            log_channel INTEGER,
            application_channel INTEGER,
            suspended_role INTEGER,
            suspended_channel INTEGER,
            application_blacklist_role INTEGER
        )''')

        # Add new columns to guild_settings if they don't exist
        guild_settings_columns_to_add = {
            "admin_role": "INTEGER",
            "log_channel": "INTEGER",
            "application_channel": "INTEGER",
            "suspended_role": "INTEGER",
            "suspended_channel": "INTEGER",
            "application_blacklist_role": "INTEGER"
        }
        self.cursor.execute("PRAGMA table_info(guild_settings)")
        existing_guild_settings_columns = [column[1] for column in self.cursor.fetchall()]
        for col_name, col_type in guild_settings_columns_to_add.items():
            if col_name not in existing_guild_settings_columns:
                try:
                    self.cursor.execute(f"ALTER TABLE guild_settings ADD COLUMN {col_name} {col_type}")
                    print(f"Added column {col_name} to guild_settings.")
                except sqlite3.OperationalError as e:
                    print(f"Could not add column {col_name} to guild_settings: {e}")

        # -----------------------------------------------------------
        # 2. Slots Table
        # Stores information about each defined slot within a guild.
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS slots (
            slot_id TEXT PRIMARY KEY,
            guild_id INTEGER NOT NULL,
            slot_name TEXT NOT NULL,
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(guild_id, slot_name)
        )''')

        # -----------------------------------------------------------
        # 3. Slot-Specific Configurations (new tables using slot_id as PK)

        # Helper to check and recreate table if PK is not 'slot_id'
        def check_and_recreate_slot_table(table_name: str, schema_sql: str):
            self.cursor.execute(f"PRAGMA table_info({table_name})")
            table_info = self.cursor.fetchall()
            recreate = False

            if table_info: # Table exists
                # Check if 'slot_id' is defined as the PRIMARY KEY
                pk_cols = [col[1] for col in table_info if col[5] == 1] # col[5] is 'pk' flag
                if not (len(pk_cols) == 1 and pk_cols[0] == 'slot_id'):
                    print(f"Detected old '{table_name}' schema or 'slot_id' not primary key. Dropping and recreating '{table_name}' table.")
                    recreate = True
            else: # Table does not exist
                recreate = True

            if recreate:
                self.cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
                self.cursor.execute(schema_sql)
            else:
                print(f"Table '{table_name}' schema is consistent.")

        # slot_configs
        slot_configs_schema = '''
        CREATE TABLE slot_configs (
            slot_id TEXT PRIMARY KEY,
            franchise_owner_role INTEGER,
            general_manager_role INTEGER,
            head_coach_role INTEGER,
            assistant_coach_role INTEGER,
            transaction_channel INTEGER,
            free_agent_role INTEGER,
            streamer_role INTEGER,
            referee_role INTEGER,
            gametime_channel INTEGER,
            trade_channel INTEGER,
            draft_channel INTEGER,
            pickup_host_role INTEGER,
            pickup_channel INTEGER,
            FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
        )'''
        check_and_recreate_slot_table('slot_configs', slot_configs_schema)

        # slot_command_settings
        slot_command_settings_schema = '''
        CREATE TABLE slot_command_settings (
            slot_id TEXT PRIMARY KEY,
            sign_enabled TEXT DEFAULT 'on',
            release_enabled TEXT DEFAULT 'on',
            offer_enabled TEXT DEFAULT 'on',
            roster_cap INTEGER DEFAULT 25,
            FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
        )'''
        check_and_recreate_slot_table('slot_command_settings', slot_command_settings_schema)

        # slot_demand_config
        slot_demand_config_schema = '''
        CREATE TABLE slot_demand_config (
            slot_id TEXT PRIMARY KEY,
            demands_enabled TEXT DEFAULT 'off',
            demand_limit TEXT DEFAULT 'false',
            demand_channel INTEGER,
            five_demands_role INTEGER,
            four_demands_role INTEGER,
            three_demands_role INTEGER,
            two_demands_role INTEGER,
            one_demand_role INTEGER,
            no_demands_role INTEGER,
            FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
        )'''
        check_and_recreate_slot_table('slot_demand_config', slot_demand_config_schema)


        # -----------------------------------------------------------
        # 4. Update Existing Transaction/Team Tables to include slot_id

        # teams table - MAJOR CHANGE: Adding slot_id to PRIMARY KEY.
        # This part of the code remains similar to previous version, as its logic was okay.
        self.cursor.execute("PRAGMA table_info(teams)")
        teams_columns = [column[1] for column in self.cursor.fetchall()]
        recreate_teams_table = False
        if teams_columns: # Table exists
            self.cursor.execute("PRAGMA index_list(teams)")
            pk_index_name = None
            for idx in self.cursor.fetchall():
                if idx[2] == 1: # is_unique, means it's likely a PK or unique index
                    self.cursor.execute(f"PRAGMA index_info({idx[1]})")
                    pk_cols_info = self.cursor.fetchall()
                    current_pk_cols = [col[2] for col in pk_cols_info if col[1] is not None] # col[1] is `cid` which is not useful, col[2] is `name`
                    if not (len(current_pk_cols) == 2 and 'slot_id' in current_pk_cols and 'team_name' in current_pk_cols):
                        recreate_teams_table = True
        else: # Table does not exist
            recreate_teams_table = True

        if recreate_teams_table:
            print("Detected old 'teams' schema or incorrect primary key. Dropping and recreating 'teams' table.")
            self.cursor.execute("DROP TABLE IF EXISTS teams")
            self.cursor.execute('''
            CREATE TABLE teams (
                slot_id TEXT NOT NULL,
                team_name TEXT NOT NULL,
                team_role INTEGER,
                team_emoji TEXT,
                league_type TEXT,
                PRIMARY KEY (slot_id, team_name),
                FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
            )''')

        # transactions table - Adding slot_id column.
        self.cursor.execute("PRAGMA table_info(transactions)")
        transactions_columns = [column[1] for column in self.cursor.fetchall()]
        if 'slot_id' not in transactions_columns:
            print("Altering 'transactions' table to add 'slot_id' column.")
            self.cursor.execute("ALTER TABLE transactions ADD COLUMN slot_id TEXT")
            self.cursor.execute("UPDATE transactions SET slot_id = 'migrated_default' WHERE slot_id IS NULL")
        
        # applications table - Adding slot_id column.
        self.cursor.execute("PRAGMA table_info(applications)")
        applications_columns = [column[1] for column in self.cursor.fetchall()]
        if 'slot_id' not in applications_columns:
            print("Altering 'applications' table to add 'slot_id' column.")
            self.cursor.execute("ALTER TABLE applications ADD COLUMN slot_id TEXT")
            self.cursor.execute("UPDATE applications SET slot_id = 'migrated_default' WHERE slot_id IS NULL")

        # league_teams table - MAJOR CHANGE: Adding slot_id to PRIMARY KEY.
        self.cursor.execute("PRAGMA table_info(league_teams)")
        league_teams_info = self.cursor.fetchall()
        recreate_league_teams = False
        if league_teams_info: # Table exists
            self.cursor.execute("PRAGMA index_list(league_teams)")
            pk_index_name = None
            for idx in self.cursor.fetchall():
                if idx[2] == 1: # is_unique
                    self.cursor.execute(f"PRAGMA index_info({idx[1]})")
                    pk_cols_info = self.cursor.fetchall()
                    current_pk_cols = [col[2] for col in pk_cols_info if col[1] is not None] # col[2] is `name`
                    if not (len(current_pk_cols) == 2 and 'role_id' in current_pk_cols and 'slot_id' in current_pk_cols):
                        recreate_league_teams = True
        else: # Table does not exist
            recreate_league_teams = True

        if recreate_league_teams:
            print("Detected old 'league_teams' schema or incorrect primary key. Dropping and recreating 'league_teams' table.")
            self.cursor.execute("DROP TABLE IF EXISTS league_teams")
            self.cursor.execute('''
            CREATE TABLE league_teams (
                role_id TEXT NOT NULL,
                slot_id TEXT NOT NULL,
                team_name TEXT NOT NULL,
                emoji TEXT NOT NULL,
                PRIMARY KEY (role_id, slot_id),
                FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
            )''')
        else:
            print("Table 'league_teams' schema is consistent.")

        # Clean up old configuration tables if they still exist
        old_tables_to_drop = ['server_config', 'command_settings', 'demand_config']
        for old_table in old_tables_to_drop:
            self.cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{old_table}';")
            if self.cursor.fetchone():
                print(f"Dropping old '{old_table}' table.")
                self.cursor.execute(f"DROP TABLE {old_table}")

        self.conn.commit()
        print("Database tables created/verified/updated.")
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = self.cursor.fetchall()
        print(f"Current Database tables: {tables}")

    async def get_guild_settings(self, guild_id: int) -> Dict[str, Any]:
        """Fetches and caches global guild settings."""
        if guild_id not in self._global_guild_settings_cache:
            self.cursor.execute("SELECT * FROM guild_settings WHERE guild_id = ?", (guild_id,))
            row = self.cursor.fetchone()
            if row:
                columns = [desc[0] for desc in self.cursor.description]
                self._global_guild_settings_cache[guild_id] = dict(zip(columns, row))
            else:
                self._global_guild_settings_cache[guild_id] = {} # Empty dict if no settings found
        return self._global_guild_settings_cache.get(guild_id, {})

    async def update_guild_setting(self, guild_id: int, key: str, value: Any):
        """Updates a single global guild setting and refreshes cache."""
        try:
            self.cursor.execute("INSERT OR IGNORE INTO guild_settings (guild_id) VALUES (?)", (guild_id,))
            self.cursor.execute(f"UPDATE guild_settings SET {key} = ? WHERE guild_id = ?", (value, guild_id))
            self.conn.commit()
            if guild_id in self._global_guild_settings_cache:
                del self._global_guild_settings_cache[guild_id] # Invalidate cache
            print(f"Autosaved guild_setting: {key} = {value} for guild {guild_id}")
        except Exception as e:
            print(f"Error autosaving guild_setting for {key}: {e}")
            traceback.print_exc()

# Create bot instance
bot = TransactionBot()

# --- Permission Helper ---
async def has_bot_management_permission(interaction: discord.Interaction) -> bool:
    """
    Checks if the user has server administrator permissions OR the configured admin_role from guild_settings.
    """
    if interaction.user.guild_permissions.administrator:
        return True

    guild_settings = await bot.get_guild_settings(interaction.guild_id)
    admin_role_id = guild_settings.get('admin_role')

    if admin_role_id and interaction.guild:
        admin_role_obj = interaction.guild.get_role(admin_role_id)
        if admin_role_obj and admin_role_obj in interaction.user.roles:
            return True
    return False

# --- Logging Helper ---
async def _log_setting_change(
    guild: discord.Guild,
    editor: discord.User,
    setting_name: str,
    old_value: Any,
    new_value: Any,
    is_role: bool = False,
    is_channel: bool = False,
    is_toggle: bool = False,
    slot_id: Optional[str] = None,
    slot_name: Optional[str] = None
):
    """
    Logs a setting change to the configured log channel from guild_settings.
    Includes slot information if applicable.
    """
    if guild is None:
        print("Error: Guild object is None in _log_setting_change")
        return

    guild_settings = await bot.get_guild_settings(guild.id) # Corrected: Added 'bot.'
    log_channel_id = guild_settings.get('log_channel')

    if not log_channel_id:
        return # No log channel configured

    log_channel = guild.get_channel(log_channel_id)
    if not log_channel or not isinstance(log_channel, discord.TextChannel):
        print(f"Log channel with ID {log_channel_id} not found or not a text channel in guild {guild.id}.")
        return

    def format_value(val, is_r, is_c, is_t):
        if val is None:
            return "❌ Not set"
        if is_r:
            role = guild.get_role(int(val))
            return role.mention if role else f"Role ID: {val} (Not Found)"
        if is_c:
            channel = guild.get_channel(int(val))
            return channel.mention if channel else f"Channel ID: {val} (Not Found)"
        if is_t:
            if isinstance(val, str):
                if val.lower() in ['on', 'true']:
                    return "🟢 Enabled"
                elif val.lower() in ['off', 'false']:
                    return "🔴 Disabled"
            return str(val)
        return str(val)

    old_value_str = format_value(old_value, is_role, is_channel, is_toggle)
    new_value_str = format_value(new_value, is_role, is_channel, is_toggle)

    if old_value == new_value:
        return

    title_suffix = ""
    if slot_id:
        # Fetch slot_name from DB if not provided, for better log readability
        if not slot_name:
            bot.cursor.execute("SELECT slot_name FROM slots WHERE slot_id = ?", (slot_id,))
            fetched_slot = bot.cursor.fetchone()
            slot_name = fetched_slot[0] if fetched_slot else slot_id[:8]
        title_suffix = f" (Slot: {slot_name})"


    embed = discord.Embed(
        title=f"⚙️ Setting Changed{title_suffix}",
        description=f"**{setting_name}** was updated by {editor.mention}.",
        color=discord.Color.orange(),
        timestamp=discord.utils.utcnow()
    )
    embed.set_author(name=editor.display_name, icon_url=editor.avatar.url if editor.avatar else None)
    embed.add_field(name="Old Value", value=old_value_str, inline=False)
    embed.add_field(name="New Value", value=new_value_str, inline=False)
    embed.set_footer(text=f"User ID: {editor.id}")

    try:
        await log_channel.send(embed=embed)
    except discord.Forbidden:
        print(f"Bot lacks permission to send messages in log channel {log_channel.name} (ID: {log_channel_id}) for guild {guild.id}.")
    except Exception as e:
        print(f"Error sending log message to channel {log_channel_id}: {e}")
        traceback.print_exc()

# --- UI Components for GlobalSetupView ---
class GlobalRoleSelect(discord.ui.RoleSelect):
    def __init__(self, custom_id: str, placeholder: str, friendly_name: str):
        super().__init__(placeholder=placeholder, min_values=1, max_values=1, custom_id=custom_id)
        self.friendly_name = friendly_name

    async def callback(self, interaction: discord.Interaction):
        view: GlobalSetupView = self.view
        selected_role_id = self.values[0].id
        
        old_value_id = view.config.get(self.custom_id)
        view.config[self.custom_id] = selected_role_id

        await view.bot.update_guild_setting(interaction.guild_id, self.custom_id, selected_role_id)
        await _log_setting_change(interaction.guild, interaction.user, self.friendly_name, old_value_id, selected_role_id, is_role=True)
        await view.update_message(interaction)

class GlobalChannelSelect(discord.ui.ChannelSelect):
    def __init__(self, custom_id: str, placeholder: str, friendly_name: str):
        super().__init__(
            channel_types=[discord.ChannelType.text],
            placeholder=placeholder,
            min_values=1,
            max_values=1,
            custom_id=custom_id
        )
        self.friendly_name = friendly_name

    async def callback(self, interaction: discord.Interaction):
        view: GlobalSetupView = self.view
        selected_channel_id = self.values[0].id
        
        old_value_id = view.config.get(self.custom_id)
        view.config[self.custom_id] = selected_channel_id

        await view.bot.update_guild_setting(interaction.guild_id, self.custom_id, selected_channel_id)
        await _log_setting_change(interaction.guild, interaction.user, self.friendly_name, old_value_id, selected_channel_id, is_channel=True)
        await view.update_message(interaction)

class GlobalSetupView(discord.ui.View):
    def __init__(self, bot_instance, guild_id: int, original_initiator: discord.User):
        super().__init__(timeout=600)
        self.bot = bot_instance
        self.guild_id = guild_id
        self.original_initiator = original_initiator # Store the user who initiated the command
        self.current_page = 0
        self.config = {}
        self.total_pages = 3 # Welcome + Roles + Channels
        asyncio.create_task(self.load_existing_configs()) # Load configs asynchronously

    async def load_existing_configs(self):
        print(f"Loading existing global configurations for guild ID: {self.guild_id}")
        self.config = await self.bot.get_guild_settings(self.guild_id)
        # Ensure all expected keys are present, even if None
        global_config_keys = [
            'admin_role', 'log_channel', 'application_channel',
            'suspended_role', 'suspended_channel', 'application_blacklist_role'
        ]
        for key in global_config_keys:
            self.config.setdefault(key, None)
        print(f"Loaded global config: {self.config}")

    def _get_footer_text(self):
        return f"Page {self.current_page + 1}/{self.total_pages}"

    def create_welcome_embed(self):
        embed = discord.Embed(
            title="Welcome to RosterFlow - Global Setup!",
            description=(
                "Let's configure the essential, server-wide settings for RosterFlow.\n"
                "These settings apply across all 'slots' you might create.\n\n"
                "This wizard will guide you through:\n"
                "🔑 Bot Admin Role (Optional)\n"
                "📜 Setup Log Channel (Optional)\n"
                "🛡️ Global Suspension Roles/Channels\n"
                "📝 Global Application Blacklist Role/Channel\n\n"
                "Click 'Next' to begin."
            ),
            color=discord.Color.blue()
        )
        embed.set_footer(text=self._get_footer_text() + " - Welcome")
        return embed

    def create_global_roles_embed(self):
        embed = discord.Embed(
            title=f"RosterFlow - Global Roles Setup ({self._get_footer_text()})",
            description=(
                "Configure roles that have special, global permissions or status.\n"
                "Selections are autosaved."
            ),
            color=discord.Color.blue()
        )
        guild = self.bot.get_guild(self.guild_id)
        if not guild: return embed

        roles_to_display = [
            ("Bot Admin Role", "admin_role", "🔑 Can manage all bot settings globally and for all slots."),
            ("Application Blacklist Role", "application_blacklist_role", "🚫 Users with this role cannot submit applications."),
            ("Suspended Role", "suspended_role", "🚫 Role assigned to suspended players.")
        ]
        for name, key, desc in roles_to_display:
            role_obj = guild.get_role(self.config.get(key)) if self.config.get(key) else None
            value = f"Set to: {role_obj.mention if role_obj else '❌ Not set'}"
            embed.add_field(name=name, value=value, inline=False)
        return embed

    def create_global_channels_embed(self):
        embed = discord.Embed(
            title=f"RosterFlow - Global Channels Setup ({self._get_footer_text()})",
            description=(
                "Configure channels for global logging and application submissions.\n"
                "Selections are autosaved."
            ),
            color=discord.Color.blue()
        )
        guild = self.bot.get_guild(self.guild_id)
        if not guild: return embed

        channels_to_display = [
            ("Setup Log Channel", "log_channel", "📜 Where RosterFlow logs configuration changes."),
            ("Application Submission Channel", "application_channel", "📝 Where all new applications are posted."),
            ("Suspended Players Channel", "suspended_channel", "🚫 Channel where suspended players are logged.")
        ]
        for name, key, desc in channels_to_display:
            channel_obj = guild.get_channel(self.config.get(key)) if self.config.get(key) else None
            value = f"Set to: {channel_obj.mention if channel_obj else '❌ Not set'}"
            embed.add_field(name=name, value=value, inline=False)
        return embed

    async def update_message(self, interaction: discord.Interaction, initial_send: bool = False):
        self.clear_items()
        embed = None
        
        # Ensure config is loaded before trying to access it
        # This will block briefly until load_existing_configs is done
        if not self.config:
            await self.load_existing_configs()

        current_page_for_footer = self.current_page

        if self.current_page == 0:
            embed = self.create_welcome_embed()
            self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
        elif self.current_page == 1:
            embed = self.create_global_roles_embed()
            self.add_item(GlobalRoleSelect(custom_id="admin_role", placeholder="Select Bot Admin Role (Optional)", friendly_name="Bot Admin Role"))
            self.add_item(GlobalRoleSelect(custom_id="application_blacklist_role", placeholder="Select Application Blacklist Role (Optional)", friendly_name="Application Blacklist Role"))
            self.add_item(GlobalRoleSelect(custom_id="suspended_role", placeholder="Select Suspended Role (Optional)", friendly_name="Suspended Role"))
            self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        elif self.current_page == 2:
            embed = self.create_global_channels_embed()
            self.add_item(GlobalChannelSelect(custom_id="log_channel", placeholder="Select Setup Log Channel (Optional)", friendly_name="Setup Log Channel"))
            self.add_item(GlobalChannelSelect(custom_id="application_channel", placeholder="Select Application Submission Channel", friendly_name="Application Submission Channel"))
            self.add_item(GlobalChannelSelect(custom_id="suspended_channel", placeholder="Select Suspended Players Channel", friendly_name="Suspended Channel"))
            self.add_item(discord.ui.Button(label="Complete Global Setup", style=discord.ButtonStyle.success, custom_id="complete_setup"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        else: # Completion page or out of bounds
            success = True # Assume success for global setup confirmation
            final_embed = discord.Embed(
                title="✅ RosterFlow Global Setup Complete",
                description=(
                    "All essential global settings have been saved successfully!\n\n"
                    "**What's Next?**\n"
                    "• Now you can create and configure individual 'slots' using `/create_slot` and `/setup <slot_name>`.\n"
                    "• Use `/help` for all available commands."
                ),
                color=discord.Color.green()
            )
            final_embed.set_footer(text="Thank you for choosing RosterFlow!")
            # Use edit_original_response if already deferred, otherwise send_message
            if interaction.response.is_done():
                await interaction.edit_original_response(embed=final_embed, view=None)
            else:
                await interaction.response.send_message(embed=final_embed, view=None, ephemeral=True)
            return

        if embed and hasattr(embed, 'footer') and embed.footer and embed.footer.text:
            if not f"Page {current_page_for_footer + 1}/{self.total_pages}" in embed.footer.text:
                embed.set_footer(text=f"Page {current_page_for_footer + 1}/{self.total_pages} - {embed.footer.text.split(' - ')[-1] if ' - ' in embed.footer.text else ''}")
        elif embed:
            embed.set_footer(text=f"Page {current_page_for_footer + 1}/{self.total_pages}")

        if initial_send:
            # If this is the initial send AND the interaction hasn't been responded to yet
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, view=self, ephemeral=True)
            else:
                # If already deferred, use followup.send
                await interaction.followup.send(embed=embed, view=self, ephemeral=True)
        else:
            # For subsequent updates, always edit the original message
            if not interaction.response.is_done():
                await interaction.response.edit_message(embed=embed, view=self)
            else:
                await interaction.edit_original_response(embed=embed, view=self)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # Check if the user interacting is the original initiator
        if interaction.user != self.original_initiator:
            await interaction.response.send_message("You are not the one who started this setup!", ephemeral=True)
            return False
        
        custom_id = interaction.data.get("custom_id")
        if interaction.data.get("component_type") == 2: # Button component type
            if custom_id == "next_page":
                self.current_page += 1
                await self.update_message(interaction)
                return True
            elif custom_id == "prev_page":
                self.current_page = max(0, self.current_page - 1)
                await self.update_message(interaction)
                return True
            elif custom_id == "complete_setup":
                self.current_page = self.total_pages # Move to a state where completion message is shown
                await self.update_message(interaction)
                return True
        return True # For selects, allow the select callback to handle it.


# --- UI Components for Slot-Specific SetupView ---
class RoleSelect(discord.ui.RoleSelect):
    def __init__(self, custom_id: str, placeholder: str, friendly_name: str, table_name: str):
        super().__init__(placeholder=placeholder, min_values=1, max_values=1, custom_id=custom_id)
        self.friendly_name = friendly_name
        self.table_name = table_name


    async def callback(self, interaction: discord.Interaction):
        view: SetupView = self.view
        selected_role_id = self.values[0].id
        
        old_value_id = view.config.get(self.custom_id)
        view.config[self.custom_id] = selected_role_id

        await view.save_single_slot_config_item(self.table_name, self.custom_id, selected_role_id)
        await _log_setting_change(interaction.guild, interaction.user, self.friendly_name, old_value_id, selected_role_id, is_role=True, slot_id=view.slot_id, slot_name=view.slot_name)
        await view.update_message(interaction)

class ChannelSelect(discord.ui.ChannelSelect):
    def __init__(self, custom_id: str, placeholder: str, friendly_name: str, table_name: str):
        super().__init__(
            channel_types=[discord.ChannelType.text],
            placeholder=placeholder,
            min_values=1,
            max_values=1,
            custom_id=custom_id
        )
        self.friendly_name = friendly_name
        self.table_name = table_name

    async def callback(self, interaction: discord.Interaction):
        view: SetupView = self.view
        selected_channel_id = self.values[0].id
        
        old_value_id = view.config.get(self.custom_id)
        view.config[self.custom_id] = selected_channel_id

        await view.save_single_slot_config_item(self.table_name, self.custom_id, selected_channel_id)
        await _log_setting_change(interaction.guild, interaction.user, self.friendly_name, old_value_id, selected_channel_id, is_channel=True, slot_id=view.slot_id, slot_name=view.slot_name)
        await view.update_message(interaction)

class ToggleSelect(discord.ui.Select):
    def __init__(self, custom_id: str, current_value: str, friendly_name: str, table_name: str,
                     true_value: str = "on", false_value: str = "off",
                     true_label: str = "🟢 Enabled", false_label: str = "🔴 Disabled",
                     placeholder: str = "Select Status"):
        self.custom_id_internal = custom_id
        self.true_value = true_value
        self.false_value = false_value
        self.friendly_name = friendly_name
        self.table_name = table_name

        options = [
            discord.SelectOption(label=true_label, value=self.true_value, default=str(current_value) == str(self.true_value)),
            discord.SelectOption(label=false_label, value=self.false_value, default=str(current_value) == str(self.false_value))
        ]
        super().__init__(placeholder=placeholder, min_values=1, max_values=1, options=options, custom_id=f"select_{custom_id}")

    async def callback(self, interaction: discord.Interaction):
        view: SetupView = self.view
        selected_value = self.values[0]
        
        old_value = view.config.get(self.custom_id_internal)
        view.config[self.custom_id_internal] = selected_value

        await view.save_single_slot_config_item(self.table_name, self.custom_id_internal, selected_value)
        await _log_setting_change(interaction.guild, interaction.user, self.friendly_name, old_value, selected_value, is_toggle=True, slot_id=view.slot_id, slot_name=view.slot_name)
        await view.update_message(interaction)

class RosterCapModal(discord.ui.Modal, title="Set Roster Cap"):
    def __init__(self, bot_instance, current_roster_cap: int, parent_view: 'SetupView'):
        super().__init__()
        self.bot_instance = bot_instance
        self.parent_view = parent_view
        self.current_roster_cap_on_open = current_roster_cap
        self.roster_cap_input = discord.ui.TextInput(
            label="Roster Cap",
            placeholder="Enter maximum roster size (e.g. 25)",
            default=str(current_roster_cap),
            required=True,
            min_length=1,
            max_length=3
        )
        self.add_item(self.roster_cap_input)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            new_cap = int(self.roster_cap_input.value)
            if new_cap < 1:
                raise ValueError("Roster cap must be positive")

            old_cap = self.current_roster_cap_on_open
            
            self.parent_view.config['roster_cap'] = new_cap
            await self.parent_view.save_single_slot_config_item('slot_command_settings', 'roster_cap', new_cap)
            
            await _log_setting_change(interaction.guild, interaction.user, "Roster Cap", old_cap, new_cap, slot_id=self.parent_view.slot_id, slot_name=self.parent_view.slot_name)

            await interaction.response.defer() # Defer the modal submission response
            await self.parent_view.update_message(interaction) # Then update the parent message

        except ValueError:
            await interaction.response.send_message(
                "Please enter a valid positive number for the roster cap.",
                ephemeral=True
            )
        except Exception as e:
            print(f"Error in roster cap modal submit: {e}")
            traceback.print_exc()
            await interaction.response.send_message("An error occurred while setting roster cap.", ephemeral=True)

class SetupView(discord.ui.View):
    def __init__(self, bot_instance, guild_id: int, slot_id: str, slot_name: str, original_initiator: discord.User):
        super().__init__(timeout=600)
        self.bot = bot_instance
        self.guild_id = guild_id
        self.slot_id = slot_id
        self.slot_name = slot_name
        self.original_initiator = original_initiator # Store the user who initiated the command
        self.current_page = 0
        self.config = {}
        # Welcome (0) + Roles (1-4, 4 pages) + Channels (5-6, 2 pages) + Pickup (7, 1 page) + TeamSettings (8, 1 page) + DemandBasic (9, 1 page) + DemandRoles (10-11, 2 pages) = 12 pages total
        self.total_pages = 12 
        asyncio.create_task(self.load_existing_configs())

    async def load_existing_configs(self):
        print(f"Loading configurations for guild ID: {self.guild_id}, slot ID: {self.slot_id}")

        # Load slot_configs
        self.bot.cursor.execute("SELECT * FROM slot_configs WHERE slot_id = ?", (self.slot_id,))
        slot_configs_row = self.bot.cursor.fetchone()
        if slot_configs_row:
            columns = [desc[0] for desc in self.bot.cursor.description]
            for i, col_name in enumerate(columns):
                self.config[col_name] = slot_configs_row[i]

        # Load slot_command_settings
        self.bot.cursor.execute("SELECT * FROM slot_command_settings WHERE slot_id = ?", (self.slot_id,))
        slot_command_settings_row = self.bot.cursor.fetchone()
        if slot_command_settings_row:
            columns = [desc[0] for desc in self.bot.cursor.description]
            for i, col_name in enumerate(columns):
                self.config[col_name] = slot_command_settings_row[i]

        # Load slot_demand_config
        self.bot.cursor.execute("SELECT * FROM slot_demand_config WHERE slot_id = ?", (self.slot_id,))
        slot_demand_config_row = self.bot.cursor.fetchone()
        if slot_demand_config_row:
            columns = [desc[0] for desc in self.bot.cursor.description]
            for i, col_name in enumerate(columns):
                self.config[col_name] = slot_demand_config_row[i]

        # Set default values for slot-specific configs
        slot_config_keys = [
            'franchise_owner_role', 'general_manager_role', 'head_coach_role',
            'assistant_coach_role', 'transaction_channel', 'free_agent_role',
            'streamer_role', 'referee_role', 'gametime_channel',
            'trade_channel', 'draft_channel', 'pickup_host_role', 'pickup_channel'
        ]
        for key in slot_config_keys:
            self.config.setdefault(key, None)

        command_setting_keys = ['sign_enabled', 'release_enabled', 'offer_enabled', 'roster_cap']
        for key in command_setting_keys:
            if key == 'roster_cap':
                self.config.setdefault(key, 25)
            else:
                self.config.setdefault(key, 'on')

        demand_config_keys = [
            'demands_enabled', 'demand_limit', 'demand_channel',
            'five_demands_role', 'four_demands_role', 'three_demands_role',
            'two_demands_role', 'one_demand_role', 'no_demands_role'
        ]
        for key in demand_config_keys:
            if key in ['demands_enabled', 'demand_limit']:
                self.config.setdefault(key, 'off')
            else:
                self.config.setdefault(key, None)

        print(f"Loaded slot config for {self.slot_name}: {self.config}")

    async def save_single_slot_config_item(self, table_name: str, key: str, value: Any):
        try:
            # Use ON CONFLICT DO UPDATE for UPSERT behavior
            query = f'''
                INSERT INTO {table_name} (slot_id, {key}) VALUES (?, ?)
                ON CONFLICT(slot_id) DO UPDATE SET {key} = excluded.{key}
            '''
            self.bot.cursor.execute(query, (self.slot_id, value))
            self.bot.conn.commit()
            print(f"Autosaved {table_name}: {key} = {value} for slot {self.slot_id}")
        except Exception as e:
            print(f"Error autosaving {table_name} for {key}: {e}")
            traceback.print_exc()

    def _get_footer_text(self):
        return f"Page {self.current_page + 1}/{self.total_pages} for slot '{self.slot_name}'"

    def create_welcome_embed(self):
        embed = discord.Embed(
            title=f"Welcome to RosterFlow - Slot Setup '{self.slot_name}'!",
            description=(
                "You are now setting up configurations specific to this slot.\n\n"
                "This wizard will guide you through configuring:\n"
                "📋 Management & Staff Roles\n"
                "📢 Communication Channels\n"
                "🛡️ Pickup Settings\n"
                "⚙️ Team Management Command Settings\n"
                "📊 Demand System Settings\n\n"
                "Click 'Next' to begin the setup process."
            ),
            color=discord.Color.blue()
        )
        embed.set_footer(text=self._get_footer_text() + " - Welcome")
        return embed

    def create_roles_embed(self):
        embed = discord.Embed(
            title=f"RosterFlow - Slot Roles Setup ({self._get_footer_text()})",
            description=(
                "Configure your league's management and staff roles for this slot. "
                "These roles will have specific permissions and access to commands.\n"
                "Selections are autosaved."
            ),
            color=discord.Color.blue()
        )
        all_roles_info = [
            ("Franchise Owner", "franchise_owner_role"),
            ("General Manager", "general_manager_role"),
            ("Head Coach", "head_coach_role"),
            ("Assistant Coach", "assistant_coach_role"),
            ("Free Agent", "free_agent_role"),
            ("Referee", "referee_role"),
            ("Streamer", "streamer_role")
        ]
        guild = self.bot.get_guild(self.guild_id)
        if not guild: return embed
        for role_name, role_key in all_roles_info:
            role_obj = guild.get_role(self.config.get(role_key)) if self.config.get(role_key) else None
            value = f"Set to: {role_obj.mention if role_obj else '❌ Not set'}"
            embed.add_field(name=role_name, value=value, inline=False)
        return embed

    def create_channels_embed(self):
        embed = discord.Embed(
            title=f"RosterFlow - Slot Channel Configuration ({self._get_footer_text()})",
            description=(
                "**Set Up Your League Communication Channels for this slot**\n\n"
                "Configure channels for announcements, schedules, trades, and draft updates.\n"
                "Selections are autosaved."
            ),
            color=discord.Color.blue()
        )
        all_channels_info = [
            ("Transaction Channel", "transaction_channel"),
            ("Game-times Channel", "gametime_channel"),
            ("Trade Channel", "trade_channel"),
            ("Draft Channel", "draft_channel")
        ]
        guild = self.bot.get_guild(self.guild_id)
        if not guild: return embed
        for ch_name, ch_key in all_channels_info:
            ch_obj = guild.get_channel(self.config.get(ch_key)) if self.config.get(ch_key) else None
            value = f"Set to: {ch_obj.mention if ch_obj else '❌ Not set'}"
            embed.add_field(name=ch_name, value=value, inline=False)
        return embed

    def create_pickup_settings_embed(self):
        embed = discord.Embed(
            title=f"RosterFlow - Slot Pickup Settings ({self._get_footer_text()})",
            description=(
                "Configure roles and channels for pickup game hosting for this slot.\n"
                "Selections are autosaved."
            ),
            color=discord.Color.blue()
        )
        guild = self.bot.get_guild(self.guild_id)
        if not guild: return embed

        settings_to_display = [
            ("Pickup Host Role", "pickup_host_role", True),
            ("Pickup Channel", "pickup_channel", False)
        ]
        for name, key, is_role in settings_to_display:
            obj_id = self.config.get(key)
            obj = None
            if obj_id:
                obj = guild.get_role(obj_id) if is_role else guild.get_channel(obj_id)
            embed.add_field(name=name, value=f"Set to: {obj.mention if obj else '❌ Not set'}", inline=False)
        return embed

    def create_team_settings_embed(self):
        embed = discord.Embed(
            title=f"RosterFlow - Slot Team Management Settings ({self._get_footer_text()})",
            description="Toggle team management commands and set the roster cap for this slot. Selections are autosaved.",
            color=discord.Color.blue()
        )
        sign_status = "🟢 Enabled" if self.config.get('sign_enabled') == 'on' else "🔴 Disabled"
        release_status = "🟢 Enabled" if self.config.get('release_enabled') == 'on' else "🔴 Disabled"
        offer_status = "🟢 Enabled" if self.config.get('offer_enabled') == 'on' else "🔴 Disabled"
        roster_cap = self.config.get('roster_cap', 25)

        embed.add_field(name="Sign Command", value=sign_status, inline=True)
        embed.add_field(name="Release Command", value=release_status, inline=True)
        embed.add_field(name="Offer Command", value=offer_status, inline=True)
        embed.add_field(name="Roster Cap", value=f"{roster_cap} players", inline=False)
        return embed

    def create_demand_settings_embed_page1(self):
        embed = discord.Embed(
            title=f"RosterFlow - Slot Demand System Setup ({self._get_footer_text()})",
            description="Configure the fundamental settings for this slot's demand system. Selections are autosaved.",
            color=discord.Color.blue()
        )
        system_status = "🟢 Enabled" if self.config.get('demands_enabled') == "on" else "🔴 Disabled"
        limits_status = "🟢 Enabled" if self.config.get('demand_limit') == "true" else "🔴 Disabled"
        
        guild = self.bot.get_guild(self.guild_id)
        demand_ch_obj = guild.get_channel(self.config.get('demand_channel')) if guild and self.config.get('demand_channel') else None
        channel_value = demand_ch_obj.mention if demand_ch_obj else "❌ Not set"

        embed.add_field(name="Demand System", value=system_status, inline=True)
        embed.add_field(name="Demand Limits", value=limits_status, inline=True)
        embed.add_field(name="Demand Channel", value=channel_value, inline=False)
        embed.add_field(name="Info", value="ℹ️ If 'Demand System' is disabled, or 'Demand Limits' are disabled (when system is on), the demand role configuration pages will be skipped.", inline=False)
        return embed

    def create_demand_roles_embed(self, part: int):
        page_title_part = "Page 1" if part == 1 else "Page 2"
        embed = discord.Embed(
            title=f"RosterFlow - Slot Demand Role Config ({page_title_part} - {self._get_footer_text()})",
            description="Configure roles for different demand levels for this slot. Selections are autosaved.",
            color=discord.Color.blue()
        )
        guild = self.bot.get_guild(self.guild_id)
        if not guild: return embed

        if part == 1:
            roles_to_display = [
                ("Five Demands Role", "five_demands_role"),
                ("Four Demands Role", "four_demands_role"),
                ("Three Demands Role", "three_demands_role")
            ]
        else:
            roles_to_display = [
                ("Two Demands Role", "two_demands_role"),
                ("One Demand Role", "one_demand_role"),
                ("No Demands Role", "no_demands_role")
            ]
        for name, key in roles_to_display:
            role_obj = guild.get_role(self.config.get(key)) if self.config.get(key) else None
            embed.add_field(name=name, value=f"Set to: {role_obj.mention if role_obj else '❌ Not set'}", inline=False)
        return embed

    async def update_message(self, interaction: discord.Interaction, initial_send: bool = False):
        self.clear_items()
        embed = None
        
        # Wait for config to load if not already loaded
        while not self.config:
            await asyncio.sleep(0.1) # Small delay to allow load_existing_configs to run

        current_page_for_footer = self.current_page

        # Page 0: Welcome
        if self.current_page == 0:
            embed = self.create_welcome_embed()
            self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
        # Page 1-4: Slot Roles Setup
        elif 1 <= self.current_page <= 4:
            embed = self.create_roles_embed()
            if self.current_page == 1:
                self.add_item(RoleSelect(custom_id="franchise_owner_role", placeholder="Select Franchise Owner Role", friendly_name="Franchise Owner Role", table_name="slot_configs"))
                self.add_item(RoleSelect(custom_id="general_manager_role", placeholder="Select GM Role", friendly_name="General Manager Role", table_name="slot_configs"))
            elif self.current_page == 2:
                self.add_item(RoleSelect(custom_id="head_coach_role", placeholder="Select Head Coach Role", friendly_name="Head Coach Role", table_name="slot_configs"))
                self.add_item(RoleSelect(custom_id="assistant_coach_role", placeholder="Select Assistant Coach Role", friendly_name="Assistant Coach Role", table_name="slot_configs"))
            elif self.current_page == 3:
                self.add_item(RoleSelect(custom_id="free_agent_role", placeholder="Select Free Agent Role", friendly_name="Free Agent Role", table_name="slot_configs"))
            elif self.current_page == 4:
                self.add_item(RoleSelect(custom_id="referee_role", placeholder="Select Referee Role", friendly_name="Referee Role", table_name="slot_configs"))
                self.add_item(RoleSelect(custom_id="streamer_role", placeholder="Select Streamer Role", friendly_name="Streamer Role", table_name="slot_configs"))
            self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        # Page 5-6: Communication Channels
        elif 5 <= self.current_page <= 6:
            embed = self.create_channels_embed()
            if self.current_page == 5:
                self.add_item(ChannelSelect(custom_id="transaction_channel", placeholder="Select Transaction Channel", friendly_name="Transaction Channel", table_name="slot_configs"))
                self.add_item(ChannelSelect(custom_id="gametime_channel", placeholder="Select Game-times Channel", friendly_name="Game-times Channel", table_name="slot_configs"))
            elif self.current_page == 6:
                self.add_item(ChannelSelect(custom_id="trade_channel", placeholder="Select Trade Channel", friendly_name="Trade Channel", table_name="slot_configs"))
                self.add_item(ChannelSelect(custom_id="draft_channel", placeholder="Select Draft Channel", friendly_name="Draft Channel", table_name="slot_configs"))
            self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        # Page 7: Pickup Settings
        elif self.current_page == 7:
            embed = self.create_pickup_settings_embed()
            self.add_item(RoleSelect(custom_id="pickup_host_role", placeholder="Select Pickup Host Role", friendly_name="Pickup Host Role", table_name="slot_configs"))
            self.add_item(ChannelSelect(custom_id="pickup_channel", placeholder="Select Pickup Channel", friendly_name="Pickup Channel", table_name="slot_configs"))
            self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        # Page 8: Team Management Settings
        elif self.current_page == 8:
            embed = self.create_team_settings_embed()
            self.add_item(ToggleSelect(custom_id='sign_enabled', current_value=self.config.get('sign_enabled', 'on'), placeholder="Sign Command Status", friendly_name="Sign Command", table_name="slot_command_settings"))
            self.add_item(ToggleSelect(custom_id='release_enabled', current_value=self.config.get('release_enabled', 'on'), placeholder="Release Command Status", friendly_name="Release Command", table_name="slot_command_settings"))
            self.add_item(ToggleSelect(custom_id='offer_enabled', current_value=self.config.get('offer_enabled', 'on'), placeholder="Offer Command Status", friendly_name="Offer Command", table_name="slot_command_settings"))
            self.add_item(discord.ui.Button(label="Set Roster Cap", style=discord.ButtonStyle.secondary, custom_id="set_roster_cap"))
            self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        # Page 9: Demand System Basic Settings
        elif self.current_page == 9:
            embed = self.create_demand_settings_embed_page1()
            self.add_item(ToggleSelect(custom_id='demands_enabled', current_value=self.config.get('demands_enabled', 'off'), true_label="🟢 System Enabled", false_label="🔴 System Disabled", placeholder="Demand System Status", friendly_name="Demand System", table_name="slot_demand_config"))
            self.add_item(ToggleSelect(custom_id='demand_limit', current_value=self.config.get('demand_limit', 'false'), true_value="true", false_value="false", true_label="🟢 Limits Enabled", false_label="🔴 Limits Disabled", placeholder="Demand Limits Status", friendly_name="Demand Limits", table_name="slot_demand_config"))
            self.add_item(ChannelSelect(custom_id="demand_channel", placeholder="Select Demand Channel", friendly_name="Demand Channel", table_name="slot_demand_config"))
            
            if self.config.get('demands_enabled') == 'off' or \
               (self.config.get('demands_enabled') == 'on' and self.config.get('demand_limit') != 'true'):
                # If demands are disabled or limits are off, skip demand role pages (10, 11) to final page
                self.add_item(discord.ui.Button(label="Next (Skip Demand Roles)", style=discord.ButtonStyle.primary, custom_id="next_page"))
            else:
                self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        # Page 10: Demand System Role Configuration (Part 1)
        elif self.current_page == 10:
            embed = self.create_demand_roles_embed(part=1)
            self.add_item(RoleSelect(custom_id="five_demands_role", placeholder="Select Five Demands Role", friendly_name="Five Demands Role", table_name="slot_demand_config"))
            self.add_item(RoleSelect(custom_id="four_demands_role", placeholder="Select Four Demands Role", friendly_name="Four Demands Role", table_name="slot_demand_config"))
            self.add_item(RoleSelect(custom_id="three_demands_role", placeholder="Select Three Demands Role", friendly_name="Three Demands Role", table_name="slot_demand_config"))
            self.add_item(discord.ui.Button(label="Next", style=discord.ButtonStyle.primary, custom_id="next_page"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        # Page 11: Demand System Role Configuration (Part 2) - This is the actual last content page before completion
        elif self.current_page == self.total_pages - 1: # Last content page (page 11, index 11)
            embed = self.create_demand_roles_embed(part=2)
            self.add_item(RoleSelect(custom_id="two_demands_role", placeholder="Select Two Demands Role", friendly_name="Two Demands Role", table_name="slot_demand_config"))
            self.add_item(RoleSelect(custom_id="one_demand_role", placeholder="Select One Demand Role", friendly_name="One Demand Role", table_name="slot_demand_config"))
            self.add_item(RoleSelect(custom_id="no_demands_role", placeholder="Select No Demands Role", friendly_name="No Demands Role", table_name="slot_demand_config"))
            self.add_item(discord.ui.Button(label="Complete Slot Setup", style=discord.ButtonStyle.success, custom_id="complete_slot_setup"))
            self.add_item(discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page"))
        else: # Final completion message
            final_embed = discord.Embed(
                title=f"✅ Slot Setup Complete for '{self.slot_name}'",
                description=(
                    "All slot-specific settings have been saved successfully!\n\n"
                    "You can now manage your league using commands associated with this slot."
                ),
                color=discord.Color.green()
            )
            final_embed.set_footer(text="Thank you for choosing RosterFlow!")
            # Use edit_original_response if already deferred, otherwise send_message
            if interaction.response.is_done():
                await interaction.edit_original_response(embed=final_embed, view=None)
            else:
                await interaction.response.send_message(embed=final_embed, view=None, ephemeral=True)
            return


        # Update footer text to reflect current page and total pages
        if embed:
            actual_total_pages_display = self.total_pages # Default to max pages
            if self.config.get('demands_enabled') == 'off' or \
               (self.config.get('demands_enabled') == 'on' and self.config.get('demand_limit') != 'true'):
                # If demand roles were skipped, the effective total pages is 2 less
                if self.current_page > 9: # From page 10 onwards, means we skipped
                    actual_total_pages_display = self.total_pages - 2 
            
            embed.set_footer(text=f"Page {current_page_for_footer + 1}/{actual_total_pages_display} - Slot: '{self.slot_name}'")


        if initial_send:
            # If this is the initial send AND the interaction hasn't been responded to yet
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, view=self, ephemeral=True)
            else:
                # If already deferred, use followup.send
                await interaction.followup.send(embed=embed, view=self, ephemeral=True)
        else:
            # For subsequent updates, always edit the original message
            if not interaction.response.is_done():
                await interaction.response.edit_message(embed=embed, view=self)
            else:
                await interaction.edit_original_response(embed=embed, view=self)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # Check if the user interacting is the original initiator
        if interaction.user != self.original_initiator:
            await interaction.response.send_message("You are not the one who started this setup!", ephemeral=True)
            return False

        custom_id = interaction.data.get("custom_id")

        if interaction.data.get("component_type") == 2: # Button component type
            if custom_id == "set_roster_cap":
                current_cap = self.config.get('roster_cap', 25)
                modal = RosterCapModal(self.bot, current_cap, parent_view=self)
                await interaction.response.send_modal(modal)
                return True

            elif custom_id == "next_page":
                if self.current_page == 9 and \
                   (self.config.get('demands_enabled') == 'off' or \
                    (self.config.get('demands_enabled') == 'on' and self.config.get('demand_limit') != 'true')):
                    # If demands are disabled or limits are off, skip demand role pages (10, 11)
                    self.current_page = self.total_pages - 1 # Go to the final page (11)
                else:
                    self.current_page += 1
                
                # Ensure we don't go past the last logical page
                if self.current_page >= self.total_pages:
                    self.current_page = self.total_pages # Cap at the last page for completion message
                
                await self.update_message(interaction)
                return True

            elif custom_id == "prev_page":
                # Logic to correctly jump back if demand roles were skipped
                if self.current_page == self.total_pages -1 and \
                   (self.config.get('demands_enabled') == 'off' or \
                    (self.config.get('demands_enabled') == 'on' and self.config.get('demand_limit') != 'true')):
                    self.current_page = 9 # Go back to Demand System Basic Settings if roles were skipped
                else:
                    self.current_page = max(0, self.current_page - 1)
                
                await self.update_message(interaction)
                return True

            elif custom_id == "complete_slot_setup":
                # This button is only on the final page, triggers completion logic
                final_embed = discord.Embed(
                    title=f"✅ Slot Setup Complete for '{self.slot_name}'",
                    description=(
                        "All slot-specific settings have been saved successfully!\n\n"
                        "You can now manage your league using commands associated with this slot."
                    ),
                    color=discord.Color.green()
                )
                final_embed.set_footer(text="Thank you for choosing RosterFlow!")
                # Use edit_original_response if already deferred, otherwise send_message
                if interaction.response.is_done():
                    await interaction.edit_original_response(embed=final_embed, view=None)
                else:
                    await interaction.response.send_message(embed=final_embed, view=None, ephemeral=True)
                return True

        return True


# Helper function to get the next application number for a slot
def _get_next_application_number(slot_id: str) -> int:
    bot.cursor.execute("SELECT MAX(application_number) FROM applications WHERE slot_id = ?", (slot_id,))
    max_num = bot.cursor.fetchone()[0]
    return (max_num or 0) + 1

class ApplicationButtonsView(ui.View):
    def __init__(self, bot_instance: TransactionBot):
        super().__init__(timeout=None) # Keep buttons persistent
        self.bot_instance = bot_instance

    @ui.button(label="Accept", style=discord.ButtonStyle.success, custom_id="accept_application")
    async def accept_button(self, interaction: discord.Interaction, button: ui.Button):
        if not await has_bot_management_permission(interaction):
            await interaction.response.send_message("You do not have permission to accept or decline applications.", ephemeral=True)
            return

        await interaction.response.defer()

        message_id = interaction.message.id

        self.bot_instance.cursor.execute("SELECT * FROM applications WHERE message_id = ?", (message_id,))
        app_data = self.bot_instance.cursor.fetchone()

        if not app_data:
            await interaction.followup.send("This application could not be found in the database.", ephemeral=True)
            return

        # Extract data, assuming column order from applications table
        app_id, slot_id, app_number, applicant_id, app_type, status, reason, experience, additional_info, \
        prev_league_experience, prev_league_name, award_exp, tc_ready, tc_link, rings_exp, \
        player_recruitment_plan, activity_commitment, activity_rating, _, _ = app_data

        if status != 'pending':
            await interaction.followup.send(f"This application has already been {status}.", ephemeral=True)
            return

        self.bot_instance.cursor.execute("UPDATE applications SET status = ? WHERE id = ?", ('accepted', app_id))
        self.bot_instance.conn.commit()

        original_embed = interaction.message.embeds[0]
        original_embed.color = discord.Color.green()
        original_embed.add_field(name="Status", value=f"Accepted by {interaction.user.mention}", inline=False)
        # Fetch slot_name for the footer
        slot_name_for_footer = slot_id[:8] + "..."
        bot.cursor.execute("SELECT slot_name FROM slots WHERE slot_id = ?", (slot_id,))
        slot_row = bot.cursor.fetchone()
        if slot_row:
            slot_name_for_footer = slot_row[0]

        original_embed.set_footer(text=f"Application #{app_number} | Accepted by {interaction.user.display_name} | Slot: {slot_name_for_footer}")
        await interaction.message.edit(embed=original_embed, view=None)

        applicant = self.bot_instance.get_user(applicant_id) or await self.bot_instance.fetch_user(applicant_id)
        if applicant:
            try:
                dm_embed = Embed(
                    title=f"Your Application for {app_type} - Accepted!",
                    description=f"Congratulations! Your application for **{app_type}** in **{interaction.guild.name}** (Slot: `{slot_name_for_footer}`) has been accepted by {interaction.user.display_name}.",
                    color=discord.Color.green()
                )
                dm_embed.add_field(name="Application Number", value=f"#{app_number}", inline=True)
                dm_embed.add_field(name="Decision Maker", value=interaction.user.mention, inline=True)
                dm_embed.set_footer(text="Contact server staff for next steps!")
                await applicant.send(embed=dm_embed)
            except discord.Forbidden:
                print(f"Could not DM applicant {applicant.id} about accepted application.")
                await interaction.followup.send(f"Application accepted, but could not DM {applicant.mention}.", ephemeral=True)
            except Exception as e:
                print(f"Error sending DM: {e}")
                await interaction.followup.send(f"Application accepted, but an error occurred while sending DM to {applicant.mention}.", ephemeral=True)
        else:
            await interaction.followup.send("Application accepted, but could not find the applicant to DM.", ephemeral=True)

        await interaction.followup.send(f"Application #{app_number} for {applicant.mention if applicant else 'Unknown User'} has been accepted.", ephemeral=True)

    @ui.button(label="Decline", style=discord.ButtonStyle.danger, custom_id="decline_application")
    async def decline_button(self, interaction: discord.Interaction, button: ui.Button):
        if not await has_bot_management_permission(interaction):
            await interaction.response.send_message("You do not have permission to accept or decline applications.", ephemeral=True)
            return

        await interaction.response.defer()

        message_id = interaction.message.id

        self.bot_instance.cursor.execute("SELECT * FROM applications WHERE message_id = ?", (message_id,))
        app_data = self.bot_instance.cursor.fetchone()

        if not app_data:
            await interaction.followup.send("This application could not be found in the database.", ephemeral=True)
            return
            
        app_id, slot_id, app_number, applicant_id, app_type, status, reason, experience, additional_info, \
        prev_league_experience, prev_league_name, award_exp, tc_ready, tc_link, rings_exp, \
        player_recruitment_plan, activity_commitment, activity_rating, _, _ = app_data

        if status != 'pending':
            await interaction.followup.send(f"This application has already been {status}.", ephemeral=True)
            return

        self.bot_instance.cursor.execute("UPDATE applications SET status = ? WHERE id = ?", ('declined', app_id))
        self.bot_instance.conn.commit()

        original_embed = interaction.message.embeds[0]
        original_embed.color = discord.Color.red()
        original_embed.add_field(name="Status", value=f"Declined by {interaction.user.mention}", inline=False)
        
        # Fetch slot_name for the footer
        slot_name_for_footer = slot_id[:8] + "..."
        bot.cursor.execute("SELECT slot_name FROM slots WHERE slot_id = ?", (slot_id,))
        slot_row = bot.cursor.fetchone()
        if slot_row:
            slot_name_for_footer = slot_row[0]

        original_embed.set_footer(text=f"Application #{app_number} | Declined by {interaction.user.display_name} | Slot: {slot_name_for_footer}")
        await interaction.message.edit(embed=original_embed, view=None)

        applicant = self.bot_instance.get_user(applicant_id) or await self.bot_instance.fetch_user(applicant_id)
        if applicant:
            try:
                dm_embed = Embed(
                    title=f"Your Application for {app_type} - Declined",
                    description=f"Unfortunately, your application for **{app_type}** in **{interaction.guild.name}** (Slot: `{slot_name_for_footer}`) has been declined by {interaction.user.display_name}.",
                    color=discord.Color.red()
                )
                dm_embed.add_field(name="Application Number", value=f"#{app_number}", inline=True)
                dm_embed.add_field(name="Decision Maker", value=interaction.user.mention, inline=True)
                dm_embed.set_footer(text="You may re-apply later or contact server staff for feedback.")
                await applicant.send(embed=dm_embed)
            except discord.Forbidden:
                print(f"Could not DM applicant {applicant.id} about declined application.")
                await interaction.followup.send(f"Application declined, but could not DM {applicant.mention}.", ephemeral=True)
            except Exception as e:
                print(f"Error sending DM: {e}")
                await interaction.followup.send(f"Application declined, but an error occurred while sending DM to {applicant.mention}.", ephemeral=True)
        else:
            await interaction.followup.send("Application declined, but could not find the applicant to DM.", ephemeral=True)

        await interaction.followup.send(f"Application #{app_number} for {applicant.mention if applicant else 'Unknown User'} has been declined.", ephemeral=True)


class ApplicationModal(ui.Modal, title='League Application'):
    def __init__(self, bot_instance: TransactionBot, application_type: str, slot_id: str, slot_name: str):
        super().__init__()
        self.bot_instance = bot_instance
        self.application_type = application_type
        self.slot_id = slot_id
        self.slot_name = slot_name

        self.add_item(ui.TextInput(label=f"Why apply for {application_type}?", style=discord.TextStyle.paragraph, custom_id="reason", required=True, max_length=1000))
        self.add_item(ui.TextInput(label="Prev. League Exp? (Y/N) & League Name", style=discord.TextStyle.short, custom_id="prev_league_info", required=False, placeholder="e.g., Yes, NBA 2K League or No", max_length=200))
        self.add_item(ui.TextInput(label="Awards/Rings? TC Link? (Y/N, Link)", style=discord.TextStyle.short, custom_id="awards_rings_tc_info", required=False, placeholder="e.g., Yes, 2 awards, 1 ring, TC: link.com or No", max_length=200))
        self.add_item(ui.TextInput(label="Player Plan (TC/Recruit) & Activity (1-10)", style=discord.TextStyle.short, custom_id="player_activity_plan", required=False, placeholder="e.g., Recruit, 8 or Both, 10", max_length=100))
        self.add_item(ui.TextInput(label="Any other relevant information?", style=discord.TextStyle.paragraph, custom_id="additional_info", required=False, max_length=500))

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

        guild_id = interaction.guild_id
        application_channel_id = None
        blacklist_role_id = None

        try:
            guild_settings = await self.bot_instance.get_guild_settings(guild_id)
            application_channel_id = guild_settings.get('application_channel')
            blacklist_role_id = guild_settings.get('application_blacklist_role')

            if blacklist_role_id:
                blacklist_role = interaction.guild.get_role(blacklist_role_id)
                if blacklist_role and blacklist_role in interaction.user.roles:
                    await interaction.followup.send("You are currently blacklisted from submitting applications.", ephemeral=True)
                    return
            
            if not application_channel_id:
                await interaction.followup.send("The global application submission channel has not been set up by an administrator. Please contact server staff.", ephemeral=True)
                return

            application_channel = interaction.guild.get_channel(application_channel_id)
            if not application_channel:
                await interaction.followup.send("The configured global application channel no longer exists. Please contact server staff.", ephemeral=True)
                return

            reason = self.children[0].value
            prev_league_info = self.children[1].value
            awards_rings_tc_info = self.children[2].value
            player_activity_plan = self.children[3].value
            additional_info = self.children[4].value

            prev_league_experience = "No"
            prev_league_name = None
            if prev_league_info and prev_league_info.lower().startswith('yes'):
                prev_league_experience = "Yes"
                if ',' in prev_league_info:
                    prev_league_name = prev_league_info.split(',', 1)[1].strip()
            
            award_exp = "No"
            rings_exp = "No"
            tc_ready = "No"
            tc_link = None
            if awards_rings_tc_info:
                lower_info = awards_rings_tc_info.lower()
                if 'yes' in lower_info:
                    if 'award' in lower_info or 'awards' in lower_info : award_exp = "Yes"
                    if 'ring' in lower_info or 'rings' in lower_info: rings_exp = "Yes"
                if 'tc:' in lower_info:
                    tc_ready = "Yes"
                    tc_link_start = lower_info.find('tc:')
                    if tc_link_start != -1:
                        tc_link_part = awards_rings_tc_info[tc_link_start + 3:].strip()
                        tc_link = tc_link_part.split(' ')[0].split(',')[0]
                        if not (tc_link.startswith('http://') or tc_link.startswith('https://')):
                                tc_link = "http://" + tc_link
            
            player_recruitment_plan = None
            activity_commitment = "No"
            activity_rating = None
            if player_activity_plan:
                parts = player_activity_plan.split(',')
                if len(parts) > 0:
                    plan_part = parts[0].strip().lower()
                    if "tc" in plan_part and "recruit" in plan_part: player_recruitment_plan = "Both"
                    elif "tc" in plan_part: player_recruitment_plan = "From TC"
                    elif "recruit" in plan_part: player_recruitment_plan = "Recruit Anybody"
                    else: player_recruitment_plan = plan_part.capitalize()

                if len(parts) > 1:
                    activity_part = parts[1].strip()
                    try:
                        rating = int(activity_part)
                        if 1 <= rating <= 10:
                            activity_rating = rating
                            activity_commitment = "Yes"
                    except ValueError:
                        pass

            app_number = _get_next_application_number(self.slot_id)

            self.bot_instance.cursor.execute(
                """INSERT INTO applications (
                    slot_id, application_number, applicant_id, application_type, status,
                    reason, experience, additional_info,
                    prev_league_experience, prev_league_name, award_exp, tc_ready, tc_link, rings_exp,
                    player_recruitment_plan, activity_commitment, activity_rating
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    self.slot_id, app_number, interaction.user.id, self.application_type, 'pending',
                    reason, "", additional_info,
                    prev_league_experience, prev_league_name, award_exp, tc_ready, tc_link, rings_exp,
                    player_recruitment_plan, activity_commitment, activity_rating
                )
            )
            self.bot_instance.conn.commit()
            application_db_id = self.bot_instance.cursor.lastrowid

            embed = Embed(
                title=f"New {self.application_type} Application - #{app_number}",
                color=discord.Color.blue()
            )
            embed.set_author(name=interaction.user.display_name, icon_url=interaction.user.display_avatar.url)
            embed.add_field(name="Applicant", value=interaction.user.mention, inline=True)
            embed.add_field(name="Applied For", value=self.application_type, inline=True)
            embed.add_field(name="Slot", value=f"{self.slot_name} (`{self.slot_id[:8]}...`)", inline=True)
            embed.add_field(name="Reason for Applying", value=reason, inline=False)

            if prev_league_experience == "Yes":
                embed.add_field(name="Previous League Experience", value=f"Yes (League: {prev_league_name if prev_league_name else 'Not specified'})", inline=False)
            else:
                embed.add_field(name="Previous League Experience", value="No", inline=False)
            
            awards_rings_tc_display = []
            if award_exp == "Yes": awards_rings_tc_display.append("Awards: Yes")
            if rings_exp == "Yes": awards_rings_tc_display.append("Rings: Yes")
            if tc_ready == "Yes": awards_rings_tc_display.append(f"TC Ready: Yes ({tc_link if tc_link else 'No link provided'})")
            if not awards_rings_tc_display: awards_rings_tc_display.append("None specified")
            embed.add_field(name="Awards/Rings/TC Info", value="\n".join(awards_rings_tc_display), inline=False)

            player_activity_display = []
            if player_recruitment_plan: player_activity_display.append(f"Player Recruitment Plan: {player_recruitment_plan}")
            if activity_commitment == "Yes" and activity_rating: player_activity_display.append(f"Activity Commitment: Yes (Rating: {activity_rating}/10)")
            elif activity_commitment == "No": player_activity_display.append("Activity Commitment: No")
            if not player_activity_display: player_activity_display.append("None specified")
            embed.add_field(name="Player Plan & Activity", value="\n".join(player_activity_display), inline=False)

            if additional_info:
                embed.add_field(name="Additional Info", value=additional_info, inline=False)

            embed.set_footer(text=f"User ID: {interaction.user.id}")
            embed.timestamp = discord.utils.utcnow()

            message = await application_channel.send(embed=embed, view=ApplicationButtonsView(self.bot_instance))
            
            self.bot_instance.cursor.execute("UPDATE applications SET message_id = ? WHERE id = ?", (message.id, application_db_id))
            self.bot_instance.conn.commit()

            await interaction.followup.send("Your application has been submitted successfully!", ephemeral=True)

        except Exception as e:
            print(f"Error submitting application: {e}")
            traceback.print_exc()
            await interaction.followup.send("An error occurred while submitting your application. Please try again later.", ephemeral=True)


# --- New global setup command ---
@bot.tree.command(name="global_setup", description="Configure server-wide bot settings (applies to all slots).")
async def global_setup(interaction: discord.Interaction):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    view = GlobalSetupView(bot, interaction.guild_id, interaction.user) # Pass interaction.user
    # Initial send will handle deferring if needed, or just send the message
    await view.update_message(interaction, initial_send=True)


# --- Modified setup command to always update demand config for all "attached" slot_ids ---

@bot.tree.command(name="setup", description="Start the configuration wizard for a specific league slot.")
@app_commands.describe(
    slot_id_or_name="The ID or name of the slot you want to configure. Leave empty for 'main' slot."
)
async def setup(interaction: discord.Interaction, slot_id_or_name: Optional[str] = None):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)

    selected_slot_id = None
    selected_slot_name = None
    guild_id = interaction.guild_id

    # Step 1: Find slot_id and slot_name for requested slot (ID or name)
    if slot_id_or_name:
        # Try exact ID first
        bot.cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND slot_id = ?", (guild_id, slot_id_or_name))
        slot_data = bot.cursor.fetchone()
        if not slot_data:
            # Try by name
            bot.cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND slot_name = ?", (guild_id, slot_id_or_name))
            slot_data = bot.cursor.fetchone()
        if slot_data:
            selected_slot_id, selected_slot_name = slot_data
        else:
            await interaction.followup.send(
                f"Could not find a slot with ID or name '{slot_id_or_name}'. Please use `/list_slots` to see available slots or `/create_slot` to make a new one.", ephemeral=True
            )
            return
    else:
        # Default to "main"
        default_slot_name = "main"
        bot.cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND slot_name = ?", (guild_id, default_slot_name))
        slot_data = bot.cursor.fetchone()
        if slot_data:
            selected_slot_id, selected_slot_name = slot_data
        else:
            selected_slot_id = uuid.uuid4().hex
            selected_slot_name = default_slot_name
            try:
                bot.cursor.execute(
                    "INSERT INTO slots (slot_id, guild_id, slot_name, description) VALUES (?, ?, ?, ?)",
                    (selected_slot_id, guild_id, selected_slot_name, "Default league configuration slot.")
                )
                bot.cursor.execute("INSERT OR IGNORE INTO slot_configs (slot_id) VALUES (?)", (selected_slot_id,))
                bot.cursor.execute("INSERT OR IGNORE INTO slot_command_settings (slot_id) VALUES (?)", (selected_slot_id,))
                bot.cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (selected_slot_id,))
                bot.conn.commit()
                await interaction.followup.send(
                    f"The default slot '{selected_slot_name}' was created for you. Now starting its configuration.", ephemeral=True
                )
                await _log_setting_change(
                    interaction.guild, interaction.user,
                    "New Default Slot Created", "N/A", f"Slot Name: {selected_slot_name}, ID: {selected_slot_id}",
                    slot_id=selected_slot_id, slot_name=selected_slot_name
                )
            except Exception as e:
                print(f"Error creating default slot: {e}")
                traceback.print_exc()
                await interaction.followup.send("An error occurred while creating the default slot. Please try again or use `/create_slot`.", ephemeral=True)
                return

    # --- SLOT ATTACHMENT FIX: Update all league_teams rows pointing to this slot name (or legacy slot_ids) to use the canonical slot_id
    # So that all teams for this slot_name (e.g. "A") use the same slot_id as in slots table

    # Fixup league_teams slot_id to match canonical slot_id for this slot_name/guild
    bot.cursor.execute(
        "UPDATE league_teams SET slot_id = ? WHERE slot_id IN (SELECT slot_id FROM slots WHERE slot_name = ? AND guild_id = ?) OR slot_id = ?",
        (selected_slot_id, selected_slot_name, guild_id, selected_slot_id)
    )
    bot.conn.commit()

    # Also ensure a row exists in slot_demand_config for this canonical slot_id
    bot.cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (selected_slot_id,))
    bot.conn.commit()

    # Now, proceed as before
    view = SetupView(bot, guild_id, selected_slot_id, selected_slot_name, interaction.user)
    await view.update_message(interaction, initial_send=True)


# --- New slot management commands ---
@bot.tree.command(name="create_slot", description="Create a new league configuration slot for this guild.")
@app_commands.describe(
    name="A unique name for your new league slot (e.g., 'Season 3', 'Competitive League')",
    description="An optional description for this slot."
)
async def create_slot(interaction: discord.Interaction, name: str, description: Optional[str] = None):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)
    guild_id = interaction.guild_id

    bot.cursor.execute("SELECT COUNT(*) FROM slots WHERE guild_id = ?", (guild_id,))
    current_slots = bot.cursor.fetchone()[0]
    if current_slots >= 5:
        await interaction.followup.send("You have reached the maximum limit of 5 slots per guild.", ephemeral=True)
        return

    bot.cursor.execute("SELECT slot_id FROM slots WHERE guild_id = ? AND slot_name = ?", (guild_id, name))
    if bot.cursor.fetchone():
        await interaction.followup.send(f"A slot with the name '{name}' already exists in this guild. Please choose a unique name.", ephemeral=True)
        return

    slot_id = uuid.uuid4().hex
    try:
        bot.cursor.execute(
            "INSERT INTO slots (slot_id, guild_id, slot_name, description) VALUES (?, ?, ?, ?)",
            (slot_id, guild_id, name, description)
        )
        # Initialize default configs for the new slot
        bot.cursor.execute("INSERT OR IGNORE INTO slot_configs (slot_id) VALUES (?)", (slot_id,))
        bot.cursor.execute("INSERT OR IGNORE INTO slot_command_settings (slot_id) VALUES (?)", (slot_id,))
        bot.cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (slot_id,))
        bot.conn.commit()

        await interaction.followup.send(f"Slot '{name}' (ID: `{slot_id[:8]}...`) created successfully! Use `/setup {name}` to configure it.", ephemeral=True)
        await _log_setting_change(
            interaction.guild, interaction.user,
            "New Slot Created", "N/A", f"Slot Name: {name}, ID: {slot_id}",
            slot_id=slot_id, slot_name=name
        )

    except Exception as e:
        print(f"Error creating slot: {e}")
        traceback.print_exc()
        await interaction.followup.send("An error occurred while creating the slot. Please try again.", ephemeral=True)

from discord import app_commands
from typing import Optional
import discord

@bot.tree.command(name="delete_slot", description="Delete an existing league configuration slot and all its data.")
@app_commands.describe(
    slot_id_or_name="The ID or name of the slot you want to delete. Use `/list_slots` to see available slots."
)
async def delete_slot(interaction: discord.Interaction, slot_id_or_name: str):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)
    guild_id = interaction.guild_id

    bot.cursor.execute(
        "SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)",
        (guild_id, slot_id_or_name, slot_id_or_name)
    )
    slot_data = bot.cursor.fetchone()

    if not slot_data:
        await interaction.followup.send(f"Could not find a slot with ID or name '{slot_id_or_name}' in this guild.", ephemeral=True)
        return

    slot_id, slot_name = slot_data

    confirm_view = discord.ui.View()
    confirm_button = discord.ui.Button(label="Confirm Deletion", style=discord.ButtonStyle.danger, custom_id=f"confirm_delete_slot_{slot_id}")
    cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary, custom_id="cancel_delete_slot")

    async def confirm_callback(intx: discord.Interaction):
        if intx.user != interaction.user:
            await intx.response.send_message("You are not authorized to confirm this action.", ephemeral=True)
            return

        await intx.response.defer(ephemeral=True)
        try:
            # Due to ON DELETE CASCADE, deleting from 'slots' should delete from related tables.
            bot.cursor.execute("DELETE FROM slots WHERE slot_id = ?", (slot_id,))
            bot.conn.commit()
            await intx.followup.send(f"Slot '{slot_name}' (ID: `{slot_id[:8]}...`) and all its associated data have been deleted.", ephemeral=True)
            await _log_setting_change(
                interaction.guild, interaction.user,
                "Slot Deleted", f"Slot Name: {slot_name}, ID: {slot_id}", "N/A",
                slot_id=slot_id, slot_name=slot_name
            )
            # BUG FIX: Safely disable view/buttons and ignore NotFound error from editing deleted/unavailable messages
            try:
                if intx.message:
                    await intx.message.edit(view=None)
                else:
                    await intx.edit_original_response(view=None)
            except discord.errors.NotFound:
                pass
            except Exception as e:
                print(f"Non-404 error editing message/view: {e}")
        except Exception as e:
            print(f"Error deleting slot: {e}")
            import traceback
            traceback.print_exc()
            await intx.followup.send("An error occurred while deleting the slot. Please try again.", ephemeral=True)

    async def cancel_callback(intx: discord.Interaction):
        if intx.user != interaction.user:
            await intx.response.send_message("You are not authorized to cancel this action.", ephemeral=True)
            return
        try:
            if intx.message:
                await intx.message.edit(content="Slot deletion cancelled.", view=None)
            else:
                await intx.edit_original_response(content="Slot deletion cancelled.", view=None)
        except discord.errors.NotFound:
            pass
        except Exception as e:
            print(f"Non-404 error editing message/view: {e}")

    confirm_button.callback = confirm_callback
    cancel_button.callback = cancel_callback
    confirm_view.add_item(confirm_button)
    confirm_view.add_item(cancel_button)

    await interaction.followup.send(
        f"Are you sure you want to delete slot '{slot_name}' (ID: `{slot_id[:8]}...`)? This action is irreversible and will delete ALL associated configurations, teams, transactions, and applications for this slot.",
        view=confirm_view,
        ephemeral=True
    )


@bot.tree.command(name="list_slots", description="List all league configuration slots for this guild.")
async def list_slots(interaction: discord.Interaction):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)
    guild_id = interaction.guild_id

    bot.cursor.execute("SELECT slot_id, slot_name, description FROM slots WHERE guild_id = ?", (guild_id,))
    slots = bot.cursor.fetchall()

    if not slots:
        embed = discord.Embed(
            title="League Slots",
            description="No league slots configured for this guild yet. Use `/create_slot <name>` to add one!",
            color=discord.Color.orange()
        )
    else:
        embed = discord.Embed(
            title=f"League Slots for {interaction.guild.name}",
            description=f"You have {len(slots)}/5 slots configured.",
            color=discord.Color.blue()
        )
        for slot_id, slot_name, description in slots:
            embed.add_field(
                name=f"**{slot_name}** (`{slot_id[:8]}...`)",
                value=f"Description: {description or 'None'}\n"
                      f"Commands: `/setup {slot_name}` or `/setup {slot_id}`",
                inline=False
            )
    await interaction.followup.send(embed=embed, ephemeral=True)


@bot.tree.command(name="apply", description="Apply for a management position within a specific league slot.")
@app_commands.describe(
    slot_id_or_name="The ID or name of the slot you are applying for. Use `/list_slots` to see available slots.",
    position="The management position you wish to apply for."
)
@app_commands.choices(position=[
    app_commands.Choice(name="Franchise Owner", value="Franchise Owner of a team"),
    app_commands.Choice(name="General Manager", value="General Manager of a team"),
    app_commands.Choice(name="Head Coach", value="Head Coach of a team"),
])
async def apply(interaction: discord.Interaction, slot_id_or_name: str, position: str):
    guild_id = interaction.guild_id
    
    # 1. Find the slot
    bot.cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)", (guild_id, slot_id_or_name, slot_id_or_name))
    slot_data = bot.cursor.fetchone()

    if not slot_data:
        await interaction.response.send_message(f"Could not find a slot with ID or name '{slot_id_or_name}'. Please use `/list_slots` to see available slots.", ephemeral=True)
        return

    selected_slot_id, selected_slot_name = slot_data

    # 2. Check global application channel and blacklist role
    guild_settings = await bot.get_guild_settings(guild_id)
    blacklist_role_id = guild_settings.get('application_blacklist_role')
    application_channel_id = guild_settings.get('application_channel')

    if blacklist_role_id:
        blacklist_role = interaction.guild.get_role(blacklist_role_id)
        if blacklist_role and blacklist_role in interaction.user.roles:
            await interaction.response.send_message("You are currently blacklisted from submitting applications.", ephemeral=True)
            return
            
    if not application_channel_id:
        await interaction.response.send_message("The global application submission channel has not been set up by an administrator. Please contact server staff.", ephemeral=True)
        return

    # 3. Present the modal
    modal = ApplicationModal(bot, position, selected_slot_id, selected_slot_name)
    await interaction.response.send_modal(modal)


@bot.event
async def on_ready():
    print(f'Bot is online and ready and has now logged in as {bot.user}')
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")
    
    # Load persistent views (only if they are expected to persist across bot restarts, like ApplicationButtonsView)
    bot.add_view(ApplicationButtonsView(bot))
    print("Loaded ApplicationButtonsView for persistent buttons.")


@bot.event
async def on_guild_join(guild: discord.Guild):
    # Create the welcome embed
    embed = discord.Embed(
        title="👋 Hello! RosterFlow is here!",
        description=(
            "Thanks for adding me to your server! I'm RosterFlow, your go-to bot for league and team management.\n\n"
            "**To get started, an administrator (or user with the Bot Admin Role, if configured) needs to run the `/global_setup` command first.**\n"
            "This will configure essential server-wide settings.\n\n"
            "After global setup, you can then:\n"
            "• Create league slots: `/create_slot <name>` (Max 5 slots per guild)\n"
            "• Configure specific slots: `/setup <slot_id_or_name>`\n"
            "• List your slots: `/list_slots`\n\n"
            "I look forward to helping you manage your league smoothly!"
            "liking rosterflow so far? send a review [https://top.gg/bot/1351652889935745104#reviews](https://top.gg/bot/1351652889935745104#reviews)"
        ),
        color=discord.Color.blue()
    )
    embed.set_footer(text="Use /global_setup to begin configuration.")
    embed.add_field(name="🚀 Quick Start (Experimental):", value="Users with permission can also try `/auto_setup` for a faster, name-based initial configuration of roles and channels.", inline=False)

    sent_in_channel = False
    target_channel = None

    if guild.system_channel and guild.system_channel.permissions_for(guild.me).send_messages:
        target_channel = guild.system_channel
    else:
        for channel in guild.text_channels:
            if channel.permissions_for(guild.me).send_messages:
                target_channel = channel
                break

    if target_channel:
        try:
            await target_channel.send(f"Hey {guild.owner.mention}!", embed=embed)
            print(f"Successfully sent welcome message to channel {target_channel.name} in guild {guild.name} ({guild.id}).")
            sent_in_channel = True
        except discord.Forbidden:
            print(f"Could not send welcome message in {target_channel.name} of guild {guild.name} ({guild.id}) due to permissions.")
        except Exception as e:
            print(f"Failed to send welcome message in channel {target_channel.name} of guild {guild.name} ({guild.id}): {e}")
    else:
        print(f"No suitable text channel found with send permissions in guild {guild.name} ({guild.id}).")

    if not sent_in_channel:
        owner = guild.owner
        if owner:
            try:
                await owner.send(embed=embed)
            except discord.Forbidden:
                print(f"Could not DM welcome message to owner {owner.name} in guild {guild.name} ({guild.id}) - DMs might be closed or blocked.")
            except Exception as e:
                print(f"Failed to DM welcome message to owner {owner.name} in guild {guild.name} ({guild.id}): {e}")
        else:
            print(f"Could not find owner for guild {guild.name} ({guild.id}) to send welcome message via DM.")
    
    # Ensure a default "main" slot exists for the newly joined guild
    default_slot_name = "main"
    bot.cursor.execute("SELECT slot_id FROM slots WHERE guild_id = ? AND slot_name = ?", (guild.id, default_slot_name))
    if not bot.cursor.fetchone():
        default_slot_id = uuid.uuid4().hex
        try:
            bot.cursor.execute(
                "INSERT INTO slots (slot_id, guild_id, slot_name, description) VALUES (?, ?, ?, ?)",
                (default_slot_id, guild.id, default_slot_name, "Default league configuration slot.")
            )
            bot.cursor.execute("INSERT OR IGNORE INTO slot_configs (slot_id) VALUES (?)", (default_slot_id,))
            bot.cursor.execute("INSERT OR IGNORE INTO slot_command_settings (slot_id) VALUES (?)", (default_slot_id,))
            bot.cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (default_slot_id,))
            bot.conn.commit()
            print(f"Automatically created default 'main' slot for new guild {guild.name} ({guild.id}).")
            await _log_setting_change(
                guild, bot.user, # Use bot.user as the editor for auto-creation
                "Default Slot Auto-Created", "N/A", f"Slot Name: {default_slot_name}, ID: {default_slot_id}",
                slot_id=default_slot_id, slot_name=default_slot_name
            )
        except Exception as e:
            print(f"Error auto-creating default 'main' slot for guild {guild.id}: {e}")
            traceback.print_exc()

import discord
from discord import app_commands
import sqlite3
import traceback
import re
import unicodedata
import uuid
from typing import Optional

def normalize_discord_name(name: str) -> str:
    """
    Strips Discord markdown, special characters, and normalizes unicode for robust matching.
    """
    name = unicodedata.normalize('NFKD', name)
    name = name.encode('ascii', 'ignore').decode('utf-8')
    name = re.sub(r'(\*\*|__|\*|`|~|\|\|)+', '', name)
    name = re.sub(r'[-_.,!?;:#/&@$%^()\[\]{}<>]', ' ', name)
    name = re.sub(r'\s+', ' ', name).strip()
    return name.lower()

def find_best_match(mapping, name):
    """
    Attempts to find the best match for a key in mapping against a normalized name.
    """
    normalized = normalize_discord_name(name)
    for key, variants in mapping.items():
        for variant in variants:
            if variant in normalized:
                return key
    return None

# ---------------- GLOBAL AUTOSETUP ----------------

@bot.tree.command(name="autosetup_global", description="Automatically configures global bot settings based on role and channel names.")
@app_commands.checks.has_permissions(administrator=True)
async def autosetup_global(interaction: discord.Interaction):
    await interaction.response.defer(ephemeral=True)
    try:
        # This mapping is designed to be as direct as possible and robust to style/format
        role_map = {
            "admin_role": ["admin", "bot admin", "administrator", "mod", "moderator"],
            "application_blacklist_role": ["blacklist", "no apply", "app ban", "application blacklist"],
            "suspended_role": ["suspended", "banished", "timeout", "banned"],
        }
        channel_map = {
            "log_channel": ["log", "setup log", "config log", "admin log", "mod log"],
            "application_channel": ["application", "applications", "apply", "recruitment"],
            "suspended_channel": ["suspended", "suspension", "punishment", "discipline"],
        }
        friendly_names = {
            "admin_role": "Bot Admin Role",
            "application_blacklist_role": "Application Blacklist Role",
            "suspended_role": "Suspended Role",
            "log_channel": "Setup Log Channel",
            "application_channel": "Application Submission Channel",
            "suspended_channel": "Suspended Logger Channel",
        }
        updates = {}
        found_roles = []
        found_channels = []

        # Detect roles
        for role in interaction.guild.roles:
            match = find_best_match(role_map, role.name)
            if match and match not in updates:
                updates[match] = role.id
                found_roles.append(f"✅ {role.mention} set as {friendly_names.get(match, match)}")

        # Detect channels
        for channel in interaction.guild.text_channels:
            match = find_best_match(channel_map, channel.name)
            if match and match not in updates:
                updates[match] = channel.id
                found_channels.append(f"✅ {channel.mention} set as {friendly_names.get(match, match)}")

        # Save to DB
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        cursor.execute("INSERT OR IGNORE INTO guild_settings (guild_id) VALUES (?)", (interaction.guild.id,))
        set_parts = ", ".join([f"{key} = ?" for key in updates.keys()])
        values = list(updates.values())
        if updates:
            cursor.execute(f"UPDATE guild_settings SET {set_parts} WHERE guild_id = ?", (*values, interaction.guild.id))
            conn.commit()
        conn.close()

        # Embed
        embed = discord.Embed(
            title="✨ Global Auto-Setup Complete",
            color=discord.Color.green()
        )
        if found_roles:
            embed.add_field(name="Configured Global Roles", value="\n".join(found_roles), inline=False)
        if found_channels:
            embed.add_field(name="Configured Global Channels", value="\n".join(found_channels), inline=False)
        if not found_roles and not found_channels:
            embed.description = "No suitable roles or channels found for global auto-setup."
        embed.set_footer(text="Use /global_setup to review or edit.")

        await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        traceback.print_exc()
        await interaction.followup.send(f"Error during autosetup_global: {e}", ephemeral=True)

# ---------------- SLOT AUTOSETUP ----------------

@bot.tree.command(name="autosetup_slot", description="Automatically configures a slot's management/staff roles and channels by name.")
@app_commands.describe(
    slot_id_or_name="The ID or name of the slot to auto-setup (defaults to 'main' if not specified)."
)
@app_commands.checks.has_permissions(administrator=True)
async def autosetup_slot(
    interaction: discord.Interaction,
    slot_id_or_name: Optional[str] = None
):
    await interaction.response.defer(ephemeral=True)
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        guild_id = interaction.guild.id

        # 1. Slot resolution (main if not given)
        if slot_id_or_name:
            cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)", (guild_id, slot_id_or_name, slot_id_or_name))
            slot = cursor.fetchone()
            if not slot:
                await interaction.followup.send(f"Slot '{slot_id_or_name}' not found. Use `/list_slots` to see available slots.", ephemeral=True)
                return
            slot_id, slot_name = slot
        else:
            cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND slot_name = ?", (guild_id, "main"))
            slot = cursor.fetchone()
            if not slot:
                # Create "main" slot if it doesn't exist
                slot_id = str(uuid.uuid4().hex)
                slot_name = "main"
                cursor.execute("INSERT INTO slots (slot_id, guild_id, slot_name, description) VALUES (?, ?, ?, ?)", (slot_id, guild_id, slot_name, "Auto-created main slot"))
                cursor.execute("INSERT OR IGNORE INTO slot_configs (slot_id) VALUES (?)", (slot_id,))
                cursor.execute("INSERT OR IGNORE INTO slot_command_settings (slot_id) VALUES (?)", (slot_id,))
                cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (slot_id,))
                conn.commit()
            else:
                slot_id, slot_name = slot

        # 2. Detection maps (slot configuration keys)
        # Enforce all variable names (except pickup_host_role) to use 5+ chars
        role_map = {
            "franchise_owner_role": ["franchise owner", "owner", "co-owner", "franchiseowner"],
            "general_manager_role": ["general manager", "gm", "manager", "gen manager", "generalmanager"],
            "head_coach_role": ["head coach", "coach", "headcoach"],
            "assistant_coach_role": ["assistant coach", "asst coach", "assistantcoach"],
            "free_agent_role": ["free agent", "freeagent"],
            "referee_role": ["referee", "ref", "official"],
            "streamer_role": ["streamer", "broadcast", "caster", "broadcaster"],
            "pickup_host_role": ["pickup host", "host", "pickuphost"], # exception: host allowed
        }
        channel_map = {
            "transaction_channel": ["transaction", "transactions", "sign", "signings", "waivers"],
            "gametime_channel": ["game time", "gametimes", "schedule", "gametime", "schedules"],
            "trade_channel": ["trade", "trades"],
            "draft_channel": ["draft", "drafts"],
            "pickup_channel": ["pickup channel", "pickup", "pickups"],
        }
        friendly_names = {
            "franchise_owner_role": "Franchise Owner Role",
            "general_manager_role": "General Manager Role",
            "head_coach_role": "Head Coach Role",
            "assistant_coach_role": "Assistant Coach Role",
            "free_agent_role": "Free Agent Role",
            "referee_role": "Referee Role",
            "streamer_role": "Streamer Role",
            "pickup_host_role": "Pickup Host Role",
            "transaction_channel": "Transaction Channel",
            "gametime_channel": "Gametime Channel",
            "trade_channel": "Trade Channel",
            "draft_channel": "Draft Channel",
            "pickup_channel": "Pickup Channel"
        }
        updates = {}
        found_roles = []
        found_channels = []

        # 3. Detect slot roles
        for role in interaction.guild.roles:
            match = find_best_match(role_map, role.name)
            if match and match not in updates:
                updates[match] = role.id
                found_roles.append(f"✅ {role.mention} set as {friendly_names.get(match, match)}")

        # 4. Detect slot channels
        for channel in interaction.guild.text_channels:
            match = find_best_match(channel_map, channel.name)
            if match and match not in updates:
                updates[match] = channel.id
                found_channels.append(f"✅ {channel.mention} set as {friendly_names.get(match, match)}")

        # 5. Save to DB
        set_parts = ", ".join([f"{key} = ?" for key in updates.keys()])
        values = list(updates.values())
        if updates:
            cursor.execute(f"INSERT OR IGNORE INTO slot_configs (slot_id) VALUES (?)", (slot_id,))
            cursor.execute(f"UPDATE slot_configs SET {set_parts} WHERE slot_id = ?", (*values, slot_id))
            conn.commit()
        conn.close()

        # 6. Respond
        embed = discord.Embed(
            title=f"✨ Slot Auto-Setup Complete - Slot: {slot_name}",
            color=discord.Color.green()
        )
        if found_roles:
            embed.add_field(name="Configured Slot Roles", value="\n".join(found_roles), inline=False)
        if found_channels:
            embed.add_field(name="Configured Slot Channels", value="\n".join(found_channels), inline=False)
        if not found_roles and not found_channels:
            embed.description = "No suitable roles or channels found for slot auto-setup."
        embed.set_footer(text="Use /setup to review or edit.")

        await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        traceback.print_exc()
        await interaction.followup.send(f"Error during autosetup_slot: {e}", ephemeral=True)

from discord import app_commands
from typing import Optional
import discord

@bot.tree.command(name="reset_slot", description="Resets all configuration for a slot. Optionally clears all teams.")
@app_commands.describe(
    slot_id_or_name="The ID or name of the slot you want to reset.",
    clear_teams="If true, will also delete all teams from the slot."
)
async def reset_slot(
    interaction: discord.Interaction,
    slot_id_or_name: str,
    clear_teams: Optional[bool] = False
):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)
    guild_id = interaction.guild_id

    # Find the slot
    bot.cursor.execute(
        "SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)",
        (guild_id, slot_id_or_name, slot_id_or_name)
    )
    slot_data = bot.cursor.fetchone()

    if not slot_data:
        await interaction.followup.send(f"Could not find a slot with ID or name '{slot_id_or_name}' in this guild.", ephemeral=True)
        return

    slot_id, slot_name = slot_data

    # Confirmation view
    confirm_view = discord.ui.View()
    confirm_button = discord.ui.Button(label="Confirm Reset", style=discord.ButtonStyle.danger, custom_id=f"confirm_reset_slot_{slot_id}")
    cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary, custom_id="cancel_reset_slot")

    async def confirm_callback(intx: discord.Interaction):
        if intx.user != interaction.user:
            await intx.response.send_message("You are not authorized to confirm this action.", ephemeral=True)
            return

        await intx.response.defer(ephemeral=True)
        try:
            # Reset configs
            bot.cursor.execute("DELETE FROM slot_configs WHERE slot_id = ?", (slot_id,))
            bot.cursor.execute("DELETE FROM slot_command_settings WHERE slot_id = ?", (slot_id,))
            bot.cursor.execute("DELETE FROM slot_demand_config WHERE slot_id = ?", (slot_id,))
            # Optionally clear teams
            if clear_teams:
                bot.cursor.execute("DELETE FROM teams WHERE slot_id = ?", (slot_id,))
                bot.cursor.execute("DELETE FROM league_teams WHERE slot_id = ?", (slot_id,))
            # Re-initialize default configs for the slot
            bot.cursor.execute("INSERT OR IGNORE INTO slot_configs (slot_id) VALUES (?)", (slot_id,))
            bot.cursor.execute("INSERT OR IGNORE INTO slot_command_settings (slot_id) VALUES (?)", (slot_id,))
            bot.cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (slot_id,))
            bot.conn.commit()
            await intx.followup.send(
                f"Slot '{slot_name}' (ID: `{slot_id[:8]}...`) has been reset. {'Teams were also cleared.' if clear_teams else 'Teams were preserved.'}",
                ephemeral=True
            )
            await _log_setting_change(
                interaction.guild, interaction.user,
                "Slot Reset", f"Slot Name: {slot_name}, ID: {slot_id}",
                f"Reset configs. {'Teams cleared.' if clear_teams else 'Teams preserved.'}",
                slot_id=slot_id, slot_name=slot_name
            )
            # BUG FIX: Safely disable view/buttons and ignore NotFound error from editing deleted/unavailable messages
            try:
                if intx.message:
                    await intx.message.edit(view=None)
                else:
                    await intx.edit_original_response(view=None)
            except discord.errors.NotFound:
                # The original message is gone, don't show a misleading error
                pass
            except Exception as e:
                print(f"Non-404 error editing message/view: {e}")
        except Exception as e:
            print(f"Error resetting slot: {e}")
            import traceback
            traceback.print_exc()
            await intx.followup.send("An error occurred while resetting the slot. Please try again.", ephemeral=True)

    async def cancel_callback(intx: discord.Interaction):
        if intx.user != interaction.user:
            await intx.response.send_message("You are not authorized to cancel this action.", ephemeral=True)
            return
        try:
            if intx.message:
                await intx.message.edit(content="Slot reset cancelled.", view=None)
            else:
                await intx.edit_original_response(content="Slot reset cancelled.", view=None)
        except discord.errors.NotFound:
            pass
        except Exception as e:
            print(f"Non-404 error editing message/view: {e}")

    confirm_button.callback = confirm_callback
    cancel_button.callback = cancel_callback
    confirm_view.add_item(confirm_button)
    confirm_view.add_item(cancel_button)

    await interaction.followup.send(
        f"Are you sure you want to reset slot '{slot_name}' (ID: `{slot_id[:8]}...`)?\n"
        f"This will erase all configuration for the slot{' and all teams' if clear_teams else ''}. This cannot be undone.",
        view=confirm_view,
        ephemeral=True
    )



@bot.tree.command(name="guild_reset", description="Deletes ALL slots and ALL configuration for this server. Owner only!")
async def reset(interaction: discord.Interaction):
    # Only allow the server owner to use this command
    if interaction.user.id != interaction.guild.owner_id:
        await interaction.response.send_message("Only the server owner can use this command.", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)
    guild_id = interaction.guild.id

    # Confirmation dialog
    confirm_view = discord.ui.View()
    confirm_button = discord.ui.Button(label="Confirm RESET (IRREVERSIBLE)", style=discord.ButtonStyle.danger, custom_id="confirm_reset_guild")
    cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary, custom_id="cancel_reset_guild")

    async def confirm_callback(intx: discord.Interaction):
        if intx.user != interaction.user:
            await intx.response.send_message("You are not authorized to confirm this action.", ephemeral=True)
            return

        await intx.response.defer(ephemeral=True)
        try:
            # Delete all slot-based data for this guild
            bot.cursor.execute("DELETE FROM slots WHERE guild_id = ?", (guild_id,))
            bot.cursor.execute("DELETE FROM guild_settings WHERE guild_id = ?", (guild_id,))
            # Optionally, delete from teams, applications, league_teams that are now orphaned (should be cascaded)
            bot.conn.commit()
            await intx.followup.send("All slots and all configuration for this server have been deleted. You must run /global_setup again.", ephemeral=True)
            try:
                if intx.message:
                    await intx.message.edit(view=None)
                else:
                    await intx.edit_original_response(view=None)
            except discord.errors.NotFound:
                pass
        except Exception as e:
            print(f"Error running /reset: {e}")
            import traceback
            traceback.print_exc()
            await intx.followup.send("An error occurred while resetting the server. Please try again.", ephemeral=True)

    async def cancel_callback(intx: discord.Interaction):
        if intx.user != interaction.user:
            await intx.response.send_message("You are not authorized to cancel this action.", ephemeral=True)
            return
        try:
            if intx.message:
                await intx.message.edit(content="Server reset cancelled.", view=None)
            else:
                await intx.edit_original_response(content="Server reset cancelled.", view=None)
        except discord.errors.NotFound:
            pass

    confirm_button.callback = confirm_callback
    cancel_button.callback = cancel_callback
    confirm_view.add_item(confirm_button)
    confirm_view.add_item(cancel_button)

    await interaction.followup.send(
        "⚠️ **Are you SURE?**\n"
        "This will permanently DELETE ALL slots and ALL configuration for this server. "
        "This cannot be undone! You will have to re-run /global_setup and all slot setups.\n\n"
        "Click **Confirm RESET** to proceed.",
        view=confirm_view, ephemeral=True
    )




import sqlite3
import uuid
import traceback
import discord
from discord import app_commands
from typing import Optional

def normalize_team_name(name: str) -> str:
    return name.lower().replace('-', ' ').replace('_', ' ')

# --- Full Predefined Team Lists ---

NFL_TEAMS: List[str] = [
    normalize_team_name("Arizona Cardinals"), normalize_team_name("Atlanta Falcons"),
    normalize_team_name("Baltimore Ravens"), normalize_team_name("Buffalo Bills"),
    normalize_team_name("Carolina Panthers"), normalize_team_name("Chicago Bears"),
    normalize_team_name("Cincinnati Bengals"), normalize_team_name("Cleveland Browns"),
    normalize_team_name("Dallas Cowboys"), normalize_team_name("Denver Broncos"),
    normalize_team_name("Detroit Lions"), normalize_team_name("Green Bay Packers"),
    normalize_team_name("Houston Texans"), normalize_team_name("Indianapolis Colts"),
    normalize_team_name("Jacksonville Jaguars"), normalize_team_name("Kansas City Chiefs"),
    normalize_team_name("Las Vegas Raiders"), normalize_team_name("Los Angeles Chargers"),
    normalize_team_name("Los Angeles Rams"), normalize_team_name("Miami Dolphins"),
    normalize_team_name("Minnesota Vikings"), normalize_team_name("New England Patriots"),
    normalize_team_name("New Orleans Saints"), normalize_team_name("New York Giants"),
    normalize_team_name("New York Jets"), normalize_team_name("Philadelphia Eagles"),
    normalize_team_name("Pittsburgh Steelers"), normalize_team_name("San Francisco 49ers"),
    normalize_team_name("Seattle Seahawks"), normalize_team_name("Tampa Bay Buccaneers"),
    normalize_team_name("Tennessee Titans"), normalize_team_name("Washington Commanders")
]

COLLEGE_TEAMS: List[str] = [
    normalize_team_name("Alabama Crimson Tide"), normalize_team_name("Clemson Tigers"),
    normalize_team_name("Ohio State Buckeyes"), normalize_team_name("Oklahoma Sooners"),
    normalize_team_name("Georgia Bulldogs"), normalize_team_name("LSU Tigers"),
    normalize_team_name("Michigan Wolverines"), normalize_team_name("Notre Dame Fighting Irish"),
    normalize_team_name("Texas Longhorns"), normalize_team_name("Florida Gators"),
    normalize_team_name("Penn State Nittany Lions"), normalize_team_name("Auburn Tigers"),
    normalize_team_name("Wisconsin Badgers"), normalize_team_name("Oregon Ducks"),
    normalize_team_name("USC Trojans"), normalize_team_name("Florida State Seminoles"),
    normalize_team_name("Texas A&M Aggies"), normalize_team_name("Iowa Hawkeyes"),
    normalize_team_name("Utah Utes"), normalize_team_name("Boise State Broncos"),
    normalize_team_name("Michigan State Spartans"), normalize_team_name("Oklahoma State Cowboys"),
    normalize_team_name("TCU Horned Frogs"), normalize_team_name("Baylor Bears"),
    normalize_team_name("Tennessee Volunteers"), normalize_team_name("Nebraska Cornhuskers"),
    normalize_team_name("Miami Hurricanes"), normalize_team_name("Virginia Tech Hokies"),
    normalize_team_name("North Carolina Tar Heels"), normalize_team_name("Louisville Cardinals"),
    normalize_team_name("Kentucky Wildcats"), normalize_team_name("Ole Miss Rebels"),
    normalize_team_name("Mississippi State Bulldogs"), normalize_team_name("Arkansas Razorbacks"),
    normalize_team_name("Missouri Tigers"), normalize_team_name("South Carolina Gamecocks"),
    normalize_team_name("NC State Wolfpack"), normalize_team_name("Wake Forest Demon Deacons"),
    normalize_team_name("Duke Blue Devils"), normalize_team_name("Boston College Eagles"),
    normalize_team_name("Syracuse Orange"), normalize_team_name("Pittsburgh Panthers"),
    normalize_team_name("Purdue Boilermakers"), normalize_team_name("Indiana Hoosiers"),
    normalize_team_name("Illinois Fighting Illini"), normalize_team_name("Northwestern Wildcats"),
    normalize_team_name("Minnesota Golden Gophers"), normalize_team_name("Maryland Terrapins"),
    normalize_team_name("Rutgers Scarlet Knights"), normalize_team_name("Arizona Wildcats"),
    normalize_team_name("Arizona State Sun Devils"), normalize_team_name("California Golden Bears"),
    normalize_team_name("Colorado Buffaloes"), normalize_team_name("Oregon State Beavers"),
    normalize_team_name("Stanford Cardinal"), normalize_team_name("UCLA Bruins"),
    normalize_team_name("Washington Huskies"), normalize_team_name("Washington State Cougars"),
    normalize_team_name("West Virginia Mountaineers"), normalize_team_name("Kansas Jayhawks"),
    normalize_team_name("Kansas State Wildcats"), normalize_team_name("Iowa State Cyclones"),
    normalize_team_name("Texas Tech Red Raiders"), normalize_team_name("Houston Cougars"),
    normalize_team_name("BYU Cougars"), normalize_team_name("UCF Knights"),
    normalize_team_name("Cincinnati Bearcats"), normalize_team_name("SMU Mustangs"),
    normalize_team_name("Tulane Green Wave"), normalize_team_name("Navy Midshipmen"),
    normalize_team_name("Army Black Knights"), normalize_team_name("Air Force Falcons")
]

ULTIMATE_FOOTBALL_TEAMS: List[str] = [
    normalize_team_name("Alabama Black Bears"), normalize_team_name("Anchorage Polar Bears"),
    normalize_team_name("Arizona Firebirds"), normalize_team_name("Atlantis Tridents"),
    normalize_team_name("Baltimore Bats"), normalize_team_name("Barton Bruisers"),
    normalize_team_name("Canton Bulldogs"), normalize_team_name("Charlotte Monarchs"),
    normalize_team_name("Chicago Cougars"), normalize_team_name("Colorado Blizzards"),
    normalize_team_name("Culicán Red Devils"), normalize_team_name("Dallas Dragons"),
    normalize_team_name("Dallas Gunslingers"), normalize_team_name("Daytona Dolphins"),
    normalize_team_name("El Paso Longhorns"), normalize_team_name("Fairbank Reindeer"),
    normalize_team_name("Glendale Ghosts"), normalize_team_name("Honolulu Volcanoes"),
    normalize_team_name("Houston Hornets"), normalize_team_name("Korblox Kobras"),
    normalize_team_name("Las Vegas Jackpots"), normalize_team_name("Los Angeles Tigers"),
    normalize_team_name("Lua Lions"), normalize_team_name("Memphis Kings"),
    normalize_team_name("Mexico City Aztecs"), normalize_team_name("Miami Sunshine"),
    normalize_team_name("Minnesota Huskies"), normalize_team_name("Monterey Sharks"),
    normalize_team_name("Montreal Angels"), normalize_team_name("Nashville Nightmares"),
    normalize_team_name("Nebraska Sabertooths"), normalize_team_name("Nevada Miners"),
    normalize_team_name("New England Musketeers"), normalize_team_name("New York Knights"),
    normalize_team_name("Oklahoma Storm Chasers"), normalize_team_name("Oregon Pioneers"),
    normalize_team_name("Orlando Lightning"), normalize_team_name("Pemberly Punishers"),
    normalize_team_name("Philadelphia Liberties"), normalize_team_name("Raliegh Rattle"),
    normalize_team_name("Roblox Warriors"), normalize_team_name("Rushmore Rhinos"),
    normalize_team_name("Sacramento Coyotes"), normalize_team_name("Salt Lake City Stallions"),
    normalize_team_name("San Diego Cruisers"), normalize_team_name("San Francisco Comets"),
    normalize_team_name("San José Scarecrows"), normalize_team_name("Santa Fe Outlaws"),
    normalize_team_name("Seattle Evergreens"), normalize_team_name("Toronto Stars"),
    normalize_team_name("Vancouver Vikings"), normalize_team_name("Veracruz Jaguars"),
    normalize_team_name("Winchester Widows"), normalize_team_name("Windsor White Beards")
]

FOOTBALL_FUSION_TEAMS: List[str] = [
    normalize_team_name("Arizona Heat"), normalize_team_name("Atlanta Nighthawks"),
    normalize_team_name("Baltimore Royals"), normalize_team_name("Buffalo Boars"),
    normalize_team_name("Carolina Predators"), normalize_team_name("Chicago Bucks"),
    normalize_team_name("Cincinnati Tigers"), normalize_team_name("Cleveland Braves"),
    normalize_team_name("Dallas Outlaws"), normalize_team_name("Denver Mustangs"),
    normalize_team_name("Detroit Lightning"), normalize_team_name("Green Bay Paladins"),
    normalize_team_name("Houston Drillers"), normalize_team_name("Indianapolis Cubs"),
    normalize_team_name("Jacksonville Devils"), normalize_team_name("Kansas City Cherokees"),
    normalize_team_name("Las Vegas Renegades"), normalize_team_name("Los Angeles Comets"),
    normalize_team_name("Los Angeles Rage"), normalize_team_name("Miami Sharks"),
    normalize_team_name("Minnesota Vipers"), normalize_team_name("New England Nationals"),
    normalize_team_name("New Orleans Knights"), normalize_team_name("New York Greats"),
    normalize_team_name("New York Juggernauts"), normalize_team_name("Philadelphia Raptors"),
    normalize_team_name("Pittsburgh Stormers"), normalize_team_name("San Francisco Lions"),
    normalize_team_name("Seattle Cyclones"), normalize_team_name("Tampa Bay Pirates"),
    normalize_team_name("Tennessee Trojans"), normalize_team_name("Washington Warriors")
]

async def has_bot_management_permission(interaction: discord.Interaction) -> bool:
    if interaction.user.guild_permissions.administrator:
        return True
    guild_settings = await bot.get_guild_settings(interaction.guild.id)
    admin_role_id = guild_settings.get('admin_role')
    if admin_role_id and interaction.guild:
        admin_role_obj = interaction.guild.get_role(admin_role_id)
        if admin_role_obj and admin_role_obj in interaction.user.roles:
            return True
    return False

async def get_or_create_default_slot(guild_id: int) -> (str, str):
    bot.cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND slot_name = ?", (guild_id, "Default"))
    slot = bot.cursor.fetchone()
    if slot:
        return slot[0], slot[1]
    bot.cursor.execute("SELECT COUNT(*) FROM slots WHERE guild_id = ?", (guild_id,))
    current_slots = bot.cursor.fetchone()[0]
    if current_slots >= 5:
        return None, None
    slot_id = uuid.uuid4().hex
    bot.cursor.execute(
        "INSERT INTO slots (slot_id, guild_id, slot_name, description) VALUES (?, ?, ?, ?)",
        (slot_id, guild_id, "Default", "Default slot for teams if no specific slot is chosen.")
    )
    bot.cursor.execute("INSERT OR IGNORE INTO slot_configs (slot_id) VALUES (?)", (slot_id,))
    bot.cursor.execute("INSERT OR IGNORE INTO slot_command_settings (slot_id) VALUES (?)", (slot_id,))
    bot.cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (slot_id,))
    bot.conn.commit()
    return slot_id, "Default"

def split_long_field_values(items, max_len=1024):
    """Split a list of strings into chunks so that each chunk does not exceed max_len."""
    chunks = []
    current = ""
    for item in items:
        if len(current) + len(item) + 1 > max_len:
            chunks.append(current)
            current = item
        else:
            if current:
                current += "\n" + item
            else:
                current = item
    if current:
        chunks.append(current)
    return chunks

@bot.tree.command(name="detect_teams", description="Detects and sets up teams from server roles for a specific slot.")
@app_commands.describe(
    slot_id_or_name="The ID or name of the slot. Leave empty for the 'Default' slot.",
    nfl="Detect NFL teams (true/false)",
    cfb="Detect College Football teams (true/false)",
    ultimate_football="Detect Ultimate Football teams (true/false)",
    football_fusion="Detect Football Fusion teams (true/false)"
)
@app_commands.checks.has_permissions(administrator=True)
async def detect_teams(
    interaction: discord.Interaction,
    slot_id_or_name: Optional[str] = None,
    nfl: Optional[bool] = False,
    cfb: Optional[bool] = False,
    ultimate_football: Optional[bool] = False,
    football_fusion: Optional[bool] = False
):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)
    guild_id = interaction.guild.id

    # --- Slot Resolution ---
    if slot_id_or_name:
        bot.cursor.execute(
            "SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)",
            (guild_id, slot_id_or_name, slot_id_or_name)
        )
        slot_data = bot.cursor.fetchone()
        if not slot_data:
            await interaction.followup.send(
                f"Could not find a slot with ID or name '{slot_id_or_name}'. Please use `/list_slots` to see available slots or `/create_slot` to make a new one.",
                ephemeral=True
            )
            return
        slot_id, slot_name = slot_data
    else:
        slot_id, slot_name = await get_or_create_default_slot(guild_id)
        if not slot_id:
            await interaction.followup.send(
                "Could not determine a default slot or create one (max 5 slots reached). Please specify a slot using `slot_id_or_name`.",
                ephemeral=True
            )
            return

    # --- Team Lists Selection ---
    team_types = []
    team_lists = []
    if nfl:
        team_types.append("NFL")
        team_lists.append(NFL_TEAMS)
    if cfb:
        team_types.append("College Football")
        team_lists.append(COLLEGE_TEAMS)
    if ultimate_football:
        team_types.append("Ultimate Football")
        team_lists.append(ULTIMATE_FOOTBALL_TEAMS)
    if football_fusion:
        team_types.append("Football Fusion")
        team_lists.append(FOOTBALL_FUSION_TEAMS)

    if not team_lists:
        await interaction.followup.send(
            "No team types selected. Please set at least one of nfl, cfb, ultimate_football, or football_fusion to true.",
            ephemeral=True
        )
        return

    # Fetch all roles currently used in league_teams for this guild, across all slots
    bot.cursor.execute(
        "SELECT role_id, team_name, slot_id FROM league_teams"
    )
    all_existing_roles = {(row[0], row[2]): row[1] for row in bot.cursor.fetchall()}
    role_id_to_slot = {}
    for (role_id, slot_id_existing), team_name in all_existing_roles.items():
        role_id_to_slot[str(role_id)] = slot_id_existing

    duplicate_roles = []
    new_teams = []
    updated_teams = []
    for role in interaction.guild.roles:
        if role.name.lower() == '@everyone':
            continue
        normalized = normalize_team_name(role.name)
        found_type = None
        for i, team_list in enumerate(team_lists):
            if normalized in team_list:
                found_type = team_types[i]
                break
        if found_type:
            if str(role.id) in role_id_to_slot and role_id_to_slot[str(role.id)] != slot_id:
                duplicate_roles.append(f"Role {role.mention} (**{role.name}**) is already used in another slot. Remove it from the other slot before adding here.")
                continue
            bot.cursor.execute(
                "SELECT team_name FROM league_teams WHERE role_id = ? AND slot_id = ?",
                (str(role.id), slot_id)
            )
            exists = bot.cursor.fetchone()
            if exists:
                if role.name != exists[0]:
                    bot.cursor.execute(
                        "UPDATE league_teams SET team_name = ?, league_type = ? WHERE role_id = ? AND slot_id = ?",
                        (role.name, found_type, str(role.id), slot_id)
                    )
                    updated_teams.append(f"Updated: **{exists[0]}** → **{role.name}** ({found_type})")
            else:
                bot.cursor.execute(
                    "INSERT INTO league_teams (role_id, slot_id, team_name, league_type, emoji) VALUES (?, ?, ?, ?, ?)",
                    (str(role.id), slot_id, role.name, found_type, "🏈")
                )
                new_teams.append(f"Added: **{role.name}** ({found_type})")

    bot.conn.commit()

    # Compose embed with field splitting to avoid >1024 char error
    embed = discord.Embed(
        title=f"Team Detection Results for Slot: '{slot_name}'",
        color=discord.Color.green()
    )
    # Helper: add multiple fields if needed to keep under 1024 chars
    def add_split_field(embed, title, items):
        if not items:
            return
        chunks = split_long_field_values(items)
        for idx, chunk in enumerate(chunks):
            field_title = title if idx == 0 else f"{title} (cont.)"
            embed.add_field(name=field_title, value=chunk, inline=False)

    add_split_field(embed, "Prevented Duplicates", duplicate_roles)
    add_split_field(embed, "Updated Teams", updated_teams)
    add_split_field(embed, "New Teams Added", new_teams)
    if not new_teams and not updated_teams and not duplicate_roles:
        embed.description = "No new teams detected or existing teams needed updating!"

    await interaction.followup.send(embed=embed, ephemeral=True)

# --- ADD TEAM COMMAND WITH DUPLICATE PREVENTION ---
@app_commands.guild_only()
@bot.tree.command(name="add_team", description="Add a new team to a slot. Each role can only be a team in one slot.")
@app_commands.describe(
    team_role="The role for the team (required). Its name will be used as the team name.",
    emoji="The emoji for the team (required).",
    slot_id_or_name="Optional: The ID or name of the slot to add the team to. Defaults to 'Default' slot."
)
async def add_team(
    interaction: discord.Interaction,
    team_role: discord.Role,
    emoji: str,
    slot_id_or_name: Optional[str] = None,
):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)
    if interaction.guild is None:
        await interaction.followup.send("This command can only be used in a server (guild).", ephemeral=True)
        return

    # --- Slot Resolution ---
    if slot_id_or_name:
        bot.cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)", (interaction.guild.id, slot_id_or_name, slot_id_or_name))
        slot_data = bot.cursor.fetchone()
        if not slot_data:
            await interaction.followup.send(f"Could not find a slot with ID or name '{slot_id_or_name}'. Please use `/list_slots` to see available slots.", ephemeral=True)
            return
        target_slot_id, target_slot_name = slot_data
    else:
        target_slot_id, target_slot_name = await get_or_create_default_slot(interaction.guild.id)
        if not target_slot_id:
            await interaction.followup.send("Could not determine a default slot or create one (max 5 slots reached). Please specify a slot using `slot_id_or_name`.", ephemeral=True)
            return

    # Get all roles used in league_teams (across all slots)
    bot.cursor.execute("SELECT role_id, slot_id FROM league_teams")
    all_team_roles = bot.cursor.fetchall()
    role_id_to_slot = {str(role_id): slot_id for role_id, slot_id in all_team_roles}

    # Duplicate prevention: If this role is already in league_teams for a different slot, prevent
    if str(team_role.id) in role_id_to_slot and role_id_to_slot[str(team_role.id)] != target_slot_id:
        await interaction.followup.send(
            f"Role {team_role.mention} (**{team_role.name}**) is already used as a team in another slot. Remove it from the other slot before adding here.",
            ephemeral=True
        )
        return

    team_name = team_role.name
    try:
        bot.cursor.execute(
            '''INSERT OR REPLACE INTO league_teams
            (role_id, slot_id, team_name, emoji)
            VALUES (?, ?, ?, ?)''',
            (str(team_role.id), target_slot_id, team_name, emoji)
        )
        bot.conn.commit()
        await interaction.followup.send(
            f"Successfully added/updated **{team_name}** {emoji} with role {team_role.mention} in slot '{target_slot_name}'.",
            ephemeral=True
        )
    except sqlite3.IntegrityError as e:
        await interaction.followup.send(
            f"Database integrity error for **{team_name}** {emoji}: {e}. This team might not be added or updated correctly.",
            ephemeral=True
        )
        bot.conn.rollback()
    except Exception as error:
        print(f"Error in add_team command: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f"An unexpected error occurred with the database: {error}", ephemeral=True
        )

# --- REMOVE TEAM COMMAND (single role only) ---
@app_commands.guild_only()
@bot.tree.command(name="remove_team", description="Remove a team from a slot by role.")
@app_commands.describe(
    slot_id_or_name="The ID or name of the slot to remove the team from. Leave blank for the 'Default' slot.",
    team_role="The team role to remove from the slot."
)
async def remove_team(
    interaction: discord.Interaction,
    team_role: discord.Role,
    slot_id_or_name: Optional[str] = None,
):
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)
    if interaction.guild is None:
        await interaction.followup.send("This command can only be used in a server (guild).", ephemeral=True)
        return

    # --- Slot Resolution ---
    if slot_id_or_name:
        bot.cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)", (interaction.guild.id, slot_id_or_name, slot_id_or_name))
        slot_data = bot.cursor.fetchone()
        if not slot_data:
            await interaction.followup.send(f"Could not find a slot with ID or name '{slot_id_or_name}'. Please use `/list_slots` to see available slots.", ephemeral=True)
            return
        target_slot_id, target_slot_name = slot_data
    else:
        target_slot_id, target_slot_name = await get_or_create_default_slot(interaction.guild.id)
        if not target_slot_id:
            await interaction.followup.send("Could not determine a default slot or create one (max 5 slots reached). Please specify a slot using `slot_id_or_name`.", ephemeral=True)
            return

    bot.cursor.execute(
        "DELETE FROM league_teams WHERE role_id = ? AND slot_id = ?",
        (str(team_role.id), target_slot_id)
    )
    if bot.cursor.rowcount > 0:
        msg = f"Removed team **{team_role.name}** ({team_role.mention}) from slot '{target_slot_name}'."
    else:
        msg = f"Team **{team_role.name}** ({team_role.mention}) was not found in slot '{target_slot_name}'."

    bot.conn.commit()
    await interaction.followup.send(msg, ephemeral=True)





import discord
from discord import app_commands, ui
from discord.ext import commands
import sqlite3
import traceback
import re
from datetime import datetime as DateTime, timezone as TimeZone, timedelta as TimeDelta
from typing import Optional, List, Dict, Any, Tuple # Added Tuple for clarity

# Assume 'bot' is an instance of your commands.Bot or a similar class,
# and is initialized with its database connections (bot.conn, bot.cursor).
# The 'bot' instance is passed to views and helper functions where needed.

# --- Helper Functions (used by Sign, Release, Offer commands) ---

def get_emoji_url(emoji_str: Optional[str]) -> Optional[str]:
    """Extract custom emoji ID and convert to URL if possible"""
    if not emoji_str:
        return None
    custom_emoji_match = re.match(r'<a?:[\w]+:(\d+)>', emoji_str)
    if custom_emoji_match:
        emoji_id = custom_emoji_match.group(1)
        return f"https://cdn.discordapp.com/emojis/{emoji_id}.png"
    return None

def get_slot_config(slot_id: str) -> Dict[str, Any]: # Changed from async def to def as per user request
    """Fetches slot-specific configurations from slot_configs, slot_command_settings, and slot_demand_config."""
    config = {}
    try:
        # Fetch from slot_configs
        bot.cursor.execute("SELECT * FROM slot_configs WHERE slot_id = ?", (slot_id,))
        row = bot.cursor.fetchone()
        if row:
            columns = [desc[0] for desc in bot.cursor.description]
            config.update(dict(zip(columns, row)))

        # Fetch from slot_command_settings
        bot.cursor.execute("SELECT * FROM slot_command_settings WHERE slot_id = ?", (slot_id,))
        row = bot.cursor.fetchone()
        if row:
            columns = [desc[0] for desc in bot.cursor.description]
            config.update(dict(zip(columns, row)))

        # Fetch from slot_demand_config (though not directly used by these commands, included for completeness)
        bot.cursor.execute("SELECT * FROM slot_demand_config WHERE slot_id = ?", (slot_id,))
        row = bot.cursor.fetchone()
        if row:
            columns = [desc[0] for desc in bot.cursor.description]
            config.update(dict(zip(columns, row)))

        # Ensure defaults if values are None
        config.setdefault('free_agent_role', None)
        config.setdefault('transaction_channel', None)
        config.setdefault('roster_cap', 25) # Default roster cap
        config.setdefault('sign_enabled', 'on')
        config.setdefault('release_enabled', 'on')
        config.setdefault('offer_enabled', 'on')

    except sqlite3.Error as e:
        print(f"Database error fetching slot config for slot {slot_id}: {e}")
        traceback.print_exc()
    except Exception as e:
        print(f"General error fetching slot config for slot {slot_id}: {e}")
        traceback.print_exc()
    return config

async def get_team_info_for_user(guild_id: int, user_roles: List[int]) -> List[Dict[str, Any]]:
    """
    Fetches all team roles and their associated slot information that a user possesses.
    Returns a list of dictionaries, each containing 'role_id', 'team_name', 'emoji', 'slot_id', 'slot_name'.
    """
    if not user_roles:
        return []

    placeholders = ','.join(['?' for _ in user_roles])
    bot.cursor.execute(f"""
        SELECT lt.role_id, lt.team_name, lt.emoji, s.slot_id, s.slot_name
        FROM league_teams lt
        JOIN slots s ON lt.slot_id = s.slot_id
        WHERE s.guild_id = ? AND lt.role_id IN ({placeholders})
    """, (guild_id, *[str(r) for r in user_roles])) # Ensure role_ids are strings for TEXT column
    
    results = []
    for row in bot.cursor.fetchall():
        results.append({
            'role_id': int(row[0]),
            'team_name': row[1],
            'emoji': row[2],
            'slot_id': row[3],
            'slot_name': row[4]
        })
    return results

async def get_teams_player_is_on(guild_id: int, player_id: int) -> List[Dict[str, Any]]:
    """
    Fetches all teams (role_id, team_name, emoji, slot_id, slot_name) that a specific player is currently on.
    """
    player_member = bot.get_guild(guild_id).get_member(player_id)
    if not player_member:
        try:
            player_member = await bot.get_guild(guild_id).fetch_member(player_id)
        except discord.NotFound:
            return [] # Player not found in guild

    player_team_role_ids = [str(role.id) for role in player_member.roles]
    if not player_team_role_ids:
        return []

    placeholders = ','.join(['?' for _ in player_team_role_ids])
    bot.cursor.execute(f"""
        SELECT lt.role_id, lt.team_name, lt.emoji, s.slot_id, s.slot_name
        FROM league_teams lt
        JOIN slots s ON lt.slot_id = s.slot_id
        WHERE s.guild_id = ? AND lt.role_id IN ({placeholders})
    """, (guild_id, *player_team_role_ids))

    results = []
    for row in bot.cursor.fetchall():
        results.append({
            'role_id': int(row[0]),
            'team_name': row[1],
            'emoji': row[2],
            'slot_id': row[3],
            'slot_name': row[4]
        })
    return results


# --- UI Classes for Selection ---
# Note: TeamSlotSelectionView is no longer directly used by _resolve_team_selection
# when a user manages multiple teams, as per user request.
# However, it remains defined for potential other uses or future changes.

class TeamSlotSelectionView(ui.View):
    def __init__(self, teams: List[Dict[str, Any]], original_interaction: discord.Interaction, command_name: str):
        super().__init__(timeout=180)
        self.teams = teams
        self.original_interaction = original_interaction
        self.command_name = command_name
        self.selected_team_info: Optional[Dict[str, Any]] = None # To store the chosen team/slot info
        self.message = None # To store the message this view is attached to

        options = []
        for team in teams:
            try:
                team_name = team.get('team_name')
                slot_name = team.get('slot_name')
                role_id = team.get('role_id')
                slot_id = team.get('slot_id')

                # Ensure all required fields are present, are of expected types, and are non-empty/non-whitespace strings where appropriate
                if (isinstance(team_name, str) and team_name.strip() and
                    isinstance(slot_name, str) and slot_name.strip() and
                    role_id is not None and isinstance(role_id, int) and
                    isinstance(slot_id, str) and slot_id.strip()):
                    
                    value_str = f"{role_id}_{slot_id}"
                    label_str = f"{team_name} ({slot_name})"
                    
                    # Create a robust description, handling potentially short slot_id
                    description_str = f"Slot ID: {slot_id}"
                    if len(slot_id) > 8:
                        description_str = f"Slot ID: {slot_id[:8]}..."
                    
                    # Ensure label and value are not empty after formatting
                    if not label_str.strip(): # Check if label becomes just whitespace
                        print(f"Warning: Generated label is empty for team: {team}. Skipping option.")
                        continue # Skip this option
                    if not value_str.strip(): # Check if value becomes just whitespace
                        print(f"Warning: Generated value is empty for team: {team}. Skipping option.")
                        continue # Skip this option

                    options.append(discord.SelectOption(
                        label=label_str,
                        value=value_str,
                        description=description_str
                    ))
                else:
                    print(f"Skipping invalid team data for select option: Missing, empty, or incorrect type values in {team}")
            except Exception as e:
                print(f"Skipping invalid team data for select option due to unexpected error: {e} in {team}")

        # Ensure the options list is never empty when passed to ui.Select
        if not options:
            # Provide a dummy option and disable the select if no valid options exist
            options = [discord.SelectOption(label="No teams available", value="no_teams_fallback")]
            is_disabled = True
        else:
            is_disabled = False

        self.add_item(ui.Select(
            custom_id="team_slot_select" if not is_disabled else "team_slot_select_disabled",
            placeholder="Select a team/slot to act from..." if not is_disabled else "No valid teams found for selection.",
            options=options, # Now guaranteed to have at least one option
            min_values=1,
            max_values=1,
            disabled=is_disabled
        ))


    async def on_timeout(self):
        if self.message:
            for item in self.children:
                item.disabled = True
            await self.message.edit(content="Team selection timed out.", view=self)
        self.stop()

    @ui.button(label="Cancel", style=discord.ButtonStyle.red, row=1)
    async def cancel_button(self, interaction: discord.Interaction, button: ui.Button):
        if interaction.user != self.original_interaction.user:
            await interaction.response.send_message("This action is not for you.", ephemeral=True)
            return
        await interaction.response.edit_message(content="Team selection cancelled.", view=None)
        self.stop()

    @ui.select(custom_id="team_slot_select")
    async def select_callback(self, interaction: discord.Interaction, select: ui.Select):
        if interaction.user != self.original_interaction.user:
            await interaction.response.send_message("This action is not for you.", ephemeral=True)
            return

        selected_value = select.values[0]
        selected_role_id_str, selected_slot_id = selected_value.split('_')
        selected_role_id = int(selected_role_id_str)

        # Find the full team info from the initial list
        self.selected_team_info = next(
            (team for team in self.teams if team['role_id'] == selected_role_id and team['slot_id'] == selected_slot_id),
            None
        )

        if not self.selected_team_info:
            await interaction.response.send_message("An error occurred. Please try the command again.", ephemeral=True)
            self.stop()
            return

        await interaction.response.edit_message(content=f"Selected {self.selected_team_info['team_name']} in slot {self.selected_team_info['slot_name']}. Processing...", view=None)
        self.stop() # Stop the view to prevent further interactions

class PlayerTeamSelectionView(ui.View):
    def __init__(self, player: discord.User, teams: List[Dict[str, Any]], original_interaction: discord.Interaction):
        super().__init__(timeout=180)
        self.player = player
        self.teams = teams
        self.original_interaction = original_interaction
        self.selected_team_info: Optional[Dict[str, Any]] = None
        self.message = None

        options = []
        for team in teams:
            try:
                team_name = team.get('team_name')
                slot_name = team.get('slot_name')
                role_id = team.get('role_id')
                slot_id = team.get('slot_id')

                # Ensure all required fields are present, are of expected types, and are non-empty/non-whitespace strings where appropriate
                if (isinstance(team_name, str) and team_name.strip() and
                    isinstance(slot_name, str) and slot_name.strip() and
                    role_id is not None and isinstance(role_id, int) and
                    isinstance(slot_id, str) and slot_id.strip()):
                    
                    value_str = f"{role_id}_{slot_id}"
                    label_str = f"{team_name} ({slot_name})"

                    description_str = f"Slot ID: {slot_id}"
                    if len(slot_id) > 8:
                        description_str = f"Slot ID: {slot_id[:8]}..."

                    # Ensure label and value are not empty after formatting
                    if not label_str.strip():
                        print(f"Warning: Generated label is empty for team: {team}. Skipping option.")
                        continue
                    if not value_str.strip():
                        print(f"Warning: Generated value is empty for team: {team}. Skipping option.")
                        continue

                    options.append(discord.SelectOption(
                        label=label_str,
                        value=value_str,
                        description=description_str
                    ))
                else:
                    print(f"Skipping invalid team data for select option: Missing, empty, or incorrect type values in {team}")
            except Exception as e:
                print(f"Skipping invalid team data for select option due to unexpected error: {e} in {team}")

        if not options:
            options = [discord.SelectOption(label="No teams available", value="no_teams_fallback")]
            is_disabled = True
        else:
            is_disabled = False

        self.add_item(ui.Select(
            custom_id="player_team_select" if not is_disabled else "player_team_select_disabled",
            placeholder=f"Select which team to release {player.display_name} from..." if not is_disabled else "No valid teams found for selection.",
            options=options, # Now guaranteed to have at least one option
            min_values=1,
            max_values=1,
            disabled=is_disabled
        ))


    async def on_timeout(self):
        if self.message:
            for item in self.children:
                item.disabled = True
            await self.message.edit(content="Team selection timed out for player release.", view=self)
        self.stop()

    @ui.button(label="Cancel", style=discord.ButtonStyle.red, row=1)
    async def cancel_button(self, interaction: discord.Interaction, button: ui.Button):
        if interaction.user != self.original_interaction.user:
            await interaction.response.send_message("This action is not for you.", ephemeral=True)
            return
        await interaction.response.edit_message(content="Player release cancelled.", view=None)
        self.stop()

    @ui.select(custom_id="player_team_select")
    async def select_callback(self, interaction: discord.Interaction, select: ui.Select):
        if interaction.user.id != self.original_interaction.user.id: # Changed to .id for comparison
            await interaction.response.send_message("This action is not for you.", ephemeral=True)
            return

        selected_value = select.values[0]
        selected_role_id_str, selected_slot_id = selected_value.split('_')
        selected_role_id = int(selected_role_id_str)

        self.selected_team_info = next(
            (team for team in self.teams if team['role_id'] == selected_role_id and team['slot_id'] == selected_slot_id),
            None
        )

        if not self.selected_team_info:
            await interaction.response.send_message("An error occurred. Please try the command again.", ephemeral=True)
            self.stop()
            return

        await interaction.response.edit_message(content=f"Selected to release {self.player.display_name} from {self.selected_team_info['team_name']} in slot {self.selected_team_info['slot_name']}. Processing...", view=None)
        self.stop()


# --- ForcedSignReversalView Class (used by Sign command) ---

class ForcedSignReversalView(ui.View):
    def __init__(self, *, signed_player_id: int, original_signer: discord.Member,
                             team_role_id: int, team_name: str, team_emoji: str,
                             free_agent_role_id: int, guild_id: int,
                             roster_cap_limit: int, roster_count_before_sign: int,
                             slot_id: str, slot_name: str, bot_instance):
        super().__init__(timeout=3600.0) # 1 hour timeout
        self.signed_player_id = signed_player_id
        self.original_signer = original_signer
        self.team_role_id = team_role_id
        self.team_name = team_name
        self.team_emoji = team_emoji
        self.free_agent_role_id = free_agent_role_id
        self.guild_id = guild_id
        self.roster_cap_limit = roster_cap_limit
        self.roster_count_before_sign = roster_count_before_sign # Roster count *before* this player was signed
        self.slot_id = slot_id
        self.slot_name = slot_name
        self.bot_instance = bot_instance
        self.message = None # Will store the message this view is attached to

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True
        if self.message:
            try:
                await self.message.edit(content="*This 'force sign' reversal option has expired.*", view=self)
            except discord.NotFound:
                pass
            except discord.HTTPException as e:
                print(f"Error editing message on timeout: {e}")
        self.stop()

    @ui.button(label="I was force signed (Reverse)", style=discord.ButtonStyle.danger, custom_id="force_sign_reversal")
    async def reverse_signing_button(self, interaction: discord.Interaction, button: ui.Button):
        if interaction.user.id != self.signed_player_id:
            await interaction.response.send_message("This button is not for you.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        guild = interaction.guild or self.bot_instance.get_guild(self.guild_id)
        if not guild:
            await interaction.followup.send("Error: Could not find server information.", ephemeral=True)
            return

        player_member = guild.get_member(self.signed_player_id)
        if not player_member:
            try:
                player_member = await guild.fetch_member(self.signed_player_id)
            except discord.NotFound:
                await interaction.followup.send("Error: Could not find your profile in the server.", ephemeral=True)
                return
            
        team_role = guild.get_role(self.team_role_id)
        free_agent_role = guild.get_role(self.free_agent_role_id)

        if not team_role or not free_agent_role:
            await interaction.followup.send("Error: Critical roles (team or free agent) not found. Cannot reverse.", ephemeral=True)
            return

        try:
            if team_role in player_member.roles:
                await player_member.remove_roles(team_role, reason="Force sign reversal by player")
            await player_member.add_roles(free_agent_role, reason="Force sign reversal by player")

            for item in self.children:
                item.disabled = True
            if self.message:
                await self.message.edit(content=f"Signing to {self.team_name} has been reversed. You are now a Free Agent.", view=self)
            
            # Get transaction channel from slot_configs
            slot_config = get_slot_config(self.slot_id) # Removed await as get_slot_config is now synchronous
            transaction_channel_id = slot_config.get('transaction_channel')

            reversal_description = (
                f"**🚨 Forced Sign Reversed! 🚨**\n\n"
                f"{player_member.mention} `{player_member.display_name}` has **REVERSED** their forced signing to {self.team_emoji} {team_role.mention} in slot '{self.slot_name}'.\n\n"
                f"This action was taken to counter a forced sign initiated by: {self.original_signer.mention} `{self.original_signer.display_name}`.\n\n"
                f"{player_member.display_name} is now a Free Agent.\n\n"
                f"**📂Team Roster ({self.team_name}):** {self.roster_count_before_sign}/{self.roster_cap_limit}"
            )
            reversal_embed = discord.Embed(
                title=f"{self.team_name} Transaction Reversal",
                description=reversal_description,
                color=discord.Color.orange(), # Orange color for reversals
                timestamp=DateTime.now(TimeZone.utc) # Use aliased DateTime and TimeZone
            )
            emoji_url = get_emoji_url(self.team_emoji)
            if emoji_url:
                reversal_embed.set_thumbnail(url=emoji_url)
            # Set author to server logo
            if guild.icon:
                reversal_embed.set_author(name=guild.name, icon_url=guild.icon.url)
            reversal_embed.set_footer(text=f"Reversed by {player_member.display_name}", icon_url=player_member.display_avatar.url)

            if transaction_channel_id:
                log_channel = guild.get_channel(transaction_channel_id)
                if log_channel and isinstance(log_channel, discord.TextChannel):
                    try:
                        await log_channel.send(embed=reversal_embed)
                    except discord.Forbidden:
                        print(f"No permission to send reversal log to channel {log_channel.id}")
            
            try:
                await self.original_signer.send(f"The signing of {player_member.display_name} to {self.team_name} in slot '{self.slot_name}' was reversed by the player due to a 'force sign' claim.")
            except discord.Forbidden:
                print(f"Could not DM original signer {self.original_signer.display_name} about reversal.")

            await interaction.followup.send("The signing has been reversed. You are now a Free Agent.", ephemeral=True)
            self.stop()

        except discord.Forbidden:
            await interaction.followup.send("I lack permissions to change your roles. Please contact an admin.", ephemeral=True)
        except discord.HTTPException as e:
            await interaction.followup.send(f"An error occurred: {e}", ephemeral=True)
        except Exception as e:
            print(f"Error in force_sign_reversal: {e}")
            traceback.print_exc()
            await interaction.followup.send("An unexpected error occurred while reversing the signing.", ephemeral=True)


# --- Helper to resolve team selection for commands ---
async def _resolve_team_selection(
    interaction: discord.Interaction,
    team_role_arg: Optional[discord.Role],
    command_name: str
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """
    Resolves the team and slot information based on optional team_role_arg or user's roles.
    Returns (selected_team_info, error_message).
    """
    user_team_roles_ids = [role.id for role in interaction.user.roles]
    user_teams_info = await get_team_info_for_user(interaction.guild_id, user_team_roles_ids)

    selected_team_info: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

    if team_role_arg:
        # User provided a specific team role
        matching_team = next((team for team in user_teams_info if team['role_id'] == team_role_arg.id), None)
        if matching_team:
            selected_team_info = matching_team
        else:
            error_message = f"You do not manage the team associated with role {team_role_arg.mention} or it's not a configured team. Please mention a team role you manage."
    else:
        # No team role provided, use existing selection logic
        if not user_teams_info:
            error_message = f"You don't have any team roles configured in any slot to {command_name} players for."
        elif len(user_teams_info) == 1:
            selected_team_info = user_teams_info[0]
        else:
            # User manages multiple teams. As requested, do not send a selection view.
            # Instead, instruct them to use the team_role argument.
            error_message = (
                f"You manage multiple teams. Please specify which team's role you want to use with the `team_role` argument "
                f"(e.g., `/{command_name} player: @Player team_role: @YourTeamRole`)."
            )
            # The calling command (sign, release, offer) will check for this error_message and send it.

    return selected_team_info, error_message


# --- The /sign command ---

@bot.tree.command(name="sign", description="Sign a player to your team")
@app_commands.describe(
    player="The player to sign",
    team_role="The specific team role you want to use (mention it). Optional if you manage only one team."
)
async def sign(interaction: discord.Interaction, player: discord.User, team_role: Optional[discord.Role] = None):
    await interaction.response.defer(ephemeral=True)
    
    selected_team_info, error_message = await _resolve_team_selection(interaction, team_role, "sign")

    if error_message:
        # If an error message was returned, send it and stop.
        await interaction.followup.send(error_message, ephemeral=True)
        return
    if not selected_team_info: # Should be covered by error_message, but defensive
        await interaction.followup.send("Failed to determine which team to use. Please try again.", ephemeral=True)
        return

    # Extract selected team and slot details
    team_role_id = selected_team_info['role_id']
    team_name = selected_team_info['team_name']
    team_emoji = selected_team_info['emoji']
    slot_id = selected_team_info['slot_id']
    slot_name = selected_team_info['slot_name']

    # Step 2: Get slot-specific configurations
    slot_config = get_slot_config(slot_id) # Removed await as get_slot_config is now synchronous
    guild_global_settings = await bot.get_guild_settings(interaction.guild_id) # Get global settings for suspended role

    sign_enabled = slot_config.get('sign_enabled', 'on')
    roster_cap_limit = slot_config.get('roster_cap', 25)
    free_agent_role_id = slot_config.get('free_agent_role')
    transaction_channel_id = slot_config.get('transaction_channel')
    suspended_role_id = guild_global_settings.get('suspended_role') # Suspended role is global

    if sign_enabled != 'on':
        await interaction.followup.send(f"The sign command is currently disabled for slot '{slot_name}'.", ephemeral=True)
        return

    team_role_obj = interaction.guild.get_role(team_role_id)
    if not team_role_obj:
        await interaction.followup.send(f"Error: The team role for '{team_name}' in slot '{slot_name}' was not found in the server.", ephemeral=True)
        return
    
    roster_count_before_this_sign = len([m for m in interaction.guild.members if team_role_obj in m.roles])
    if roster_count_before_this_sign >= roster_cap_limit:
        await interaction.followup.send(f"Your team '{team_name}' in slot '{slot_name}' has reached the roster cap of {roster_cap_limit} players.", ephemeral=True)
        return
            
    player_member_obj = interaction.guild.get_member(player.id)
    if not player_member_obj:
        try:
            player_member_obj = await interaction.guild.fetch_member(player.id)
        except discord.NotFound:
            await interaction.followup.send(f"Player {player.display_name} not found in this server.", ephemeral=True)
            return

    if player_member_obj == interaction.user:
        await interaction.followup.send("You cannot sign yourself.", ephemeral=True)
        return

    if not free_agent_role_id:
        await interaction.followup.send(f"Free Agent role is not configured for slot '{slot_name}'. Please ask an admin to configure it via `/setup {slot_name}`.", ephemeral=True)
        return
    
    free_agent_role_obj = interaction.guild.get_role(free_agent_role_id)
    if not free_agent_role_obj:
        await interaction.followup.send(f"Configured Free Agent role for slot '{slot_name}' not found in the server.", ephemeral=True)
        return

    if suspended_role_id:
        suspended_role_obj = interaction.guild.get_role(suspended_role_id)
        if suspended_role_obj and suspended_role_obj in player_member_obj.roles:
            await interaction.followup.send(f"{player.display_name} cannot be signed because they have the '{suspended_role_obj.name}' role.", ephemeral=True)
            return

    if free_agent_role_obj not in player_member_obj.roles:
        await interaction.followup.send(f"{player.display_name} does not have the Free Agent role for slot '{slot_name}'.", ephemeral=True)
        return

    if team_role_obj in player_member_obj.roles:
        await interaction.followup.send(f"{player.display_name} is already on {team_name} in slot '{slot_name}'.", ephemeral=True)
        return

    try:
        # Remove player from any other team roles in the same slot (if applicable, for a clean sign)
        # This is a common league rule: a player can only be on one team per slot.
        player_current_teams_in_slot = await get_teams_player_is_on(interaction.guild_id, player.id)
        for team_on in player_current_teams_in_slot:
            if team_on['slot_id'] == slot_id and team_on['role_id'] != team_role_id:
                old_team_role_obj = interaction.guild.get_role(team_on['role_id'])
                if old_team_role_obj and old_team_role_obj in player_member_obj.roles:
                    await player_member_obj.remove_roles(old_team_role_obj, reason=f"Signed to new team {team_name} in slot {slot_name}")
                    print(f"Removed {player.display_name} from {team_on['team_name']} in slot {slot_name} before signing to {team_name}.")


        await player_member_obj.remove_roles(free_agent_role_obj, reason=f"Signed by {interaction.user.display_name} to {team_name} in slot {slot_name}")
        await player_member_obj.add_roles(team_role_obj, reason=f"Signed by {interaction.user.display_name} to {team_name} in slot {slot_name}")
    except discord.Forbidden:
        await interaction.followup.send("I lack permissions to manage roles for this player or team. Ensure my role is above team/free agent roles.", ephemeral=True)
        return
    except discord.HTTPException as e:
        await interaction.followup.send(f"An error occurred while updating roles: {e}", ephemeral=True)
        return

    team_color = team_role_obj.color if team_role_obj.color != discord.Color.default() else discord.Color.blue()
    roster_after_sign = roster_count_before_this_sign + 1
    
    description = (
        f"***Player Signed***\n"
        f"The {team_emoji} {team_role_obj.mention}  have successfully **signed** {player.mention} `{player.display_name}`\n"
        f"**🤵 Signed By:** {interaction.user.mention} `{interaction.user.display_name}`\n\n"
        f"**📂Roster:** {roster_after_sign}/{roster_cap_limit}"
    )
    sign_embed = discord.Embed(
        title=f"{team_name} Transaction",
        description=description,
        color=team_color,
        timestamp=DateTime.now(TimeZone.utc)
    )
    emoji_url = get_emoji_url(team_emoji)
    if emoji_url:
        sign_embed.set_thumbnail(url=emoji_url)
    if interaction.guild.icon:
        sign_embed.set_author(name=interaction.guild.name, icon_url=interaction.guild.icon.url)
    sign_embed.set_footer(text=f"Action by {interaction.user.display_name}", icon_url=interaction.user.display_avatar.url)
    
    if transaction_channel_id:
        log_channel = interaction.guild.get_channel(transaction_channel_id)
        if log_channel and isinstance(log_channel, discord.TextChannel):
            try:
                await log_channel.send(embed=sign_embed)
            except discord.Forbidden:
                print(f"No permission to send to transaction channel {log_channel.id} for slot {slot_name}")
    
    await interaction.followup.send(f"{player.display_name} has been successfully signed to {team_name} in slot '{slot_name}'!", ephemeral=True)

    dm_embed = discord.Embed(
        title="📝 You've Been Signed!",
        description=f"You have been signed to {team_emoji} **{team_name}** in slot **{slot_name}** by {interaction.user.mention}.\n\n"
                    f"If you believe this was a mistake or a 'force sign', you can reverse this transaction within the next hour.",
        color=team_color,
        timestamp=DateTime.now(TimeZone.utc)
    )
    if interaction.guild.icon:
        dm_embed.set_author(name=interaction.guild.name, icon_url=interaction.guild.icon.url)
    dm_embed.set_footer(text=f"Guild: {interaction.guild.name} | Slot: {slot_name}")
    
    reversal_view = ForcedSignReversalView(
        signed_player_id=player.id,
        original_signer=interaction.user,
        team_role_id=team_role_id,
        team_name=team_name,
        team_emoji=team_emoji,
        free_agent_role_id=free_agent_role_id,
        guild_id=interaction.guild_id,
        roster_cap_limit=roster_cap_limit,
        roster_count_before_sign=roster_count_before_this_sign,
        slot_id=slot_id,
        slot_name=slot_name,
        bot_instance=bot
    )
    try:
        dm_message = await player_member_obj.send(embed=dm_embed, view=reversal_view)
        reversal_view.message = dm_message
    except discord.Forbidden:
        print(f"Could not DM {player.display_name} about signing. They may have DMs disabled.")
        await interaction.followup.send(f"Note: Could not DM {player.display_name} with the reversal option (DMs may be disabled).", ephemeral=True, delete_after=15)
    
    except Exception as error:
        print(f"Error in sign command: {error}")
        traceback.print_exc()
        await interaction.followup.send(f"An unexpected error occurred: {str(error)}", ephemeral=True)


# --- The /release command ---

@bot.tree.command(name="release", description="Release a player from your team")
@app_commands.describe(
    player="The player to release",
    team_role="The specific team role you want to release from (mention it). Optional if you manage only one team."
)
async def release(interaction: discord.Interaction, player: discord.User, team_role: Optional[discord.Role] = None):
    await interaction.response.defer(ephemeral=True)
    
    try: # Outer try block for the entire command logic
        # Step 1: Resolve which team/slot the coach is acting from
        selected_coach_team_info, error_message = await _resolve_team_selection(interaction, team_role, "release")

        if error_message:
            await interaction.followup.send(error_message, ephemeral=True)
            return
        if not selected_coach_team_info: # Should be covered by error_message, but defensive
            await interaction.followup.send("Failed to determine which team to use for release. Please try again.", ephemeral=True)
            return

        # Extract selected coach's team and slot details
        coach_team_role_id = selected_coach_team_info['role_id']
        coach_team_name = selected_coach_team_info['team_name']
        coach_team_emoji = selected_coach_team_info['emoji']
        coach_slot_id = selected_coach_team_info['slot_id']
        coach_slot_name = selected_coach_team_info['slot_name']

        # Step 2: Check which teams the player is on that the coach also manages (in the selected slot)
        player_member = interaction.guild.get_member(player.id)
        if not player_member:
            try:
                player_member = await interaction.guild.fetch_member(player.id)
            except discord.NotFound:
                await interaction.followup.send(f"Player {player.display_name} not found in this server.", ephemeral=True)
                return

        player_all_teams_info = await get_teams_player_is_on(interaction.guild_id, player.id)

        # Filter to teams that the coach manages AND are in the selected slot
        releasable_teams_for_player = []
        for p_team in player_all_teams_info:
            if p_team['slot_id'] == coach_slot_id and p_team['role_id'] == coach_team_role_id:
                releasable_teams_for_player.append(p_team)

        if not releasable_teams_for_player:
            await interaction.followup.send(f"{player.display_name} is not on your team '{coach_team_name}' in slot '{coach_slot_name}'.", ephemeral=True)
            return
        
        # If player is on multiple teams managed by the coach (should ideally be one per slot, but defensive check)
        final_release_team_info: Optional[Dict[str, Any]] = None
        if len(releasable_teams_for_player) > 1:
            release_selection_view = PlayerTeamSelectionView(player, releasable_teams_for_player, interaction)
            release_response_message = await interaction.followup.send(
                f"{player.display_name} is on multiple teams you manage in slot '{coach_slot_name}'. Please select which team to release them from:",
                view=release_selection_view, ephemeral=True
            )
            release_selection_view.message = release_response_message
            await release_selection_view.wait()

            if release_selection_view.selected_team_info:
                final_release_team_info = release_selection_view.selected_team_info
            else:
                # User cancelled or timed out
                return
        else:
            final_release_team_info = releasable_teams_for_player[0] # Only one team to release from

        # Extract final release team details
        release_team_role_id = final_release_team_info['role_id']
        release_team_name = final_release_team_info['team_name']
        release_team_emoji = final_release_team_info['emoji']
        release_slot_id = final_release_team_info['slot_id'] # Should be same as coach_slot_id
        release_slot_name = final_release_team_info['slot_name'] # Should be same as coach_slot_name


        # Step 3: Get slot-specific configurations for the selected release slot
        slot_config = get_slot_config(release_slot_id) # Removed await as get_slot_config is now synchronous
        guild_global_settings = await bot.get_guild_settings(interaction.guild_id)

        release_enabled = slot_config.get('release_enabled', 'on')
        roster_cap_limit = slot_config.get('roster_cap', 25)
        free_agent_role_id = slot_config.get('free_agent_role')
        transaction_channel_id = slot_config.get('transaction_channel')
        suspended_role_id = guild_global_settings.get('suspended_role')

        if release_enabled != 'on':
            return await interaction.followup.send(f"The release command is currently disabled for slot '{release_slot_name}'.", ephemeral=True)

        release_team_role_obj = interaction.guild.get_role(release_team_role_id)
        if not release_team_role_obj:
            return await interaction.followup.send(f"Error: The team role for '{release_team_name}' in slot '{release_slot_name}' was not found in the server.", ephemeral=True)
        
        if release_team_role_obj not in player_member.roles:
            return await interaction.followup.send(f"{player.display_name} is not on {release_team_name} in slot '{release_slot_name}'.", ephemeral=True)
                
        if not free_agent_role_id:
            return await interaction.followup.send(f"Free Agent role is not configured for slot '{release_slot_name}'. Please ask an admin to configure it via `/setup {release_slot_name}`.", ephemeral=True)

        free_agent_role_obj = interaction.guild.get_role(free_agent_role_id)
        if not free_agent_role_obj:
            return await interaction.followup.send(f"Configured Free Agent role for slot '{release_slot_name}' not found in the server.", ephemeral=True)
                
        # Remove team role and add free agent role
        try:
            await player_member.remove_roles(release_team_role_obj, reason=f"Released by {interaction.user.display_name} from {release_team_name} in slot {release_slot_name}")
            await player_member.add_roles(free_agent_role_obj, reason=f"Released by {interaction.user.display_name} from {release_team_name} in slot {release_slot_name}")
        except discord.Forbidden:
            return await interaction.followup.send("I don't have permission to manage roles. Ensure my role is above team/free agent roles.", ephemeral=True)
        except discord.HTTPException as e:
            return await interaction.followup.send(f"An error occurred while updating roles: {e}", ephemeral=True)

        # Create the release embed
        current_time = DateTime.now(TimeZone.utc)
        team_color = release_team_role_obj.color if release_team_role_obj.color != discord.Color.default() else discord.Color.blue()
        
        roster_count_after_release = len([m for m in interaction.guild.members if release_team_role_obj in m.roles])
        
        description = (
            f"***Player Released***\n"
            f"The {release_team_emoji} {release_team_role_obj.mention} have **released** {player.mention} `{player.display_name}`\n"
            f"**🤵Coach:** {interaction.user.mention} `{interaction.user.display_name}`\n\n"
            f"**📂Roster:** {roster_count_after_release}/{roster_cap_limit}"
        )
        
        release_embed = discord.Embed(
            title=f"{release_team_name} Transaction",
            description=description,
            color=team_color,
            timestamp=current_time
        )
        
        emoji_url = get_emoji_url(release_team_emoji)
        if emoji_url:
            release_embed.set_thumbnail(url=emoji_url)
        
        if interaction.guild.icon:
            release_embed.set_author(name=interaction.guild.name, icon_url=interaction.guild.icon.url)
        
        release_embed.set_footer(
            text=f"Action by {interaction.user.display_name}",
            icon_url=interaction.user.display_avatar.url
        )
        
        if transaction_channel_id:
            try:
                channel = interaction.guild.get_channel(transaction_channel_id)
                if channel and isinstance(channel, discord.TextChannel):
                    await channel.send(embed=release_embed)
            except Exception as e:
                print(f"Error sending to transaction channel: {e}")
                traceback.print_exc()
                
        await interaction.followup.send(f"Player {player.display_name} released successfully from {release_team_name} in slot '{release_slot_name}'!", ephemeral=True)
        
    except Exception as error: # This is the outer exception block
        print(f"Error in release command: {error}")
        traceback.print_exc()
        await interaction.followup.send(f"An unexpected error occurred: {str(error)}", ephemeral=True)


# --- OfferButtons Class (used by Offer command) ---

class OfferButtons(ui.View):
    def __init__(self, team_role_id: int, team_emoji: str, team_name: str, coach: discord.Member, player: discord.User, offer_expires_at: DateTime, guild_id: int, slot_id: str, slot_name: str, bot_instance): # Use aliased DateTime
        super().__init__(timeout=86400) # 24 hours timeout
        self.team_role_id = team_role_id
        self.team_emoji = team_emoji
        self.team_name = team_name
        self.coach = coach
        self.player = player
        self.expires_at = offer_expires_at
        self.guild_id = guild_id
        self.slot_id = slot_id
        self.slot_name = slot_name
        self.bot_instance = bot_instance
        self.message = None # To store the message this view is attached to

    def disable_buttons(self):
        for item in self.children:
            if isinstance(item, ui.Button):
                item.disabled = True

    @ui.button(label="Accept", style=discord.ButtonStyle.green)
    async def accept_button(self, interaction: discord.Interaction, button: ui.Button):
        if interaction.user.id != self.player.id:
            await interaction.response.send_message("This offer is not for you.", ephemeral=True)
            return

        if DateTime.now(TimeZone.utc) > self.expires_at: # Use aliased DateTime and TimeZone
            self.disable_buttons()
            await interaction.message.edit(view=self)
            await interaction.response.send_message("This offer has expired.", ephemeral=True)
            return
            
        # Defer the response as database operations and role changes can take time
        await interaction.response.defer(ephemeral=True)

        guild = interaction.guild or self.bot_instance.get_guild(self.guild_id)
        if not guild:
            try:
                guild = await self.bot_instance.fetch_guild(self.guild_id)
            except Exception as e:
                print(f"Error fetching guild {self.guild_id} in OfferButtons: {e}")
                await interaction.followup.send("Error: Could not access server information.", ephemeral=True)
                return

        player_member_obj = guild.get_member(self.player.id)
        if not player_member_obj:
            try:
                player_member_obj = await guild.fetch_member(self.player.id)
            except discord.NotFound:
                await interaction.followup.send("Error: Could not find you in the server.", ephemeral=True)
                return
            except discord.HTTPException:
                await interaction.followup.send("Error: Failed to fetch your server profile.", ephemeral=True)
                return
            
        # Fetch slot-specific configurations
        slot_config = get_slot_config(self.slot_id) # Removed await as get_slot_config is now synchronous
        guild_global_settings = await bot.get_guild_settings(self.guild_id)

        roster_cap_limit = slot_config.get('roster_cap', 25)
        free_agent_role_id = slot_config.get('free_agent_role')
        transaction_channel_id = slot_config.get('transaction_channel')
        suspended_role_id = guild_global_settings.get('suspended_role')

        team_role_obj = guild.get_role(self.team_role_id)
        if not team_role_obj:
            await interaction.followup.send(f"Error: The team role for this offer ({self.team_name} in slot {self.slot_name}) no longer exists.", ephemeral=True)
            return
            
        current_roster_count = len([m for m in guild.members if team_role_obj in m.roles])
        if current_roster_count >= roster_cap_limit:
            self.disable_buttons() # Disable buttons as offer can't be accepted
            await interaction.message.edit(view=self)
            await interaction.followup.send(f"The team '{self.team_name}' in slot '{self.slot_name}' has reached the roster cap of {roster_cap_limit}. This offer cannot be accepted.", ephemeral=True)
            return

        if not free_agent_role_id:
            await interaction.followup.send(f"Free Agent role is not configured for slot '{self.slot_name}'. Contact admin.", ephemeral=True)
            return
            
        free_agent_role_obj = guild.get_role(free_agent_role_id)
        if not free_agent_role_obj:
            await interaction.followup.send(f"Configured Free Agent role for slot '{self.slot_name}' not found. Contact admin.", ephemeral=True)
            return

        # --- Suspension Check on Accept ---
        if suspended_role_id:
            suspended_role_obj = guild.get_role(suspended_role_id)
            if suspended_role_obj and suspended_role_obj in player_member_obj.roles:
                await interaction.followup.send(f"You cannot accept this offer as you currently have the '{suspended_role_obj.name}' role.", ephemeral=True)
                return
        # --- End Suspension Check ---

        if free_agent_role_obj not in player_member_obj.roles:
            await interaction.followup.send(f"You no longer have the Free Agent role for slot '{self.slot_name}'. This offer cannot be accepted.", ephemeral=True)
            return
            
        try:
            # Remove player from any other team roles in the same slot (if applicable, for a clean sign)
            player_current_teams_in_slot = await get_teams_player_is_on(self.guild_id, self.player.id)
            for team_on in player_current_teams_in_slot:
                if team_on['slot_id'] == self.slot_id and team_on['role_id'] != self.team_role_id:
                    old_team_role_obj = guild.get_role(team_on['role_id'])
                    if old_team_role_obj and old_team_role_obj in player_member_obj.roles:
                        await player_member_obj.remove_roles(old_team_role_obj, reason=f"Accepted offer to new team {self.team_name} in slot {self.slot_name}")
                        print(f"Removed {self.player.display_name} from {team_on['team_name']} in slot {self.slot_name} before accepting offer to {self.team_name}.")

            await player_member_obj.remove_roles(free_agent_role_obj, reason=f"Accepted offer from {self.team_name} in slot {self.slot_name}")
            await player_member_obj.add_roles(team_role_obj, reason=f"Accepted offer from {self.team_name} in slot {self.slot_name}")
        except discord.Forbidden:
            await interaction.followup.send("I lack permissions to update your roles. Please contact an admin.", ephemeral=True)
            return
        except discord.HTTPException as e:
            await interaction.followup.send(f"An error occurred while updating roles: {e}", ephemeral=True)
            return

        self.disable_buttons()
        await interaction.message.edit(view=self) # Edit the DM to disable buttons

        # Send transaction log
        team_color = team_role_obj.color if team_role_obj.color != discord.Color.default() else discord.Color.blue()
        
        description = (
            f"***Offer Accepted***\n"
            f"{self.player.mention} `{self.player.display_name}` has **accepted** the offer from {self.team_emoji} {team_role_obj.mention}.\n"
            f"**🤵 Offer sent By:** {self.coach.mention} `{self.coach.display_name}`\n\n"
            f"**📂Roster:** {current_roster_count + 1}/{roster_cap_limit}"
        )
        offer_embed = discord.Embed(
            title=f"{self.team_name} Transaction (Offer Accepted)",
            description=description,
            color=team_color,
            timestamp=DateTime.now(TimeZone.utc)
        )
        emoji_url = get_emoji_url(self.team_emoji)
        if emoji_url:
            offer_embed.set_thumbnail(url=emoji_url)
        if guild.icon:
            offer_embed.set_author(name=guild.name, icon_url=guild.icon.url)
        offer_embed.set_footer(text=f"Player: {self.player.display_name}", icon_url=self.player.display_avatar.url)

        if transaction_channel_id:
            log_channel = guild.get_channel(transaction_channel_id)
            if log_channel and isinstance(log_channel, discord.TextChannel):
                try:
                    await log_channel.send(embed=offer_embed)
                except discord.Forbidden:
                    print(f"No permission to send to transaction channel {log_channel.id} for slot {self.slot_name}")
                except Exception as e:
                    print(f"Error sending to transaction channel: {e}")
        
        try:
            await self.coach.send(f"{self.player.display_name} has accepted your offer to join {self.team_name} in slot '{self.slot_name}'!")
        except discord.Forbidden:
            print(f"Could not DM coach {self.coach.id} about offer acceptance.")
            
        await interaction.followup.send(f"You have successfully accepted the offer and joined {self.team_name} in slot '{self.slot_name}'!", ephemeral=True)

    @ui.button(label="Deny", style=discord.ButtonStyle.red)
    async def deny_button(self, interaction: discord.Interaction, button: ui.Button):
        if interaction.user.id != self.player.id:
            await interaction.response.send_message("This offer is not for you.", ephemeral=True)
            return

        self.disable_buttons()
        await interaction.message.edit(view=self) # Edit the DM

        try:
            await self.coach.send(f"{self.player.display_name} has declined your offer to join {self.team_name} in slot '{self.slot_name}'.")
        except discord.Forbidden:
            print(f"Could not DM coach {self.coach.id} about offer denial.")
            
        await interaction.response.send_message("You have declined the offer.", ephemeral=True)

# --- The /offer command ---

@bot.tree.command(name="offer", description="Offer a contract to a player")
@app_commands.describe(
    player="The player to offer a contract to",
    team_role="The specific team role you want to offer from (mention it). Optional if you manage only one team."
)
async def offer(interaction: discord.Interaction, player: discord.User, team_role: Optional[discord.Role] = None):
    await interaction.response.defer(ephemeral=True)
    
    try: # Outer try block for the entire command logic
        # Step 1: Resolve which team/slot the coach is acting from
        selected_team_info, error_message = await _resolve_team_selection(interaction, team_role, "offer")

        if error_message:
            await interaction.followup.send(error_message, ephemeral=True)
            return
        if not selected_team_info: # Should be covered by error_message, but defensive
            await interaction.followup.send("Failed to determine which team to use for the offer. Please try again.", ephemeral=True)
            return

        # Extract selected team and slot details
        team_role_id = selected_team_info['role_id']
        team_name = selected_team_info['team_name']
        team_emoji = selected_team_info['emoji']
        slot_id = selected_team_info['slot_id']
        slot_name = selected_team_info['slot_name']

        # Step 2: Get slot-specific configurations
        slot_config = get_slot_config(slot_id) # Removed await as get_slot_config is now synchronous
        guild_global_settings = await bot.get_guild_settings(interaction.guild_id)

        offer_enabled = slot_config.get('offer_enabled', 'on')
        roster_cap_limit = slot_config.get('roster_cap', 25)
        free_agent_role_id = slot_config.get('free_agent_role')
        suspended_role_id = guild_global_settings.get('suspended_role') # Suspended role is global

        if offer_enabled != 'on':
            await interaction.followup.send(f"The offer command is currently disabled for slot '{slot_name}'.", ephemeral=True)
            return

        team_role_obj = interaction.guild.get_role(team_role_id)
        if not team_role_obj:
            await interaction.followup.send(f"Error: The team role for '{team_name}' in slot '{slot_name}' was not found in the server.", ephemeral=True)
            return
                
        current_roster_count = len([m for m in interaction.guild.members if team_role_obj in m.roles])
        if current_roster_count >= roster_cap_limit:
            await interaction.followup.send(f"Your team '{team_name}' in slot '{slot_name}' has reached the roster cap of {roster_cap_limit}. You cannot offer new contracts.", ephemeral=True)
            return
                
        player_member_obj = interaction.guild.get_member(player.id)
        if not player_member_obj:
            try:
                player_member_obj = await interaction.guild.fetch_member(player.id)
            except discord.NotFound:
                await interaction.followup.send(f"Player {player.display_name} not found in this server.", ephemeral=True)
                return
            except discord.HTTPException:
                await interaction.followup.send(f"Could not fetch player {player.display_name}.", ephemeral=True)
                return

        if player_member_obj == interaction.user:
            await interaction.followup.send("You cannot offer a contract to yourself.", ephemeral=True)
            return

        if not free_agent_role_id:
            await interaction.followup.send(f"Free Agent role is not configured for slot '{slot_name}'. Please ask an admin to configure it via `/setup {slot_name}`.", ephemeral=True)
            return
                
        free_agent_role_obj = interaction.guild.get_role(free_agent_role_id)
        if not free_agent_role_obj:
            await interaction.followup.send(f"Configured Free Agent role for slot '{slot_name}' not found in the server.", ephemeral=True)
            return

        if suspended_role_id:
            suspended_role_obj = interaction.guild.get_role(suspended_role_id)
            if suspended_role_obj and suspended_role_obj in player_member_obj.roles:
                await interaction.followup.send(f"{player.display_name} cannot be offered a contract because they currently have the '{suspended_role_obj.name}' role.", ephemeral=True)
                return

        if free_agent_role_obj not in player_member_obj.roles:
            await interaction.followup.send(f"{player.display_name} does not have the Free Agent role for slot '{slot_name}'.", ephemeral=True)
            return
                
        if team_role_obj in player_member_obj.roles:
            await interaction.followup.send(f"{player.display_name} is already on {team_name} in slot '{slot_name}'.", ephemeral=True)
            return


        offer_expires_at = DateTime.now(TimeZone.utc) + TimeDelta(hours=24)
        expires_in_timestamp = f"<t:{int(offer_expires_at.timestamp())}:R>"
        
        offer_embed = discord.Embed(
            title="📝 Contract Offer Received",
            description=f"You have received a contract offer from {team_emoji} **{team_name}** in slot **{slot_name}**!\n\n"
                        f"**Offered By (Coach):** {interaction.user.mention} `{interaction.user.display_name}`\n"
                        f"**Expires:** {expires_in_timestamp}\n\n"
                        f"The team currently has **{current_roster_count}/{roster_cap_limit}** players.",
            color=team_role_obj.color if team_role_obj.color != discord.Color.default() else discord.Color.blue()
        )
        emoji_url = get_emoji_url(team_emoji)
        if emoji_url:
            offer_embed.set_thumbnail(url=emoji_url)
        if interaction.guild.icon:
            offer_embed.set_author(name=interaction.guild.name, icon_url=interaction.guild.icon.url)
        offer_embed.set_footer(text=f"Guild: {interaction.guild.name} | Slot: {slot_name}")

        view = OfferButtons(
            team_role_id=team_role_id,
            team_emoji=team_emoji,
            team_name=team_name,
            coach=interaction.user,
            player=player,
            offer_expires_at=offer_expires_at,
            guild_id=interaction.guild_id,
            slot_id=slot_id,
            slot_name=slot_name,
            bot_instance=bot
        )

        try:
            await player_member_obj.send(embed=offer_embed, view=view)
            await interaction.followup.send(f"Offer successfully sent to {player.display_name} via DM for slot '{slot_name}'.", ephemeral=True)
        except discord.Forbidden:
            await interaction.followup.send(f"Could not send DM to {player.display_name}. They may have DMs disabled from server members or the bot.", ephemeral=True)
        except Exception as e:
            await interaction.followup.send(f"An error occurred trying to DM the player: {e}", ephemeral=True)

    except Exception as error:
        print(f"Error in offer command: {error}")
        traceback.print_exc()
        await interaction.followup.send(f"An unexpected error occurred: {str(error)}", ephemeral=True)






import discord
from discord import app_commands
from datetime import datetime
import sqlite3
import traceback

# TeamListPaginator class for handling pagination
class TeamListPaginator(discord.ui.View):
    def __init__(self, embeds):
        super().__init__(timeout=300)  # 5-minute timeout
        self.embeds = embeds
        self.current_page = 0
        self.total_pages = len(embeds)

    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.gray)
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.gray)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < len(self.embeds) - 1:
            self.current_page += 1
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer()

@bot.tree.command(name="list_teams", description="List all teams registered in a slot or across all slots")
@app_commands.describe(
    slot_id="The slot ID to list teams for (leave blank to list all slots)",
    public="Make the list visible to everyone (default: only you can see it)",
    sort_by="How to sort the teams (default: alphabetically)",
    show_roles="Show team management information"
)
@app_commands.choices(sort_by=[
    app_commands.Choice(name="Alphabetical", value="alpha"),
    app_commands.Choice(name="Member Count (High to Low)", value="members_desc"),
    app_commands.Choice(name="Member Count (Low to High)", value="members_asc")
])
@app_commands.choices(show_roles=[
    app_commands.Choice(name="Show Franchise Owners", value="owners"),
    app_commands.Choice(name="Show General Managers", value="gms"),
    app_commands.Choice(name="None", value="none")
])
async def list_teams(
    interaction: discord.Interaction,
    slot_id: str = None,
    public: bool = False,
    sort_by: app_commands.Choice[str] = None,
    show_roles: app_commands.Choice[str] = None
):
    await interaction.response.defer(ephemeral=not public)

    # Make sure all members are chunked for accurate role membership info
    if not interaction.guild.chunked:
        await interaction.guild.chunk(cache=True)

    try:
        # Connect to transaction_bot.db only
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()

        # --- Find valid slot(s) ---
        slots = {}
        cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ?", (interaction.guild.id,))
        for sid, sname in cursor.fetchall():
            slots[sid] = sname

        target_slots = []
        if slot_id:
            # Try to find slot by exact slot_id or slot_name (case-insensitive)
            matched_slot_id = None
            for sid, sname in slots.items():
                if sid == slot_id or (sname and str(sname).lower() == str(slot_id).lower()):
                    matched_slot_id = sid
                    break
            if not matched_slot_id:
                await interaction.followup.send(
                    f"No slot found with ID or name `{slot_id}` in this server. Use `/list_slots` to see available slots.",
                    ephemeral=True
                )
                return
            target_slots = [matched_slot_id]
        else:
            target_slots = list(slots.keys())

        if not target_slots:
            await interaction.followup.send(
                'No slots found in this server. Use `/create_slot` to add a slot first.',
                ephemeral=True
            )
            return

        # --- Fetch management role IDs (from first slot, fallback to None) ---
        fo_role_id = None
        gm_role_id = None
        for test_slot_id in target_slots:
            cursor.execute("""
                SELECT franchise_owner_role, general_manager_role FROM slot_configs WHERE slot_id = ?
            """, (test_slot_id,))
            roles_row = cursor.fetchone()
            if roles_row:
                if not fo_role_id and roles_row[0]: fo_role_id = roles_row[0]
                if not gm_role_id and roles_row[1]: gm_role_id = roles_row[1]
            if fo_role_id and gm_role_id:
                break

        fo_role = interaction.guild.get_role(int(fo_role_id)) if fo_role_id else None
        gm_role = interaction.guild.get_role(int(gm_role_id)) if gm_role_id else None

        # --- Try both tables: teams and league_teams, for compatibility ---
        # Collect teams from both tables, deduplicating by (slot_id, role_id)
        collected_teams = {}
        for table in ['teams', 'league_teams']:
            for slot in target_slots:
                try:
                    if table == 'teams':
                        cursor.execute(
                            "SELECT slot_id, team_name, team_role, team_emoji FROM teams WHERE slot_id = ?",
                            (slot,))
                        for row in cursor.fetchall():
                            sid, name, role_id, emoji = row
                            key = (sid, str(role_id))
                            if key not in collected_teams:
                                collected_teams[key] = {
                                    'slot_id': sid,
                                    'name': name,
                                    'role_id': role_id,
                                    'emoji': emoji if emoji else "🏆",
                                    'source': 'teams'
                                }
                    else:
                        cursor.execute(
                            "SELECT slot_id, team_name, role_id, emoji FROM league_teams WHERE slot_id = ?",
                            (slot,))
                        for row in cursor.fetchall():
                            sid, name, role_id, emoji = row
                            key = (sid, str(role_id))
                            if key not in collected_teams:
                                collected_teams[key] = {
                                    'slot_id': sid,
                                    'name': name,
                                    'role_id': role_id,
                                    'emoji': emoji if emoji else "🏆",
                                    'source': 'league_teams'
                                }
                except sqlite3.OperationalError:
                    continue

        if not collected_teams:
            await interaction.followup.send(
                'No teams found in the selected slot(s).',
                ephemeral=True
            )
            return

        # --- Build slot name mapping for display ---
        slot_names = slots

        # --- Build team data: filter only valid roles in Discord ---
        team_data = []
        for team in collected_teams.values():
            this_slot_id = team['slot_id']
            name = team['name']
            role_id = team['role_id']
            emoji = team['emoji']
            team_role = interaction.guild.get_role(int(role_id)) if role_id else None
            if team_role:
                team_info = {
                    'role': team_role,
                    'name': name,
                    'emoji': emoji,
                    'member_count': len(team_role.members),
                    'slot_name': slot_names.get(this_slot_id, this_slot_id[:8] + "..."),
                }

                # If showing owners or GMs, find them for this team
                if show_roles and show_roles.value != "none":
                    role_type = "Franchise Owner" if show_roles.value == "owners" else "General Manager"
                    owner = None
                    if show_roles.value == "owners" and fo_role:
                        for member in team_role.members:
                            if fo_role in member.roles:
                                owner = member
                                break
                    elif show_roles.value == "gms" and gm_role:
                        for member in team_role.members:
                            if gm_role in member.roles:
                                owner = member
                                break
                    team_info['owner'] = owner

                team_data.append(team_info)

        if not team_data:
            await interaction.followup.send(
                "No displayable teams found. This might be due to missing team roles on the Discord server.",
                ephemeral=True
            )
            return

        # --- Sorting ---
        if sort_by and sort_by.value in ["members_desc", "members_asc"]:
            team_data.sort(
                key=lambda x: x['member_count'],
                reverse=(sort_by.value == "members_desc")
            )
        elif sort_by and sort_by.value == "alpha":
            team_data.sort(key=lambda x: x['name'].lower())

        # --- Pagination ---
        teams_per_page = 10
        total_teams = len(team_data)
        total_pages = (total_teams + teams_per_page - 1) // teams_per_page

        embeds = []
        for page in range(total_pages):
            start_idx = page * teams_per_page
            end_idx = min(start_idx + teams_per_page, total_teams)
            page_teams = team_data[start_idx:end_idx]

            embed = discord.Embed(
                title=f"Teams List ({total_teams} total)",
                description=f"Page {page+1}/{total_pages}",
                color=discord.Color.blue(),
                timestamp=datetime.now()
            )

            for team in page_teams:
                member_display = f"{team['member_count']} members"
                value = f"{team['role'].mention} • {member_display} • Slot: `{team['slot_name']}`"
                if show_roles and show_roles.value != "none":
                    role_type = "Franchise Owner" if show_roles.value == "owners" else "General Manager"
                    owner_text = f"No {role_type}"
                    if team.get('owner'):
                        owner_text = team['owner'].mention
                    value += f"\n👤 {owner_text}"

                embed.add_field(
                    name=f"{team['emoji']} {team['name']}",
                    value=value,
                    inline=False
                )

            embed.set_footer(text=f"Sorted by: {sort_by.name if sort_by else 'Alphabetical'}")
            embeds.append(embed)

        if len(embeds) > 1:
            paginator = TeamListPaginator(embeds)
            await interaction.followup.send(embed=embeds[0], view=paginator, ephemeral=not public)
        else:
            await interaction.followup.send(embed=embeds[0], ephemeral=not public)

    except Exception as error:
        print(f"Error listing teams: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error listing the teams: {error}',
            ephemeral=True
        )


import discord
from discord import app_commands, ui
from typing import Optional
import sqlite3
import traceback
from datetime import datetime as DateTime, timezone as TimeZone



# --- DEMAND CONFIG HELPER ---

def ensure_demand_config_for_slot(slot_id: str):
    """Ensure there is a demand config row for this slot_id."""
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (slot_id,))
    conn.commit()
    conn.close()

def get_demand_config_for_slot(slot_id: str) -> Optional[dict]:
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    cursor.execute("""
        SELECT demands_enabled, demand_limit, demand_channel,
            five_demands_role, four_demands_role, three_demands_role,
            two_demands_role, one_demand_role, no_demands_role
        FROM slot_demand_config WHERE slot_id = ?
    """, (slot_id,))
    row = cursor.fetchone()
    conn.close()
    if row:
        keys = [
            "demands_enabled", "demand_limit", "demand_channel",
            "five_demands_role", "four_demands_role", "three_demands_role",
            "two_demands_role", "one_demand_role", "no_demands_role"
        ]
        return dict(zip(keys, row))
    return None

def get_slot_management_roles(slot_id: str):
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    cursor.execute("""
        SELECT franchise_owner_role, general_manager_role, head_coach_role, assistant_coach_role
        FROM slot_configs WHERE slot_id = ?
    """, (slot_id,))
    row = cursor.fetchone()
    conn.close()
    if row:
        return {
            "franchise_owner_role": row[0],
            "general_manager_role": row[1],
            "head_coach_role": row[2],
            "assistant_coach_role": row[3]
        }
    return {}

def get_free_agent_role_id(slot_id: str):
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    cursor.execute("SELECT free_agent_role FROM slot_configs WHERE slot_id = ?", (slot_id,))
    row = cursor.fetchone()
    conn.close()
    return row[0] if row else None

def get_team_and_slot_roles(guild: discord.Guild, user: discord.Member):
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    role_ids = [str(role.id) for role in user.roles]
    if not role_ids:
        conn.close()
        return []
    placeholders = ",".join(["?"] * len(role_ids))
    cursor.execute(
        f"SELECT role_id, slot_id, team_name, emoji FROM league_teams WHERE role_id IN ({placeholders}) AND slot_id IS NOT NULL",
        tuple(role_ids)
    )
    results = cursor.fetchall()
    conn.close()
    valid_teams = []
    seen = set()
    for rid, slot_id, team_name, emoji in results:
        team_role = guild.get_role(int(rid))
        if team_role and team_role in user.roles and rid not in seen:
            valid_teams.append({
                "role": team_role,
                "slot_id": slot_id,
                "team_name": team_name,
                "emoji": emoji
            })
            seen.add(rid)
    return valid_teams

def get_emoji_url(emoji_str: Optional[str]) -> Optional[str]:
    import re
    if not emoji_str:
        return None
    custom_emoji_match = re.match(r'<a?:[\w]+:(\d+)>', emoji_str)
    if custom_emoji_match:
        emoji_id = custom_emoji_match.group(1)
        return f"https://cdn.discordapp.com/emojis/{emoji_id}.png"
    return None

# --- DEMAND TEAM SELECT VIEW ---

class DemandTeamSelectView(ui.View):
    def __init__(self, teams):
        super().__init__(timeout=60)
        self.teams = teams
        self.selected = None
        seen = set()
        options = []
        for team in teams:
            role_id = str(team['role'].id)
            if role_id in seen:
                continue
            seen.add(role_id)
            label = f"{team['team_name']}"
            description = f"Slot: {team['slot_id'][:8]}..."
            options.append(discord.SelectOption(label=label, value=role_id, description=description))
            if len(options) >= 25:
                break
        if options:
            select = ui.Select(
                placeholder="Select which team to demand from",
                options=options,
                min_values=1,
                max_values=1
            )
            async def select_callback(interaction: discord.Interaction):
                selected_id = int(interaction.data["values"][0])
                for team in self.teams:
                    if team['role'].id == selected_id:
                        self.selected = team
                        break
                await interaction.response.edit_message(content=f"Selected team: {self.selected['team_name']}", view=None)
                self.stop()
            select.callback = select_callback
            self.add_item(select)

# --- /check_demands COMMAND ---

@bot.tree.command(name="check_demands", description="Check current demand system configuration for a slot")
@app_commands.describe(
    slot_id_or_name="The slot's ID or name to check. Leave blank for the default slot."
)
@app_commands.default_permissions(administrator=True)
async def check_demands(interaction: discord.Interaction, slot_id_or_name: Optional[str] = None):
    await interaction.response.defer(ephemeral=True)
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    guild_id = interaction.guild_id
    slot_id = None
    slot_name = None

    # Find slot by ID or name, or use default
    if slot_id_or_name:
        cursor.execute(
            "SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)",
            (guild_id, slot_id_or_name, slot_id_or_name)
        )
        row = cursor.fetchone()
        if not row:
            await interaction.followup.send(
                f"No slot found with ID or name `{slot_id_or_name}` in this server.",
                ephemeral=True
            )
            conn.close()
            return
        slot_id, slot_name = row
    else:
        cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? ORDER BY slot_name LIMIT 1", (guild_id,))
        row = cursor.fetchone()
        if not row:
            await interaction.followup.send(
                "No slots found for this server. Please create a slot first.",
                ephemeral=True
            )
            conn.close()
            return
        slot_id, slot_name = row

    ensure_demand_config_for_slot(slot_id)
    config = get_demand_config_for_slot(slot_id)
    mgmt_roles = get_slot_management_roles(slot_id)
    if config:
        embed = discord.Embed(
            title=f"Demand System Configuration - Slot: {slot_name}",
            description="Current settings for this slot:",
            color=discord.Color.blue()
        )
        status = "🟢 Enabled" if config["demands_enabled"] == "on" else "🔴 Disabled"
        limits = "✅ Enabled" if config["demand_limit"] == "true" else "❌ Disabled"
        embed.add_field(name="System Status", value=status, inline=True)
        embed.add_field(name="Demand Limits", value=limits, inline=True)
        embed.add_field(name="Demand Channel", value=f"<#{config['demand_channel']}>" if config["demand_channel"] else "Not set", inline=True)

        for name, key in [
            ("Five Demands Role", "five_demands_role"),
            ("Four Demands Role", "four_demands_role"),
            ("Three Demands Role", "three_demands_role"),
            ("Two Demands Role", "two_demands_role"),
            ("One Demand Role", "one_demand_role"),
            ("No Demands Role", "no_demands_role")
        ]:
            value = f"<@&{config[key]}>" if config.get(key) else "Not set"
            embed.add_field(name=name, value=value, inline=True)

        for name, key in [
            ("Franchise Owner (MGMT)", "franchise_owner_role"),
            ("General Manager (MGMT)", "general_manager_role"),
            ("Head Coach (MGMT)", "head_coach_role"),
            ("Assistant Coach (MGMT)", "assistant_coach_role")
        ]:
            value = f"<@&{mgmt_roles[key]}>" if mgmt_roles.get(key) else "Not set"
            embed.add_field(name=name, value=value, inline=True)
    else:
        embed = discord.Embed(
            title=f"Demand System Configuration - Slot: {slot_name}",
            description="❌ No configuration found for this slot. Use `/setup` to configure it.",
            color=discord.Color.red()
        )
    # Debug print for /check_demands
    print(f"[DEBUG] check_demands: slot_id={slot_id}, config={config}")
    await interaction.followup.send(embed=embed, ephemeral=True)
    conn.close()

# --- /demand COMMAND ---

@bot.tree.command(name="demand", description="Demand a release from your current team in a slot")
@app_commands.describe(
    team_role="The specific team role to demand off (required if you are on more than one team)"
)
async def demand(interaction: discord.Interaction, team_role: Optional[discord.Role] = None):
    await interaction.response.defer(ephemeral=True)
    user = interaction.user if isinstance(interaction.user, discord.Member) else interaction.guild.get_member(interaction.user.id)
    guild = interaction.guild

    # Find all teams the user is on in league_teams
    user_teams = get_team_and_slot_roles(guild, user)
    if not user_teams:
        await interaction.followup.send("You must be on a team to demand a release.", ephemeral=True)
        return

    # If more than one, and no team_role given, ask user to select
    if len(user_teams) > 1 and not team_role:
        view = DemandTeamSelectView(user_teams)
        msg = await interaction.followup.send(
            "You are on more than one team. Please select which team to demand release from:",
            view=view,
            ephemeral=True
        )
        await view.wait()
        if not view.selected:
            await msg.edit(content="Demand cancelled (no team selected).", view=None)
            return
        selected_team = view.selected
    elif team_role:
        selected_team = next((t for t in user_teams if t["role"].id == team_role.id), None)
        if not selected_team:
            await interaction.followup.send("You are not a member of the specified team.", ephemeral=True)
            return
    else:
        selected_team = user_teams[0]

    slot_id = selected_team["slot_id"]

    # --- Patch: Canonical slot_id fix
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    # Get slot_name for the team slot_id
    cursor.execute("SELECT slot_name FROM slots WHERE slot_id = ?", (slot_id,))
    slot_row = cursor.fetchone()
    if slot_row:
        slot_name = slot_row[0]
        # Get canonical slot_id for this slot_name and guild
        cursor.execute("SELECT slot_id FROM slots WHERE slot_name = ? AND guild_id = ?", (slot_name, guild.id))
        canonical_slot_id_row = cursor.fetchone()
        if canonical_slot_id_row:
            slot_id = canonical_slot_id_row[0]
    conn.close()
    # End of Patch

    ensure_demand_config_for_slot(slot_id)
    config = get_demand_config_for_slot(slot_id)
    mgmt_roles = get_slot_management_roles(slot_id)

    # --- Debug output to help diagnose demand config issues
    print(f"[DEBUG] Demand for slot_id={slot_id} using demand_channel={config['demand_channel']} (should match /check_demands output)")
    if not config:
        await interaction.followup.send(
            f"Debug: No demand config found for slot_id `{slot_id}` even after attempting to create it.",
            ephemeral=True
        )
        return
    if config["demands_enabled"] != "on":
        await interaction.followup.send(
            f"Debug: demands_enabled for slot_id `{slot_id}` is `{config['demands_enabled']}` (should be 'on').\nIf you see this, run `/setup {slot_id}` and enable the demand system for this slot.",
            ephemeral=True
        )
        return

    demand_limit_enabled = config["demand_limit"] == "true"
    demand_roles = {
        5: config["five_demands_role"],
        4: config["four_demands_role"],
        3: config["three_demands_role"],
        2: config["two_demands_role"],
        1: config["one_demand_role"],
        0: config["no_demands_role"]
    }
    current_demand_role = None
    current_demands = None

    if demand_limit_enabled:
        demand_role_list = [(count, role_id) for count, role_id in demand_roles.items() if role_id is not None]
        demand_role_list.sort(reverse=True)
        for role in user.roles:
            role_id = str(role.id)
            for count, config_role_id in demand_role_list:
                if role_id == str(config_role_id):
                    current_demand_role = role
                    current_demands = count
                    break
            if current_demand_role:
                break

        if current_demand_role is None:
            await interaction.followup.send(
                "You don't have any demand roles assigned or they are not configured correctly.",
                ephemeral=True
            )
            return

        if current_demands == 0:
            await interaction.followup.send(
                "You have no demands remaining.",
                ephemeral=True
            )
            return

        try:
            await user.remove_roles(current_demand_role, reason=f"Used 1 demand (now {current_demands - 1} remaining)")
            next_demands = current_demands - 1
            if next_demands in demand_roles and demand_roles[next_demands]:
                next_role = guild.get_role(int(demand_roles[next_demands]))
                if next_role:
                    await user.add_roles(next_role, reason=f"Moved to {next_demands} demands role")
        except discord.Forbidden:
            await interaction.followup.send("I lack permissions to manage demand roles. Please contact an admin.", ephemeral=True)
            return
        except Exception as e:
            print(f"Error updating demand roles: {e}")
            traceback.print_exc()
            await interaction.followup.send("An error occurred while updating your demand roles.", ephemeral=True)
            return

    # Remove team role and add free agent role, and remove slot mgmt roles
    try:
        await user.remove_roles(selected_team["role"], reason="Player demanded release")
        coach_roles_to_remove = []
        for key in ["franchise_owner_role", "general_manager_role", "head_coach_role", "assistant_coach_role"]:
            role_id = mgmt_roles.get(key)
            if role_id:
                role_obj = guild.get_role(int(role_id))
                if role_obj and role_obj in user.roles:
                    coach_roles_to_remove.append(role_obj)
        if coach_roles_to_remove:
            await user.remove_roles(*coach_roles_to_remove, reason="Player demanded release (slot management roles)")
        free_agent_role_id = get_free_agent_role_id(slot_id)
        if free_agent_role_id:
            free_agent_role = guild.get_role(int(free_agent_role_id))
            if free_agent_role:
                await user.add_roles(free_agent_role, reason="Player moved to Free Agent after demand")
            else:
                await interaction.followup.send(
                    "Warning: Could not find the free agent role. Please contact an administrator.",
                    ephemeral=True
                )
        else:
            await interaction.followup.send(
                "Free agent role is not configured for this slot. Please configure the free agent role first.",
                ephemeral=True
            )
            return
    except discord.Forbidden:
        await interaction.followup.send(
            "Error: I lack permissions to manage roles (remove team or coach role or add free agent role). Please contact an administrator.",
            ephemeral=True
        )
        return
    except Exception as role_error:
        print(f"Error managing roles during demand: {role_error}")
        traceback.print_exc()
        await interaction.followup.send(
            "Error: An unexpected error occurred while managing your roles. Please contact an administrator.",
            ephemeral=True
        )
        return

    # Debug prints for /demand
    print(f"[DEBUG] demand: slot_id={slot_id}, config={config}")
    print(f"[DEBUG] demand: demand_channel_id={config['demand_channel']}")
    print(f"[DEBUG] demand: guild_id={guild.id} | guild.name={guild.name}")

    # Prepare embed for demand log
    embed_color = selected_team['role'].color if selected_team['role'].color != discord.Color.default() else discord.Color.blue()
    embed_description = (
        f"**demand**\n"
        f"{user.mention} `{user.display_name}` has **demanded** from "
        f"{selected_team['emoji'] or ''} {selected_team['role'].mention}."
    )
    embed = discord.Embed(
        title=f"{selected_team['team_name']} ",
        description=embed_description,
        color=embed_color,
        timestamp=DateTime.now(TimeZone.utc)
    )
    if selected_team['emoji']:
        emoji_url = get_emoji_url(selected_team['emoji'])
        if emoji_url:
            embed.set_thumbnail(url=emoji_url)
    if guild.icon:
        embed.set_author(name=guild.name, icon_url=guild.icon.url)
    embed.set_footer(text=f"Action by {user.display_name}", icon_url=user.display_avatar.url)

    demand_channel_id = config["demand_channel"]
    if demand_channel_id:
        channel = guild.get_channel(int(demand_channel_id))
        if channel and isinstance(channel, discord.TextChannel):
            try:
                await channel.send(embed=embed)
            except discord.Forbidden:
                print(f"Bot lacks permission to send demand log to channel {channel.id}")
            except Exception as e:
                print(f"Error sending demand log: {e}")
        else:
            print(f"Demand channel with ID {demand_channel_id} not found or not a text channel.")
    else:
        print("Demand channel not configured.")

    await interaction.followup.send(
        f"Successfully demanded release from {selected_team['team_name']}! You are now a Free Agent.",
        ephemeral=True
    )




from discord import app_commands
import discord

@bot.tree.command(name="developer_reset", description="IRREVERSIBLE: Wipe all bot DB data for ALL servers (Bot Owner only!)")
async def reset_all_guilds(interaction: discord.Interaction):
    # Restrict to bot owner only
    if interaction.user.id != 782671382685024279:
        await interaction.response.send_message("Only the bot owner can use this command.", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    confirm_view = discord.ui.View()
    confirm_btn = discord.ui.Button(
        label="YES, WIPE ALL SERVERS",
        style=discord.ButtonStyle.danger,
        custom_id="confirm_global_db_reset"
    )
    cancel_btn = discord.ui.Button(
        label="Cancel",
        style=discord.ButtonStyle.secondary,
        custom_id="cancel_global_db_reset"
    )

    async def confirm_callback(intx: discord.Interaction):
        if intx.user.id != 782671382685024279:
            await intx.response.send_message("Only the bot owner may confirm this.", ephemeral=True)
            return
        await intx.response.defer(ephemeral=True)
        try:
            import sqlite3
            conn = sqlite3.connect('transaction_bot.db')
            cursor = conn.cursor()
            # Drop all slot-based tables and configs
            cursor.execute("DELETE FROM slots;")
            cursor.execute("DELETE FROM guild_settings;")
            cursor.execute("DELETE FROM slot_configs;")
            cursor.execute("DELETE FROM slot_command_settings;")
            cursor.execute("DELETE FROM slot_demand_config;")
            cursor.execute("DELETE FROM league_teams;")
            cursor.execute("DELETE FROM teams;")
            cursor.execute("DELETE FROM transactions;")
            cursor.execute("DELETE FROM applications;")
            conn.commit()
            conn.close()
            await intx.followup.send("All server data in the bot database has been wiped from all guilds.\nYou must re-run `/global_setup` and re-create slots.", ephemeral=True)
            try:
                if intx.message:
                    await intx.message.edit(view=None)
                else:
                    await intx.edit_original_response(view=None)
            except discord.errors.NotFound:
                pass
        except Exception as e:
            await intx.followup.send(f"Error during global wipe: {e}", ephemeral=True)

    async def cancel_callback(intx: discord.Interaction):
        if intx.user.id != 782671382685024279:
            await intx.response.send_message("Only the bot owner may cancel this.", ephemeral=True)
            return
        try:
            if intx.message:
                await intx.message.edit(content="Global wipe cancelled.", view=None)
            else:
                await intx.edit_original_response(content="Global wipe cancelled.", view=None)
        except discord.errors.NotFound:
            pass

    confirm_btn.callback = confirm_callback
    cancel_btn.callback = cancel_callback
    confirm_view.add_item(confirm_btn)
    confirm_view.add_item(cancel_btn)

    await interaction.followup.send(
        "**⚠️ WARNING: GLOBAL DATABASE WIPE**\n"
        "This will irreversibly delete **ALL slots, teams, configs, applications, and transactions for ALL SERVERS** this bot is in.\n"
        "**Only use this if you want to start completely from scratch everywhere.**\n\n"
        "Are you absolutely sure?",
        view=confirm_view,
        ephemeral=True
    )



import sqlite3
import traceback
import discord
from discord import app_commands
from typing import Optional, List

def generate_name_variations(team_name: str) -> List[str]:
    """Generate different variations of the team name for emoji matching"""
    variations = set()
    variations.add(team_name)
    variations.add(team_name.replace(" ", ""))
    variations.add(team_name.replace(" ", "_"))
    variations.add(team_name.lower())
    variations.add(team_name.upper())
    return list(variations)

def find_matching_emoji(guild_emojis: List[discord.Emoji], team_variations: List[str]) -> Optional[str]:
    """Find the best matching emoji for the team"""
    for variation in team_variations:
        for emoji in guild_emojis:
            if variation.lower() in emoji.name.lower():
                return str(emoji)
    return None

def split_long_text(items, max_len=1024):
    """Split a list of strings so that each chunk does not exceed max_len."""
    chunks = []
    current = ""
    for item in items:
        line = item + "\n"
        if len(current) + len(line) > max_len:
            chunks.append(current)
            current = line
        else:
            current += line
    if current:
        chunks.append(current)
    return chunks

@bot.tree.command(name="detect_emojis", description="Detect and update team emojis for a specific slot (or main slot if none selected).")
@app_commands.describe(
    slot_id_or_name="The slot name or ID to update emojis for (defaults to 'main' slot if not specified)."
)
@app_commands.checks.has_permissions(administrator=True)
async def detect_emojis(
    interaction: discord.Interaction,
    slot_id_or_name: Optional[str] = None
):
    await interaction.response.defer(ephemeral=True)

    # --- Slot selection logic ---
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()

        # Find the slot_id and slot_name for given slot_id_or_name (default to 'main')
        guild_id = str(interaction.guild.id)
        if slot_id_or_name:
            cursor.execute(
                "SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND (slot_id = ? OR slot_name = ?)",
                (guild_id, slot_id_or_name, slot_id_or_name)
            )
            found = cursor.fetchone()
            if not found:
                await interaction.followup.send(
                    f"Could not find slot '{slot_id_or_name}'. Please use `/list_slots` to see available slots.", ephemeral=True
                )
                return
            slot_id, slot_name = found
        else:
            # Default to first slot named 'main', else just the first slot for the guild
            cursor.execute(
                "SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND slot_name = ?",
                (guild_id, "main")
            )
            found = cursor.fetchone()
            if not found:
                cursor.execute(
                    "SELECT slot_id, slot_name FROM slots WHERE guild_id = ? ORDER BY slot_name LIMIT 1",
                    (guild_id,)
                )
                found = cursor.fetchone()
                if not found:
                    await interaction.followup.send(
                        "No slots found for this server. Please create a slot first.",
                        ephemeral=True
                    )
                    return
            slot_id, slot_name = found

        # --- Fetch teams for this slot ---
        cursor.execute(
            "SELECT role_id, team_name, emoji FROM league_teams WHERE slot_id = ?",
            (slot_id,)
        )
        teams = cursor.fetchall()

        if not teams:
            await interaction.followup.send(
                f"No teams found in this slot ('{slot_name}'). Please set up teams first.",
                ephemeral=True
            )
            return

        updates = []
        guild_emojis = interaction.guild.emojis

        for team in teams:
            role_id, team_name, current_emoji = team

            # Validate team name and role
            if not team_name:
                continue
            team_role = interaction.guild.get_role(int(role_id))
            if not team_role:
                continue

            name_variations = generate_name_variations(team_name)
            found_emoji = find_matching_emoji(guild_emojis, name_variations)

            # Use default if none found
            if not found_emoji:
                found_emoji = "🏈"

            # Update if emoji is different
            if found_emoji != current_emoji:
                cursor.execute(
                    '''
                    UPDATE league_teams 
                    SET emoji = ? 
                    WHERE role_id = ? AND slot_id = ?
                    ''', (found_emoji, role_id, slot_id)
                )
                updates.append({
                    "team": team_name,
                    "old_emoji": current_emoji or "None",
                    "new_emoji": found_emoji
                })

        conn.commit()

        # --- Create results embed ---
        embed = discord.Embed(
            title=f"Team Emoji Detection Results - Slot: {slot_name}",
            color=discord.Color.blue()
        )

        if updates:
            update_lines = [
                f"• {u['team']}: {u['old_emoji']} → {u['new_emoji']}"
                for u in updates
            ]
            split_chunks = split_long_text(update_lines)
            for idx, chunk in enumerate(split_chunks):
                fname = f"Updated Teams ({len(updates)})" if idx == 0 else f"Updated Teams (cont.)"
                embed.add_field(name=fname, value=chunk, inline=False)
        else:
            embed.description = "No emoji updates were needed. All teams have appropriate emojis."

        await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as error:
        print(f"Error detecting emojis: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error while detecting emojis: {str(error)}',
            ephemeral=True
        )
    finally:
        if 'conn' in locals():
            conn.close()


from typing import Literal, Optional
import sqlite3
import discord
from discord import app_commands
import pytz
import asyncio
from datetime import datetime, timedelta

def get_management_permission(interaction: discord.Interaction):
    if interaction.user.guild_permissions.administrator:
        return True
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        cursor.execute("SELECT admin_role FROM guild_settings WHERE guild_id = ?", (interaction.guild.id,))
        row = cursor.fetchone()
        if row and row[0]:
            admin_role = interaction.guild.get_role(int(row[0]))
            if admin_role and admin_role in interaction.user.roles:
                return True
    except Exception:
        pass
    finally:
        if 'conn' in locals():
            conn.close()
    return False

async def get_user_teams(interaction: discord.Interaction):
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    teams = []
    for role in interaction.user.roles:
        cursor.execute("SELECT team_name, emoji, slot_id FROM league_teams WHERE role_id = ?", (str(role.id),))
        team_info = cursor.fetchone()
        if team_info:
            teams.append((role, team_info[0], team_info[1], team_info[2]))
    conn.close()
    return teams

async def select_team_view(interaction: discord.Interaction, teams):
    class TeamSelect(discord.ui.Select):
        def __init__(self, options):
            super().__init__(placeholder="Select your team", min_values=1, max_values=1, options=options)
        async def callback(self, select_interaction: discord.Interaction):
            self.view.selected = self.values[0]
            self.view.stop()
            await select_interaction.response.defer()
    options = [
        discord.SelectOption(label=f"{t[1]}", description=f"Slot: {t[3][:8]}...", value=str(i))
        for i, t in enumerate(teams)
    ]
    view = discord.ui.View(timeout=60)
    view.selected = None
    view.add_item(TeamSelect(options))
    await interaction.followup.send("You are on multiple teams. Please select which team to use as Team 1:", view=view, ephemeral=True)
    await view.wait()
    return teams[int(view.selected)] if view.selected is not None else None

def get_coach_gm_fo_ids(guild, slot_id):
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    coach_roles = []
    cursor.execute("SELECT head_coach_role, assistant_coach_role, general_manager_role, franchise_owner_role FROM slot_configs WHERE slot_id = ?", (slot_id,))
    row = cursor.fetchone()
    if row:
        coach_roles = [int(r) for r in row if r]
    user_ids = set()
    for member in guild.members:
        for r in coach_roles:
            if discord.utils.get(member.roles, id=r):
                user_ids.add(member.id)
    conn.close()
    return user_ids

def is_cancel_allowed(interaction, team1_slot_id, team2_slot_id):
    if get_management_permission(interaction):
        return True
    allowed = set()
    allowed.update(get_coach_gm_fo_ids(interaction.guild, team1_slot_id))
    allowed.update(get_coach_gm_fo_ids(interaction.guild, team2_slot_id))
    return interaction.user.id in allowed

async def dm_embed_coaches_and_staff(
    guild, user_ids, embed: discord.Embed,
    mention_referees=None, mention_streamers=None
):
    sent = set()
    for user_id in user_ids:
        member = guild.get_member(user_id)
        if member and member.id not in sent:
            extra_mentions = ""
            if mention_referees:
                extra_mentions += f"\nReferees: {mention_referees}"
            if mention_streamers:
                extra_mentions += f"\nStreamers: {mention_streamers}"
            try:
                dm = await member.create_dm()
                await dm.send(content=extra_mentions if extra_mentions else None, embed=embed)
            except Exception:
                pass
            sent.add(member.id)

@bot.tree.command(name="gametime", description="Schedule a game")
@app_commands.describe(
    team1="Select Team 1 (your team, or leave blank to auto-detect)",
    team2="Select Team 2 (opponent team)",
    time="Time of the game (HH:MM)",
    am_pm="AM or PM",
    day="Select the day for the game",
    timezone="Select your timezone"
)
@app_commands.choices(
    day=[
        app_commands.Choice(name="Today", value="today"),
        app_commands.Choice(name="Tomorrow", value="tomorrow"),
        app_commands.Choice(name="In 2 days", value="in_2_days"),
        app_commands.Choice(name="In 3 days", value="in_3_days")
    ]
)
async def gametime(
    interaction: discord.Interaction,
    team1: Optional[discord.Role],
    team2: discord.Role,
    time: str,
    am_pm: Literal["AM", "PM"],
    day: app_commands.Choice[str],
    timezone: Literal["US/Eastern", "US/Central", "US/Mountain", "US/Pacific"]
):
    await interaction.response.defer(ephemeral=True)
    try:
        if team1 is not None and not get_management_permission(interaction) and team1 not in interaction.user.roles:
            await interaction.followup.send("You do not have permission to select Team 1 unless you are on that team, or you are an Admin/Bot Manager.", ephemeral=True)
            return
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        # --- TEAM 1 (user team) ---
        if team1:
            cursor.execute("SELECT team_name, emoji, slot_id FROM league_teams WHERE role_id = ?", (str(team1.id),))
            t1info = cursor.fetchone()
            if not t1info:
                await interaction.followup.send("Selected Team 1 is not a valid team.", ephemeral=True)
                return
            user_team_role = team1
            user_team_name, user_team_emoji, slot_id_1 = t1info[0], t1info[1] or "🏆", t1info[2]
        else:
            teams = await get_user_teams(interaction)
            if len(teams) == 0:
                await interaction.followup.send("You don't have a team role. Please select Team 1 or join a team.", ephemeral=True)
                return
            elif len(teams) == 1:
                user_team_role, user_team_name, user_team_emoji, slot_id_1 = teams[0]
                user_team_emoji = user_team_emoji or "🏆"
            else:
                selected = await select_team_view(interaction, teams)
                if not selected:
                    await interaction.followup.send("No team selected, cancelling.", ephemeral=True)
                    return
                user_team_role, user_team_name, user_team_emoji, slot_id_1 = selected
                user_team_emoji = user_team_emoji or "🏆"
        # --- TEAM 2 (opponent) ---
        cursor.execute("SELECT team_name, emoji, slot_id FROM league_teams WHERE role_id = ?", (str(team2.id),))
        t2info = cursor.fetchone()
        if not t2info:
            await interaction.followup.send("Selected Team 2 is not a valid team.", ephemeral=True)
            return
        opponent_role = team2
        opponent_team_name, opponent_team_emoji, slot_id_2 = t2info[0], t2info[1] or "🏆", t2info[2]
        # --- Find gametime_channel from slot(s) ---
        slot_id = slot_id_1 if slot_id_1 == slot_id_2 else slot_id_1 or slot_id_2
        cursor.execute("SELECT gametime_channel, referee_role, streamer_role FROM slot_configs WHERE slot_id = ?", (slot_id,))
        config = cursor.fetchone()
        if not config or not config[0]:
            await interaction.followup.send("Game time channel is not configured for this slot. Please set it up in slot config.", ephemeral=True)
            return
        gametime_channel_id, referee_role_id, streamer_role_id = config
        gametime_channel = interaction.guild.get_channel(int(gametime_channel_id))
        if not gametime_channel:
            await interaction.followup.send("Game time channel not found or bot missing permissions.", ephemeral=True)
            return

        # Mention referee and streamer roles before game embed
        referee_mention = ""
        streamer_mention = ""
        if referee_role_id:
            ref_role = interaction.guild.get_role(int(referee_role_id))
            if ref_role:
                referee_mention = ref_role.mention
        if streamer_role_id:
            stream_role = interaction.guild.get_role(int(streamer_role_id))
            if stream_role:
                streamer_mention = stream_role.mention
        mention_msg = ""
        if referee_mention or streamer_mention:
            mention_msg = f"{referee_mention} {streamer_mention}".strip()
            await gametime_channel.send(mention_msg)

        # --- Parse time ---
        try:
            hour, minute = map(int, time.split(':'))
            if am_pm == "PM" and hour != 12:
                hour += 12
            elif am_pm == "AM" and hour == 12:
                hour = 0
            days_mapping = {
                "today": 0,
                "tomorrow": 1,
                "in_2_days": 2,
                "in_3_days": 3
            }
            today = datetime.now()
            game_date = today + timedelta(days=days_mapping[day.value])
            game_datetime = game_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
            user_timezone = pytz.timezone(timezone)
            game_datetime = user_timezone.localize(game_datetime)
            unix_ts = int(game_datetime.timestamp())
            # Custom formatted date for title
            pretty_title = game_datetime.strftime("%B %-d %-I:%M%p %Z %Y").replace("AM", "AM").replace("PM", "PM")
            # Discord compatible for EST, CST, etc.
            time_display = game_datetime.strftime("%I:%M %p").lstrip('0')
            date_display = game_datetime.strftime("%A, %B %d, %Y")
            time_zone_short = timezone.split('/')[-1]
            timezone_emoji_map = {
                "Eastern": "🕒",
                "Central": "🕑",
                "Mountain": "🕐",
                "Pacific": "🕕"
            }
            timezone_emoji = timezone_emoji_map.get(time_zone_short, "🕓")
            guild_icon = interaction.guild.icon.url if interaction.guild.icon else None
            guild_name = interaction.guild.name

            class GameView(discord.ui.View):
                def __init__(self):
                    super().__init__(timeout=None)
                    self.referees = []
                    self.streamer = None
                    self.game_time = game_datetime
                    self.message = None
                    self.unique_id = f"gwcs#{str(interaction.id)[:6]}"
                    self.creation_time = datetime.now().strftime("%m/%d/%y, %I:%M %p")
                    self.last_update_minute = -1
                    self.cancelled = False
                    self.dm_sent_gametime = False
                    self.dm_sent_1hr = False
                    self.dm_sent_start = False
                    self.cancelled_by = None
                    self.active_dm_tasks = []

                async def send_gametime_dm(self, all_coach_ids, game_message_link):
                    countdown = f"<t:{unix_ts}:R>!"
                    embed = discord.Embed(
                        title=pretty_title,  # E.g. "July 2 7:00PM EST 2025"
                        description=(
                            f"__**{user_team_emoji} {user_team_role.name} vs {opponent_team_emoji} {opponent_role.name}**__\n\n"
                            f"**Scheduled for:** <t:{unix_ts}:F> ({countdown})\n"
                            f"**Channel:** {game_message_link}\n"
                        ),
                        color=discord.Color.purple()
                    )
                    embed.set_author(name=guild_name, icon_url=guild_icon)
                    if guild_icon:
                        embed.set_thumbnail(url=guild_icon)
                    embed.add_field(name="Timezone", value=f"{timezone_emoji} {time_zone_short}", inline=True)
                    embed.set_footer(text="You are receiving this DM as a team staff member.")
                    await dm_embed_coaches_and_staff(
                        interaction.guild, all_coach_ids, embed
                    )
                async def send_dm_1hr_game(self, all_coach_ids, game_message_link):
                    countdown = f"<t:{unix_ts}:R>!"
                    embed = discord.Embed(
                        title="Game In 1 Hour!",
                        description=(
                            f"__**{user_team_emoji} {user_team_role.name} vs {opponent_team_emoji} {opponent_role.name}**__\n\n"
                            f"**Scheduled for:** <t:{unix_ts}:F> ({countdown})\n"
                            f"**Channel:** {game_message_link}\n"
                        ),
                        color=discord.Color.orange()
                    )
                    embed.set_author(name=guild_name, icon_url=guild_icon)
                    if guild_icon:
                        embed.set_thumbnail(url=guild_icon)
                    embed.add_field(name="Timezone", value=f"{timezone_emoji} {time_zone_short}", inline=True)
                    embed.set_footer(text="This is your 1 hour game reminder.")
                    await dm_embed_coaches_and_staff(
                        interaction.guild, all_coach_ids, embed
                    )
                async def send_dm_game_start(self, all_coach_ids):
                    referee_mentions = ""
                    streamer_mentions = ""
                    if self.referees:
                        referee_mentions = " ".join(f"<@{uid}>" for uid in self.referees)
                    if self.streamer:
                        streamer_mentions = f"<@{self.streamer}>"
                    countdown = f"<t:{unix_ts}:R>!"
                    embed = discord.Embed(
                        title="Game Has Started!",
                        description=(
                            f"__**{user_team_emoji} {user_team_role.name} vs {opponent_team_emoji} {opponent_role.name}**__\n\n"
                            f"**Started at:** <t:{unix_ts}:F> ({countdown})"
                        ),
                        color=discord.Color.green()
                    )
                    embed.set_author(name=guild_name, icon_url=guild_icon)
                    if guild_icon:
                        embed.set_thumbnail(url=guild_icon)
                    embed.add_field(name="Referees", value=referee_mentions or "None", inline=True)
                    embed.add_field(name="Streamer", value=streamer_mentions or "None", inline=True)
                    embed.set_footer(text="Game in progress.")
                    await dm_embed_coaches_and_staff(
                        interaction.guild, all_coach_ids, embed,
                        mention_referees=referee_mentions, mention_streamers=streamer_mentions
                    )
                async def stop_all_dm_tasks(self):
                    for task in self.active_dm_tasks:
                        task.cancel()
                    self.active_dm_tasks.clear()
                async def update_countdown(self):
                    if self.message:
                        game_message_link = f"https://discord.com/channels/{self.message.guild.id}/{self.message.channel.id}/{self.message.id}"
                        coach_ids_1 = get_coach_gm_fo_ids(interaction.guild, slot_id_1)
                        coach_ids_2 = get_coach_gm_fo_ids(interaction.guild, slot_id_2)
                        all_coach_ids = coach_ids_1 | coach_ids_2
                        # Send gametime DM ONCE
                        if all_coach_ids and not self.dm_sent_gametime:
                            await self.send_gametime_dm(all_coach_ids, game_message_link)
                            self.dm_sent_gametime = True
                    while True:
                        try:
                            if not self.message:
                                await asyncio.sleep(10)
                                continue
                            if self.cancelled:
                                await self.stop_all_dm_tasks()
                                cancelled_embed = discord.Embed(
                                    title="Game Cancelled",
                                    description=(
                                        f"{user_team_emoji} {user_team_role.mention} **vs** {opponent_team_emoji} {opponent_role.mention}\n\n"
                                        f"Cancelled by: {self.cancelled_by.mention if self.cancelled_by else 'Unknown'}"
                                    ),
                                    color=discord.Color.red()
                                )
                                cancelled_embed.set_author(name=guild_name, icon_url=guild_icon)
                                if guild_icon:
                                    cancelled_embed.set_thumbnail(url=guild_icon)
                                cancelled_embed.set_footer(
                                    text=f"Originally scheduled by {interaction.user.display_name} • {self.creation_time}"
                                )
                                await self.message.edit(embed=cancelled_embed, view=None)
                                return
                            now = datetime.now(pytz.utc)
                            if now.minute == self.last_update_minute:
                                await asyncio.sleep(5)
                                continue
                            self.last_update_minute = now.minute
                            delta = self.game_time.astimezone(pytz.utc) - now
                            game_message_link = f"https://discord.com/channels/{self.message.guild.id}/{self.message.channel.id}/{self.message.id}"
                            coach_ids_1 = get_coach_gm_fo_ids(interaction.guild, slot_id_1)
                            coach_ids_2 = get_coach_gm_fo_ids(interaction.guild, slot_id_2)
                            all_coach_ids = coach_ids_1 | coach_ids_2
                            if not self.dm_sent_1hr and 3540 <= delta.total_seconds() <= 3660:
                                await self.send_dm_1hr_game(all_coach_ids, game_message_link)
                                self.dm_sent_1hr = True
                            if not self.dm_sent_start and delta.total_seconds() <= 0:
                                await self.send_dm_game_start(all_coach_ids)
                                self.dm_sent_start = True
                                await self.stop_all_dm_tasks()
                            if delta.total_seconds() <= 0:
                                countdown = f"<t:{unix_ts}:R>!"
                                game_started_embed = discord.Embed(
                                    title="Game Has Started!",
                                    description=(
                                        f"{user_team_emoji} {user_team_role.mention} **vs** {opponent_team_emoji} {opponent_role.mention}\n"
                                        f"Countdown: {countdown}"
                                    ),
                                    color=discord.Color.purple()
                                )
                                game_started_embed.set_author(name=guild_name, icon_url=guild_icon)
                                if guild_icon:
                                    game_started_embed.set_thumbnail(url=guild_icon)
                                game_started_embed.set_footer(
                                    text=f"Scheduled by {interaction.user.display_name} • {self.creation_time}"
                                )
                                await self.message.edit(embed=game_started_embed, view=None)
                                return
                            countdown = f"<t:{unix_ts}:R>!"
                            embed = discord.Embed(
                                description="",
                                color=discord.Color.purple()
                            )
                            embed.set_author(
                                name=guild_name,
                                icon_url=guild_icon
                            )
                            if guild_icon:
                                embed.set_thumbnail(url=guild_icon)
                            embed.add_field(
                                name="",
                                value=f"{user_team_emoji} {user_team_role.mention} **vs** {opponent_team_emoji} {opponent_role.mention} {countdown}",
                                inline=False
                            )
                            embed.add_field(
                                name="",
                                value=f"{timezone_emoji} **{time_display} {time_zone_short}** on **{date_display}**",
                                inline=False
                            )
                            ref_value = "None assigned"
                            if self.referees:
                                ref_mentions = []
                                for ref_id in self.referees:
                                    ref_user = interaction.guild.get_member(ref_id)
                                    if ref_user:
                                        ref_mentions.append(f"<@{ref_id}> `{ref_user.display_name}`")
                                ref_value = "\n".join(ref_mentions)
                            embed.add_field(
                                name="🎮 Referee:",
                                value=ref_value,
                                inline=True
                            )
                            streamer_value = "None assigned"
                            if self.streamer:
                                streamer_user = interaction.guild.get_member(self.streamer)
                                if streamer_user:
                                    streamer_value = f"<@{self.streamer}> `{streamer_user.display_name}`"
                            embed.add_field(
                                name="📹 Streamer:",
                                value=streamer_value,
                                inline=True
                            )
                            embed.set_footer(
                                text=f"Scheduled by {interaction.user.display_name} • {self.creation_time}"
                            )
                            await self.message.edit(embed=embed)
                            await asyncio.sleep(30)
                        except discord.NotFound:
                            await self.stop_all_dm_tasks()
                            return
                        except discord.HTTPException as e:
                            if e.code == 429:
                                await asyncio.sleep(60)
                                continue
                            print(f"HTTP Error in countdown: {e}")
                            await self.stop_all_dm_tasks()
                            return
                        except Exception as e:
                            print(f"Error in countdown: {e}")
                            await self.stop_all_dm_tasks()
                            return
                @discord.ui.button(
                    label="Referee",
                    emoji="🎮",
                    style=discord.ButtonStyle.primary,
                    custom_id="referee_button"
                )
                async def referee_button(self, ref_interaction: discord.Interaction, button: discord.ui.Button):
                    if not referee_role_id or str(referee_role_id) not in [str(role.id) for role in ref_interaction.user.roles]:
                        await ref_interaction.response.send_message("You don't have the referee role.", ephemeral=True)
                        return
                    if ref_interaction.user.id in self.referees:
                        self.referees.remove(ref_interaction.user.id)
                        msg = "You've been removed as a referee."
                    else:
                        if len(self.referees) >= 2:
                            await ref_interaction.response.send_message("There are already 2 referees assigned.", ephemeral=True)
                            return
                        self.referees.append(ref_interaction.user.id)
                        msg = "You've been assigned as a referee."
                    await ref_interaction.response.send_message(msg, ephemeral=True)
                @discord.ui.button(
                    label="Streamer",
                    emoji="📹",
                    style=discord.ButtonStyle.success,
                    custom_id="streamer_button"
                )
                async def streamer_button(self, stream_interaction: discord.Interaction, button: discord.ui.Button):
                    if not streamer_role_id or str(streamer_role_id) not in [str(role.id) for role in stream_interaction.user.roles]:
                        await stream_interaction.response.send_message("You don't have the streamer role.", ephemeral=True)
                        return
                    if self.streamer == stream_interaction.user.id:
                        self.streamer = None
                        msg = "You've been removed as the streamer."
                    else:
                        self.streamer = stream_interaction.user.id
                        msg = "You've been assigned as the streamer."
                    await stream_interaction.response.send_message(msg, ephemeral=True)
                @discord.ui.button(
                    label="Cancel Game",
                    emoji="❌",
                    style=discord.ButtonStyle.danger,
                    custom_id="cancel_game"
                )
                async def cancel_game(self, cancel_interaction: discord.Interaction, button: discord.ui.Button):
                    allowed = is_cancel_allowed(cancel_interaction, slot_id_1, slot_id_2)
                    if not allowed:
                        await cancel_interaction.response.send_message("You do not have permission to cancel this game.", ephemeral=True)
                        return
                    self.cancelled = True
                    self.cancelled_by = cancel_interaction.user
                    await self.stop_all_dm_tasks()
                    await cancel_interaction.response.send_message("Game has been cancelled.", ephemeral=True)
                    cancelled_embed = discord.Embed(
                        title="Game Cancelled",
                        description=(
                            f"{user_team_emoji} {user_team_role.mention} **vs** {opponent_team_emoji} {opponent_role.mention}\n\n"
                            f"Cancelled by: {self.cancelled_by.mention}"
                        ),
                        color=discord.Color.red()
                    )
                    cancelled_embed.set_author(name=guild_name, icon_url=guild_icon)
                    if guild_icon:
                        cancelled_embed.set_thumbnail(url=guild_icon)
                    cancelled_embed.set_footer(
                        text=f"Originally scheduled by {interaction.user.display_name} • {self.creation_time}"
                    )
                    await self.message.edit(embed=cancelled_embed, view=None)
            game_view = GameView()
            initial_embed = discord.Embed(
                description="",
                color=discord.Color.purple()
            )
            initial_embed.set_author(
                name=guild_name,
                icon_url=guild_icon
            )
            if guild_icon:
                initial_embed.set_thumbnail(url=guild_icon)
            countdown = f"<t:{unix_ts}:R>!"
            initial_embed.add_field(
                name="",
                value=f"{user_team_emoji} {user_team_role.mention} **vs** {opponent_team_emoji} {opponent_role.mention} {countdown}",
                inline=False
            )
            initial_embed.add_field(
                name="",
                value=f"{timezone_emoji} **{time_display} {time_zone_short}** on **{date_display}**",
                inline=False
            )
            initial_embed.add_field(
                name="🎮 Referee:",
                value="None assigned",
                inline=True
            )
            initial_embed.add_field(
                name="📹 Streamer:",
                value="None assigned",
                inline=True
            )
            initial_embed.set_footer(
                text=f"Scheduled by {interaction.user.display_name} • {game_view.creation_time}"
            )
            message = await gametime_channel.send(embed=initial_embed, view=game_view)
            game_view.message = message
            bot.loop.create_task(game_view.update_countdown())
            await interaction.followup.send(f"Game scheduled successfully in {gametime_channel.mention}!", ephemeral=True)
        except ValueError:
            await interaction.followup.send("Invalid time format. Please use HH:MM format.", ephemeral=True)
            return
    except Exception as e:
        print(f"Error in gametime command: {str(e)}")
        await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)
    finally:
        if 'conn' in locals():
            conn.close()


import discord
from discord import app_commands
from discord.ext import tasks
import sqlite3
from datetime import datetime
import traceback

DB = "transaction_bot.db"
TEAM_TABLE = "league_teams"
SLOT_TABLE = "slots"
SLOT_CONFIGS_TABLE = "slot_configs"

# List of football emojis to replace with team name
FOOTBALL_EMOJIS = {"🏈", "🏉", "🦶"}

def get_db():
    return sqlite3.connect(DB)

def get_slot_id_from_input(guild_id, slot_input):
    if not slot_input:
        return None
    conn = get_db()
    cursor = conn.cursor()
    # Try id first
    cursor.execute(f"SELECT slot_id FROM {SLOT_TABLE} WHERE slot_id = ? AND guild_id = ?", (slot_input, str(guild_id)))
    row = cursor.fetchone()
    if row:
        conn.close()
        return row[0]
    # Try name
    cursor.execute(f"SELECT slot_id FROM {SLOT_TABLE} WHERE slot_name = ? AND guild_id = ?", (slot_input, str(guild_id)))
    row = cursor.fetchone()
    conn.close()
    return row[0] if row else None

def ensure_management_table():
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS live_management_lists (
        guild_id TEXT,
        channel_id TEXT,
        slot_id TEXT,
        display_type TEXT,
        auto_update TEXT DEFAULT 'yes',
        include_empty TEXT DEFAULT 'yes',
        league_style TEXT DEFAULT 'nfl',
        secondary_label TEXT DEFAULT 'AD',
        message_ids TEXT,
        last_updated TIMESTAMP,
        active TEXT DEFAULT 'yes',
        PRIMARY KEY (guild_id, channel_id, slot_id)
    )""")
    conn.commit()
    conn.close()

def get_labels(league_style, secondary_label, display_type):
    """Return tuple (fo_label, gm_label) based on style/settings."""
    if league_style == "college":
        fo_label = "UP" if display_type in ["owners", "both"] else ""
        gm_label = secondary_label if display_type in ["gms", "both"] else ""
    else:
        fo_label = "FO" if display_type in ["owners", "both"] else ""
        gm_label = "GM" if display_type in ["gms", "both"] else ""
    return fo_label, gm_label

def substitute_emoji(emoji, team_name, guild):
    """Replace football emoji or invalid custom emoji with team name."""
    # Default to emoji as is
    if not emoji:
        return team_name
    # Check if it's a unicode football emoji
    if emoji in FOOTBALL_EMOJIS:
        return team_name
    # Check for custom emoji: <a:name:id> or <:name:id>
    if emoji.startswith("<") and emoji.endswith(">"):
        # Try to extract emoji id
        try:
            emoji_id = int(emoji.split(":")[-1][:-1] if emoji.endswith(">") else emoji.split(":")[-1])
            # If not in guild, replace
            if not guild.get_emoji(emoji_id):
                return team_name
        except Exception:
            return team_name
    return emoji

async def update_management_embed(guild, channel, slot_id):
    ensure_management_table()
    conn = get_db()
    cursor = conn.cursor()
    guild_id = str(guild.id)
    channel_id = str(channel.id)
    cursor.execute("""
    SELECT display_type, include_empty, message_ids, league_style, secondary_label, active
    FROM live_management_lists WHERE guild_id = ? AND channel_id = ? AND slot_id = ?
    """, (guild_id, channel_id, slot_id))
    config = cursor.fetchone()
    if not config or config[-1] != "yes":
        conn.close()
        return
    display_type, include_empty, message_ids, league_style, secondary_label, _ = config

    # Remove old messages
    if message_ids:
        for mid in message_ids.split(","):
            try:
                msg = await channel.fetch_message(int(mid))
                await msg.delete()
            except Exception:
                pass

    # Get slot-specific roles from slot_configs!
    cursor.execute(f"SELECT franchise_owner_role, general_manager_role FROM {SLOT_CONFIGS_TABLE} WHERE slot_id = ?", (slot_id,))
    role_config = cursor.fetchone()
    franchise_owner_role = guild.get_role(int(role_config[0])) if role_config and role_config[0] else None
    gm_role = guild.get_role(int(role_config[1])) if role_config and role_config[1] else None

    # Get teams for slot
    cursor.execute(f"SELECT role_id, team_name, emoji FROM {TEAM_TABLE} WHERE slot_id = ?", (slot_id,))
    teams = cursor.fetchall()

    fo_label, gm_label = get_labels(league_style, secondary_label, display_type)

    lines = []
    for role_id, team_name, emoji in teams:
        role_obj = guild.get_role(int(role_id))
        members = role_obj.members if role_obj else []
        owner = next((m for m in members if franchise_owner_role and franchise_owner_role in m.roles), None)
        gm = next((m for m in members if gm_role and gm_role in m.roles), None)

        emoji_or_teamname = substitute_emoji(emoji, team_name, guild)

        if display_type == "both":
            fo_line = f"{fo_label} {emoji_or_teamname}".strip()
            gm_line = f"{gm_label} {emoji_or_teamname}".strip()
            if owner:
                fo_line += f" {owner.mention}"
            if gm:
                gm_line += f" {gm.mention}"
            lines.append(fo_line)
            lines.append(gm_line)
        elif display_type == "owners":
            fo_line = f"{fo_label} {emoji_or_teamname}".strip()
            if owner:
                fo_line += f" {owner.mention}"
            lines.append(fo_line)
        elif display_type == "gms":
            gm_line = f"{gm_label} {emoji_or_teamname}".strip()
            if gm:
                gm_line += f" {gm.mention}"
            lines.append(gm_line)

    # TITLE: Do NOT include slot name, always just "Management List"
    embed = discord.Embed(
        title="Management List",
        description="\n".join(lines) if lines else "No teams found.",
        color=discord.Color.blurple()
    )
    if guild.icon:
        embed.set_thumbnail(url=guild.icon.url)
    embed.set_author(name=guild.name, icon_url=guild.icon.url if guild.icon else None)
    embed.set_footer(text=f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    msg = await channel.send(embed=embed)
    cursor.execute("""
    UPDATE live_management_lists SET message_ids = ?, last_updated = ?
    WHERE guild_id = ? AND channel_id = ? AND slot_id = ?
    """, (str(msg.id), datetime.now().isoformat(), guild_id, channel_id, slot_id))
    conn.commit()
    conn.close()

@tasks.loop(hours=1)
async def update_all_management_lists():
    ensure_management_table()
    import gc
    for obj in gc.get_objects():
        if isinstance(obj, discord.Client):
            bot = obj
            break
    else:
        return
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("""
    SELECT guild_id, channel_id, slot_id
    FROM live_management_lists
    WHERE auto_update = 'yes' AND active = 'yes'
    """)
    for guild_id, channel_id, slot_id in cursor.fetchall():
        guild = bot.get_guild(int(guild_id))
        channel = guild.get_channel(int(channel_id)) if guild else None
        if guild and channel:
            await update_management_embed(guild, channel, slot_id)
    conn.close()

@bot.tree.command(name="live_management_setup", description="Create/set up a live management list in a channel.")
@app_commands.describe(
    channel="Channel to post the list in",
    display_type="Which management roles to display (owners/gms/both)",
    auto_update="Auto-update every hour?",
    include_empty="Show teams with no management?",
    league_style="NFL or College style naming",
    secondary_label="(College only) Use AD or Head Coach label?",
    slot="Slot ID or name (leave blank for all/default)"
)
@app_commands.choices(display_type=[
    app_commands.Choice(name="Top Management Only", value="owners"),
    app_commands.Choice(name="Secondary Management Only", value="gms"),
    app_commands.Choice(name="Both Management Levels", value="both")
])
@app_commands.choices(league_style=[
    app_commands.Choice(name="NFL Style", value="nfl"),
    app_commands.Choice(name="College Style", value="college")
])
@app_commands.choices(auto_update=[
    app_commands.Choice(name="Yes (Update Hourly)", value="yes"),
    app_commands.Choice(name="No (Manual Updates Only)", value="no")
])
@app_commands.choices(include_empty=[
    app_commands.Choice(name="Yes (Show All Teams)", value="yes"),
    app_commands.Choice(name="No (Only Show Filled Positions)", value="no")
])
@app_commands.choices(secondary_label=[
    app_commands.Choice(name="Athletic Director", value="AD"),
    app_commands.Choice(name="Head Coach", value="Head Coach")
])
async def live_management_setup(
    interaction: discord.Interaction,
    channel: discord.TextChannel,
    display_type: app_commands.Choice[str],
    auto_update: app_commands.Choice[str] = None,
    include_empty: app_commands.Choice[str] = None,
    league_style: app_commands.Choice[str] = None,
    secondary_label: app_commands.Choice[str] = None,
    slot: str = None
):
    await interaction.response.defer(ephemeral=True)
    try:
        ensure_management_table()
        conn = get_db()
        cursor = conn.cursor()
        guild_id = str(interaction.guild.id)
        channel_id = str(channel.id)
        slot_id = get_slot_id_from_input(guild_id, slot) if slot else None
        if not slot_id:
            await interaction.followup.send("Could not find the requested slot. Please check your slot name or ID.", ephemeral=True)
            return
        auto_update_val = auto_update.value if auto_update else "yes"
        include_empty_val = include_empty.value if include_empty else "yes"
        league_style_val = league_style.value if league_style else "nfl"
        secondary_label_val = "AD"
        if league_style_val == "college" and secondary_label and secondary_label.value:
            secondary_label_val = secondary_label.value

        cursor.execute("""
        SELECT message_ids FROM live_management_lists
        WHERE guild_id = ? AND channel_id = ? AND slot_id = ?
        """, (guild_id, channel_id, slot_id))
        row = cursor.fetchone()
        if row and row[0]:
            try:
                for mid in row[0].split(","):
                    msg = await channel.fetch_message(int(mid))
                    await msg.delete()
            except Exception:
                pass
        cursor.execute("""
        INSERT OR REPLACE INTO live_management_lists
        (guild_id, channel_id, slot_id, display_type, auto_update, include_empty, league_style, secondary_label, active, last_updated)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'yes', ?)
        """, (
            guild_id, channel_id, slot_id, display_type.value, auto_update_val, include_empty_val,
            league_style_val, secondary_label_val, datetime.now().isoformat()
        ))
        conn.commit()
        conn.close()
        await update_management_embed(interaction.guild, channel, slot_id)
        await interaction.followup.send(
            f"✅ Live management list set up in {channel.mention} (slot: `{slot or slot_id}`)!\n"
            f"• Display: {display_type.name}\n"
            f"• League Style: {league_style.name if league_style else 'NFL'}\n"
            f"• Secondary Label: {secondary_label_val if league_style_val == 'college' else 'N/A'}\n"
            f"• Auto Update: {auto_update.name if auto_update else 'Yes'}\n"
            f"• Include Empty: {include_empty.name if include_empty else 'Yes'}\n"
            f"• Use `/live_management_disable` to remove.",
            ephemeral=True
        )
    except Exception as e:
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error setting up live management list: {e}", ephemeral=True)

@bot.tree.command(name="live_management_disable", description="Remove/disable a live management list in a channel/slot.")
@app_commands.describe(
    channel="Channel to disable list in",
    slot="Slot ID or name (optional, disables all/default if not supplied)"
)
async def live_management_disable(interaction: discord.Interaction, channel: discord.TextChannel, slot: str = None):
    await interaction.response.defer(ephemeral=True)
    try:
        ensure_management_table()
        conn = get_db()
        cursor = conn.cursor()
        guild_id = str(interaction.guild.id)
        channel_id = str(channel.id)
        slot_id = get_slot_id_from_input(guild_id, slot) if slot else None
        if not slot_id:
            await interaction.followup.send("Could not find the requested slot. Please check your slot name or ID.", ephemeral=True)
            return
        cursor.execute("""
        SELECT message_ids FROM live_management_lists
        WHERE guild_id = ? AND channel_id = ? AND slot_id = ? AND active = 'yes'
        """, (guild_id, channel_id, slot_id))
        row = cursor.fetchone()
        if row and row[0]:
            try:
                for mid in row[0].split(","):
                    msg = await channel.fetch_message(int(mid))
                    await msg.delete()
            except Exception:
                pass
        cursor.execute("""
        UPDATE live_management_lists SET active = 'no' WHERE guild_id = ? AND channel_id = ? AND slot_id = ?
        """, (guild_id, channel_id, slot_id))
        conn.commit()
        conn.close()
        await interaction.followup.send(
            f"❌ Live management list disabled in {channel.mention} (slot: `{slot or slot_id}`)", ephemeral=True
        )
    except Exception as e:
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error disabling management list: {e}", ephemeral=True)

@bot.tree.command(name="live_management_update", description="Manually refresh a management list in a channel/slot.")
@app_commands.describe(
    channel="Channel to update",
    slot="Slot ID or name (optional)"
)
async def live_management_update(interaction: discord.Interaction, channel: discord.TextChannel, slot: str = None):
    await interaction.response.defer(ephemeral=True)
    try:
        guild_id = str(interaction.guild.id)
        slot_id = get_slot_id_from_input(guild_id, slot) if slot else None
        if not slot_id:
            await interaction.followup.send("Could not find the requested slot. Please check your slot name or ID.", ephemeral=True)
            return
        await update_management_embed(interaction.guild, channel, slot_id)
        await interaction.followup.send(
            f"✅ Management list updated in {channel.mention} (slot: `{slot or slot_id}`)", ephemeral=True
        )
    except Exception as e:
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error updating management list: {e}", ephemeral=True)

@bot.tree.command(name="live_management_list", description="Show where all live management lists are configured.")
async def live_management_list(interaction: discord.Interaction):
    await interaction.response.defer(ephemeral=True)
    try:
        ensure_management_table()
        conn = get_db()
        cursor = conn.cursor()
        guild_id = str(interaction.guild.id)
        cursor.execute("""
        SELECT channel_id, slot_id, display_type, league_style, secondary_label, auto_update, include_empty, active
        FROM live_management_lists
        WHERE guild_id = ?
        """, (guild_id,))
        rows = cursor.fetchall()
        if not rows:
            await interaction.followup.send("No live management lists are currently configured.", ephemeral=True)
            return
        embed = discord.Embed(
            title=f"Live Management Lists for {interaction.guild.name}",
            color=discord.Color.blue()
        )
        for r in rows:
            channel_id, slot_id, display_type, league_style, secondary_label, auto_update, include_empty, active = r
            status = "🟢 Active" if active == "yes" else "🔴 Disabled"
            # Get slot name for display
            cursor.execute(f"SELECT slot_name FROM {SLOT_TABLE} WHERE slot_id = ?", (slot_id,))
            slotrow = cursor.fetchone()
            slot_name = slotrow[0] if slotrow and slotrow[0] else slot_id
            desc = (
                f"Channel: <#{channel_id}>\n"
                f"Slot: `{slot_name}`\n"
                f"Display: {display_type}\n"
                f"League Style: {league_style}\n"
                f"Secondary Label: {secondary_label if league_style == 'college' else 'N/A'}\n"
                f"Auto Update: {auto_update}\n"
                f"Include Empty: {include_empty}\n"
                f"Status: {status}"
            )
            embed.add_field(name=f"List in <#{channel_id}> / slot `{slot_name}`", value=desc, inline=False)
        await interaction.followup.send(embed=embed, ephemeral=True)
    except Exception as e:
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error listing management lists: {e}", ephemeral=True)




import discord
from discord import app_commands
import uuid

@bot.tree.command(name="autosetup", description="Automatic full setup: global config, slots, teams, slot configs, commands, and demand system.")
@app_commands.checks.has_permissions(administrator=True)
async def autosetup(interaction: discord.Interaction):
    await interaction.response.defer(ephemeral=True)
    guild = interaction.guild
    embed = discord.Embed(title="RosterFlow Auto-Setup Progress", color=discord.Color.blurple())
    msg = await interaction.followup.send(embed=embed, ephemeral=True)
    progress = []

    # --- 1. GLOBAL CONFIG ---
    role_map = {
        "admin_role": ["admin", "bot admin", "administrator", "mod", "moderator"],
        "application_blacklist_role": ["blacklist", "no apply", "app ban", "application blacklist"],
        "suspended_role": ["suspended", "banished", "timeout", "banned"],
    }
    channel_map = {
        "log_channel": ["log", "setup log", "config log", "admin log", "mod log"],
        "application_channel": ["application", "applications", "apply", "recruitment"],
        "suspended_channel": ["suspended", "suspension", "punishment", "discipline"],
    }
    def norm(name): return name.lower().replace('-', ' ').replace('_', ' ')
    def find_best(mapping, name):
        n = norm(name)
        for key, vals in mapping.items():
            for v in vals:
                if v in n:
                    return key
        return None
    updates = {}
    found_roles = []
    found_channels = []
    for role in guild.roles:
        match = find_best(role_map, role.name)
        if match and match not in updates:
            updates[match] = role.id
            found_roles.append(f"{role.mention} → `{match}`")
    for channel in guild.text_channels:
        match = find_best(channel_map, channel.name)
        if match and match not in updates:
            updates[match] = channel.id
            found_channels.append(f"{channel.mention} → `{match}`")
    bot.cursor.execute("INSERT OR IGNORE INTO guild_settings (guild_id) VALUES (?)", (guild.id,))
    if updates:
        set_parts = ", ".join([f"{key} = ?" for key in updates.keys()])
        values = list(updates.values())
        bot.cursor.execute(f"UPDATE guild_settings SET {set_parts} WHERE guild_id = ?", (*values, guild.id))
        bot.conn.commit()
        progress.append(f"✅ Global config: {', '.join(found_roles + found_channels)}")
    else:
        progress.append("⚠️ No global roles/channels found for auto-setup.")
    embed.description = "\n".join(progress)
    await msg.edit(embed=embed)

    # --- 2. DETECT SLOTS, TEAMS, AND PREP CONFIG ---
    slot_defs = [
        ("main", NFL_TEAMS, "NFL"),
        ("FF", FOOTBALL_FUSION_TEAMS, "Football Fusion"),
        ("UF", ULTIMATE_FOOTBALL_TEAMS, "Ultimate Football"),
        ("CFB", COLLEGE_TEAMS, "College Football"),
    ]
    slot_ids = {}
    slot_team_map = {}
    for slot_name, team_list, readable in slot_defs:
        roles = [role for role in guild.roles if role.name != "@everyone" and norm(role.name) in team_list]
        if not roles:
            progress.append(f"🔸 No {readable} teams found for slot `{slot_name}`")
            continue
        # Create slot if missing
        bot.cursor.execute("SELECT slot_id FROM slots WHERE guild_id = ? AND slot_name = ?", (guild.id, slot_name))
        s = bot.cursor.fetchone()
        if s:
            slot_id = s[0]
            created = False
        else:
            slot_id = uuid.uuid4().hex
            bot.cursor.execute("INSERT INTO slots (slot_id, guild_id, slot_name, description) VALUES (?, ?, ?, ?)", (slot_id, guild.id, slot_name, f"Auto-created {readable} slot"))
            created = True
        # Always ensure slot config tables exist
        bot.cursor.execute("INSERT OR IGNORE INTO slot_configs (slot_id) VALUES (?)", (slot_id,))
        bot.cursor.execute("INSERT OR IGNORE INTO slot_command_settings (slot_id) VALUES (?)", (slot_id,))
        bot.cursor.execute("INSERT OR IGNORE INTO slot_demand_config (slot_id) VALUES (?)", (slot_id,))
        slot_ids[slot_name] = slot_id
        slot_team_map[slot_name] = roles
        # Add teams
        added = 0
        for role in roles:
            bot.cursor.execute("INSERT OR IGNORE INTO league_teams (role_id, slot_id, team_name, emoji) VALUES (?, ?, ?, ?)", (str(role.id), slot_id, role.name, "🏈"))
            added += 1
        bot.conn.commit()
        progress.append(f"{'✅ Created' if created else '🔄 Updated'} slot `{slot_name}` with {added} {readable} teams.")
        embed.description = "\n".join(progress)
        await msg.edit(embed=embed)

    # --- 3. SLOT CONFIG: ROLES, CHANNELS, PICKUP, DEMAND, COMMANDS ---
    slot_role_map = {
        "franchise_owner_role": ["franchise owner", "owner", "co-owner", "franchiseowner"],
        "general_manager_role": ["general manager", "gm", "manager", "gen manager", "generalmanager"],
        "head_coach_role": ["head coach", "coach", "headcoach"],
        "assistant_coach_role": ["assistant coach", "asst coach", "assistantcoach"],
        "free_agent_role": ["free agent", "freeagent"],
        "referee_role": ["referee", "ref", "official"],
        "streamer_role": ["streamer", "broadcast", "caster", "broadcaster"],
        "pickup_host_role": ["pickup host", "host", "pickuphost"],
        "five_demands_role": ["five demands", "5 demands", "v demand 5", "five demand"],
        "four_demands_role": ["four demands", "4 demands", "v demand 4", "four demand"],
        "three_demands_role": ["three demands", "3 demands", "v demand 3", "three demand"],
        "two_demands_role": ["two demands", "2 demands", "v demand 2", "two demand"],
        "one_demand_role": ["one demand", "1 demand", "v demand 1"],
        "no_demands_role": ["no demands", "no demand", "v demand 0", "0 demand"],
    }
    slot_channel_map = {
        "transaction_channel": ["transaction", "transactions", "sign", "signings", "waivers"],
        "gametime_channel": ["game time", "gametimes", "schedule", "gametime", "schedules"],
        "trade_channel": ["trade", "trades"],
        "draft_channel": ["draft", "drafts"],
        "pickup_channel": ["pickup channel", "pickup", "pickups"],
        "demand_channel": ["demand", "demands", "v demand", "vdemands"],
    }
    command_defaults = {
        "sign_enabled": "on",
        "release_enabled": "on",
        "offer_enabled": "on",
        "roster_cap": 25,
    }
    demand_defaults = {
        "demands_enabled": "on",
        "demand_limit": "true",
    }
    for slot_name, slot_id in slot_ids.items():
        slot_updates = {}
        slot_chan_updates = {}
        demand_updates = {}
        # ROLES
        for role in guild.roles:
            match = find_best(slot_role_map, role.name)
            if match:
                if match in ["five_demands_role", "four_demands_role", "three_demands_role", "two_demands_role", "one_demand_role", "no_demands_role"]:
                    demand_updates[match] = role.id
                else:
                    slot_updates[match] = role.id
        # CHANNELS
        for channel in guild.text_channels:
            match = find_best(slot_channel_map, channel.name)
            if match == "demand_channel":
                demand_updates[match] = channel.id
            elif match:
                slot_chan_updates[match] = channel.id
        # SAVE slot_configs
        if slot_updates or slot_chan_updates:
            all_updates = {**slot_updates, **slot_chan_updates}
            set_parts = ", ".join([f"{key} = ?" for key in all_updates.keys()])
            values = list(all_updates.values())
            bot.cursor.execute("UPDATE slot_configs SET " + set_parts + " WHERE slot_id = ?", (*values, slot_id))
        # COMMAND SETTINGS
        bot.cursor.execute("UPDATE slot_command_settings SET sign_enabled = ?, release_enabled = ?, offer_enabled = ?, roster_cap = ? WHERE slot_id = ?",
            (command_defaults["sign_enabled"], command_defaults["release_enabled"], command_defaults["offer_enabled"], command_defaults["roster_cap"], slot_id))
        # DEMAND SYSTEM
        demand_updates = {**demand_defaults, **demand_updates}
        set_parts = ", ".join([f"{key} = ?" for key in demand_updates.keys()])
        values = list(demand_updates.values())
        bot.cursor.execute("UPDATE slot_demand_config SET " + set_parts + " WHERE slot_id = ?", (*values, slot_id))
        bot.conn.commit()
        progress.append(f"🛠️ Fully auto-configured slot `{slot_name}` (roles, channels, pickup, command, demand system).")
        embed.description = "\n".join(progress)
        await msg.edit(embed=embed)

    embed.color = discord.Color.green()
    embed.set_footer(text="You can use /global_setup and /setup to review or edit details.")
    await msg.edit(embed=embed)




import discord
from discord import app_commands
import sqlite3
import traceback
from datetime import datetime as DateTime, timezone as TimeZone
from typing import Optional, List, Dict, Any
import json
import uuid
import re

# =========================
# Utility Functions
# =========================

def get_emoji_url(emoji_str: Optional[str]) -> Optional[str]:
    if not emoji_str:
        return None
    custom_emoji_match = re.match(r'<a?:[\w]+:(\d+)>', emoji_str)
    if custom_emoji_match:
        emoji_id = custom_emoji_match.group(1)
        return f"https://cdn.discordapp.com/emojis/{emoji_id}.png"
    return None

def get_slot_id_from_team_role(team_role_id: int) -> Optional[str]:
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    cursor.execute("SELECT slot_id FROM league_teams WHERE role_id = ?", (str(team_role_id),))
    row = cursor.fetchone()
    conn.close()
    return row[0] if row else None

def get_slot_config(slot_id: str) -> Optional[Dict[str, Any]]:
    if not slot_id:
        return None
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM slot_configs WHERE slot_id = ?", (slot_id,))
    row = cursor.fetchone()
    if row:
        columns = [description[0] for description in cursor.description]
        config_dict = dict(zip(columns, row))
        conn.close()
        return config_dict
    conn.close()
    return None

async def has_bot_management_permission_global(interaction: discord.Interaction, slot_id: Optional[str]) -> bool:
    config = get_slot_config(slot_id) if slot_id else None
    admin_role_id = config.get('admin_role') if config else None
    if interaction.user.guild_permissions.administrator:
        return True
    if admin_role_id and interaction.guild:
        admin_role_obj = interaction.guild.get_role(admin_role_id)
        if admin_role_obj and admin_role_obj in interaction.user.roles:
            return True
    return False

def chunk_embeds(fields: List[dict], base_embed: discord.Embed, max_chars=5900, max_fields=25):
    embeds = []
    current_embed = base_embed.copy()
    current_length = len(base_embed.description or "")
    field_count = 0
    for field in fields:
        field_str = f"{field['name']}{field['value']}"
        if (current_length + len(field_str) > max_chars) or (field_count >= max_fields):
            embeds.append(current_embed)
            current_embed = base_embed.copy()
            current_length = len(base_embed.description or "")
            field_count = 0
        current_embed.add_field(name=field['name'], value=field['value'], inline=False)
        current_length += len(field_str)
        field_count += 1
    if field_count > 0 or not embeds:
        embeds.append(current_embed)
    return embeds

# =========================
# DB Initialization
# =========================

def init_disband_db():
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS disband_transactions (
            disband_id TEXT PRIMARY KEY,
            guild_id INTEGER NOT NULL,
            team_role_id INTEGER NOT NULL,
            team_name TEXT NOT NULL,
            team_emoji TEXT,
            disband_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            affected_members_data TEXT NOT NULL,
            initiator_id INTEGER NOT NULL,
            message_id INTEGER,
            is_undone INTEGER DEFAULT 0
        )
    ''')
    conn.commit()
    conn.close()

init_disband_db()

# =========================
# Undo View for Disband
# =========================

class DisbandUndoView(discord.ui.View):
    def __init__(self, disband_id: str):
        super().__init__(timeout=3600.0)
        self.disband_id = disband_id
        self.message = None

    @discord.ui.button(label="Undo Disband", style=discord.ButtonStyle.red, custom_id="undo_disband_button")
    async def undo_disband_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        cursor.execute(
            "SELECT is_undone, affected_members_data, team_role_id, team_name, team_emoji, guild_id FROM disband_transactions WHERE disband_id = ?",
            (self.disband_id,)
        )
        disband_info = cursor.fetchone()
        if not disband_info:
            await interaction.response.send_message("This disband transaction could not be found or has expired.", ephemeral=True)
            conn.close()
            return
        is_undone, affected_members_data_json, team_role_id, team_name, team_emoji, guild_id = disband_info
        slot_id = get_slot_id_from_team_role(team_role_id)
        if is_undone:
            await interaction.response.send_message("This disband has already been undone.", ephemeral=True)
            conn.close()
            return
        if not await has_bot_management_permission_global(interaction, slot_id):
            await interaction.response.send_message("You do not have permission to undo this disband.", ephemeral=True)
            conn.close()
            return
        await interaction.response.defer(ephemeral=True)
        affected_members_data: List[Dict[str, Any]] = json.loads(affected_members_data_json)
        guild = interaction.guild
        if not guild or guild.id != guild_id:
            await interaction.followup.send("This disband transaction does not belong to this server.", ephemeral=True)
            conn.close()
            return

        config = get_slot_config(slot_id)
        free_agent_role_id = config.get('free_agent_role') if config else None
        free_agent_role_obj = guild.get_role(free_agent_role_id) if free_agent_role_id else None

        fo_role_obj = guild.get_role(config.get('franchise_owner_role')) if config else None
        gm_role_obj = guild.get_role(config.get('general_manager_role')) if config else None
        hc_role_obj = guild.get_role(config.get('head_coach_role')) if config else None
        ac_role_obj = guild.get_role(config.get('assistant_coach_role')) if config else None

        successful_reversions = []
        failed_reversions = []
        for member_data in affected_members_data:
            player_id = member_data['player_id']
            old_role_ids = member_data['old_role_ids']
            member: discord.Member = guild.get_member(player_id)
            if not member:
                try:
                    member = await guild.fetch_member(player_id)
                except discord.NotFound:
                    failed_reversions.append(f"• User with ID `{player_id}` (not in server)")
                    continue
                except discord.HTTPException as e:
                    failed_reversions.append(f"• User with ID `{player_id}` (error fetching: {e})")
                    continue
            try:
                # Remove free agent role
                if free_agent_role_obj and free_agent_role_obj in member.roles:
                    await member.remove_roles(free_agent_role_obj, reason=f"Undo disband: {team_name} - removed Free Agent")
                # Add back all original roles, but only if they still exist for this slot
                roles_to_add_back = []
                for role_id in old_role_ids:
                    role_obj = guild.get_role(role_id)
                    add_this = False
                    if role_obj:
                        if fo_role_obj and role_obj.id == fo_role_obj.id:
                            add_this = True
                        elif gm_role_obj and role_obj.id == gm_role_obj.id:
                            add_this = True
                        elif hc_role_obj and role_obj.id == hc_role_obj.id:
                            add_this = True
                        elif ac_role_obj and role_obj.id == ac_role_obj.id:
                            add_this = True
                        elif role_obj.id == team_role_id:
                            add_this = True
                        else:
                            # Could be unrelated role, don't block restoring
                            add_this = True
                    if add_this and role_obj and role_obj not in member.roles:
                        roles_to_add_back.append(role_obj)
                if roles_to_add_back:
                    await member.add_roles(*roles_to_add_back, reason=f"Undo disband: {team_name} - roles restored")
                successful_reversions.append(f"• {member.mention} (`{member.display_name}`) roles restored.")
            except discord.Forbidden:
                failed_reversions.append(f"• {member.mention} (`{member.display_name}`) - Bot lacks permissions.")
            except Exception as e:
                failed_reversions.append(f"• {member.mention} - Error: {e}")
        cursor.execute("UPDATE disband_transactions SET is_undone = 1 WHERE disband_id = ?", (self.disband_id,))
        conn.commit()
        for item in self.children:
            item.disabled = True
        if self.message:
            try:
                await self.message.edit(content=f"*Disband Undo: Action complete. Button disabled.*", view=self)
            except discord.HTTPException:
                pass
        undo_embed_color = discord.Color.blue() if not failed_reversions else discord.Color.orange()
        undo_embed = discord.Embed(
            title=f"{team_name} has been undisbanded",
            description=(f"The disbandment of {team_emoji or ''} **{team_name}** has been **undone** by {interaction.user.mention}.\n"
                         f"All affected members' roles have been restored."),
            color=undo_embed_color,
            timestamp=DateTime.now(TimeZone.utc)
        )
        if guild.icon:
            undo_embed.set_author(name=guild.name, icon_url=guild.icon.url)
        if successful_reversions:
            undo_embed.add_field(name="✅ Successfully Reverted Members", value="\n".join(successful_reversions), inline=False)
        if failed_reversions:
            undo_embed.add_field(name="❌ Failed Reversions", value="\n".join(failed_reversions), inline=False)
        undo_embed.set_footer(text=f"Action by {interaction.user.display_name}", icon_url=interaction.user.display_avatar.url)
        transaction_channel_id = config.get('transaction_channel') if config else None
        if transaction_channel_id:
            log_channel = guild.get_channel(transaction_channel_id)
            if log_channel and isinstance(log_channel, discord.TextChannel):
                try:
                    await log_channel.send(embed=undo_embed)
                except Exception:
                    pass
        await interaction.followup.send("Disbandment successfully undone and roles reverted.", ephemeral=True)
        conn.close()

# =========================
# Slash Command: Disband
# =========================

@bot.tree.command(name="disband", description="Unrole all players from a team and optionally move to Free Agent.")
@app_commands.describe(team="The team to disband (unrole).")
async def disband(interaction: discord.Interaction, team: discord.Role):
    await interaction.response.defer(ephemeral=True)
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        cursor.execute(
            "SELECT team_name, emoji FROM league_teams WHERE role_id = ?",
            (str(team.id),)
        )
        team_info = cursor.fetchone()
        if not team_info:
            await interaction.followup.send("Team not found in the bot's database. Please make sure the team is registered.", ephemeral=True)
            return
        team_name_from_db, team_emoji_from_db = team_info
        slot_id = get_slot_id_from_team_role(team.id)
        config = get_slot_config(slot_id)
        if not config:
            await interaction.followup.send("Slot configuration not found. Please ensure this slot is set up.", ephemeral=True)
            return

        fo_role_id = config.get('franchise_owner_role')
        gm_role_id = config.get('general_manager_role')
        hc_role_id = config.get('head_coach_role')
        ac_role_id = config.get('assistant_coach_role')

        fo_role_obj = interaction.guild.get_role(fo_role_id) if fo_role_id else None
        gm_role_obj = interaction.guild.get_role(gm_role_id) if gm_role_id else None
        hc_role_obj = interaction.guild.get_role(hc_role_id) if hc_role_id else None
        ac_role_obj = interaction.guild.get_role(ac_role_id) if ac_role_id else None

        is_franchise_owner_of_team = False
        if fo_role_obj and fo_role_obj in interaction.user.roles and team in interaction.user.roles:
            is_franchise_owner_of_team = True

        is_bot_manager = await has_bot_management_permission_global(interaction, slot_id)
        if not is_franchise_owner_of_team and not is_bot_manager:
            await interaction.followup.send(
                "You do not have permission to disband this team. Only the Franchise Owner of the team "
                "or a Bot Administrator/Manager for this slot can use this command.",
                ephemeral=True
            )
            return

        free_agent_role_id = config.get('free_agent_role')
        free_agent_role = interaction.guild.get_role(free_agent_role_id) if free_agent_role_id else None

        affected_members_for_rollback: List[Dict[str, Any]] = []
        failed_role_changes = []

        owners, gms, hcs, acs, others = [], [], [], [], []

        for member in team.members:
            original_member_roles_ids: List[int] = [role.id for role in member.roles if role != team]
            original_member_roles_ids.append(team.id)
            affected_members_for_rollback.append({
                "player_id": member.id,
                "old_role_ids": original_member_roles_ids
            })
            is_fo = fo_role_obj and fo_role_obj in member.roles
            is_gm = gm_role_obj and gm_role_obj in member.roles
            is_hc = hc_role_obj and hc_role_obj in member.roles
            is_ac = ac_role_obj and ac_role_obj in member.roles
            if is_fo:
                owners.append(member)
            if is_gm:
                gms.append(member)
            if is_hc:
                hcs.append(member)
            if is_ac:
                acs.append(member)
            if not (is_fo or is_gm or is_hc or is_ac):
                others.append(member)
            roles_to_remove_from_member = [team]
            if is_fo and fo_role_obj:
                roles_to_remove_from_member.append(fo_role_obj)
            if is_gm and gm_role_obj:
                roles_to_remove_from_member.append(gm_role_obj)
            if is_hc and hc_role_obj:
                roles_to_remove_from_member.append(hc_role_obj)
            if is_ac and ac_role_obj:
                roles_to_remove_from_member.append(ac_role_obj)
            try:
                await member.remove_roles(*roles_to_remove_from_member, reason=f"Disband: {team.name} unroled by {interaction.user.display_name}")
                if free_agent_role:
                    await member.add_roles(free_agent_role, reason=f"Disband: {team.name} unroled - now Free Agent")
            except discord.Forbidden:
                failed_role_changes.append(f"• {member.mention} `{member.display_name}` (Missing Permissions)")
            except Exception as e:
                failed_role_changes.append(f"• {member.mention} (`{member.display_name}`) - Error: {e})")

        disband_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO disband_transactions (
                disband_id, guild_id, team_role_id, team_name, team_emoji, affected_members_data, initiator_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            disband_id,
            interaction.guild.id,
            team.id,
            team_name_from_db,
            team_emoji_from_db,
            json.dumps(affected_members_for_rollback),
            interaction.user.id
        ))
        conn.commit()

        staff_fields = []
        def member_lines(members):
            return "\n".join([f"{m.mention} `{m.display_name}`" for m in members]) if members else "*None*"
        staff_fields.append({"name": "----------Franchise Owners----------", "value": member_lines(owners)})
        staff_fields.append({"name": "----------General Managers----------", "value": member_lines(gms)})
        staff_fields.append({"name": "----------Head Coaches----------", "value": member_lines(hcs)})
        staff_fields.append({"name": "----------Assistant Coaches----------", "value": member_lines(acs)})

        affected_members_display = []
        for member_data in affected_members_for_rollback:
            member_obj = interaction.guild.get_member(member_data['player_id'])
            if member_obj:
                affected_members_display.append(f"• {member_obj.mention} (`{member_obj.display_name}`)")
            else:
                affected_members_display.append(f"• User ID: `{member_data['player_id']}` (Not found in server)")
        staff_fields.append({"name": "All Team Members", "value": "\n".join(affected_members_display) if affected_members_display else "No members were in this team."})

        base_embed = discord.Embed(
            title=f"{team_name_from_db} has been unroled",
            description=f"{team_emoji_from_db or ''} {team.mention} (`{team_name_from_db}`) has been **unroled** by {interaction.user.mention}.",
            color=team.color if team.color != discord.Color.default() else discord.Color.red(),
            timestamp=DateTime.now(TimeZone.utc)
        )
        emoji_url = get_emoji_url(team_emoji_from_db)
        if emoji_url:
            base_embed.set_thumbnail(url=emoji_url)
        if interaction.guild.icon:
            base_embed.set_author(name=interaction.guild.name, icon_url=interaction.guild.icon.url)
        base_embed.set_footer(text=f"Action by {interaction.user.display_name}", icon_url=interaction.user.display_avatar.url)

        embeds = chunk_embeds(staff_fields + [
            {"name": "Disband Transaction ID", "value": f"`{disband_id}`"}
        ], base_embed)

        transaction_channel_id = config.get('transaction_channel')
        if transaction_channel_id:
            log_channel = interaction.guild.get_channel(transaction_channel_id)
            if log_channel and isinstance(log_channel, discord.TextChannel):
                try:
                    view = DisbandUndoView(disband_id)
                    for idx, embed in enumerate(embeds):
                        if idx == len(embeds)-1:
                            message = await log_channel.send(embed=embed, view=view)
                            view.message = message
                        else:
                            await log_channel.send(embed=embed)
                except Exception:
                    await interaction.followup.send("Team unroled, but could not send log to transaction channel.", ephemeral=True)
        else:
            await interaction.followup.send("Team unroled, but no transaction channel is configured.", ephemeral=True)
        if failed_role_changes:
            fail_message = "Some members' roles could not be adjusted due to permissions issues:\n" + "\n".join(failed_role_changes)
            await interaction.followup.send(f"Team unroled, but {fail_message}", ephemeral=True)
        else:
            await interaction.followup.send(f"Team {team.name} unroled successfully!", ephemeral=True)
        conn.close()
    except Exception as error:
        print(f"Error in disband command: {error}")
        traceback.print_exc()
        await interaction.followup.send(f"An unexpected error occurred: {str(error)}", ephemeral=True)

# =========================
# Slash Command: Undo Disband
# =========================

@bot.tree.command(name="undo_disband", description="Rollback a team disbandment using its ID.")
@app_commands.describe(disband_id="The unique ID of the disband transaction to undo.")
async def undo_disband(interaction: discord.Interaction, disband_id: str):
    conn = sqlite3.connect('transaction_bot.db')
    cursor = conn.cursor()
    cursor.execute("SELECT team_role_id, guild_id FROM disband_transactions WHERE disband_id = ?", (disband_id,))
    row = cursor.fetchone()
    if not row:
        await interaction.response.send_message("No disband transaction found with that ID.", ephemeral=True)
        conn.close()
        return
    team_role_id, guild_id = row
    if interaction.guild.id != guild_id:
        await interaction.response.send_message("This disband transaction does not belong to this server.", ephemeral=True)
        conn.close()
        return
    slot_id = get_slot_id_from_team_role(team_role_id)
    if not await has_bot_management_permission_global(interaction, slot_id):
        await interaction.response.send_message("You do not have permission to undo disbandments.", ephemeral=True)
        conn.close()
        return
    view = DisbandUndoView(disband_id)
    await view.undo_disband_button(interaction, None)
    conn.close()





import discord
from discord.ext import commands
import gspread
from google.oauth2 import service_account
import re
import json
import os
import logging
import csv
import io
import uuid
from datetime import datetime

# --- Configure Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Google Sheets Authentication ---
SERVICE_ACCOUNT_JSON_FILENAME = 'roster-flow-69234ee806e5.json'
GOOGLE_SERVICE_ACCOUNT_ENV_VAR = 'GOOGLE_SERVICE_ACCOUNT_INFO'

gc = None # Initialize gc to None

def authenticate_google_sheets():
    """Authenticates with Google Sheets using a JSON file or environment variable."""
    global gc
    service_account_info = None

    if os.path.exists(SERVICE_ACCOUNT_JSON_FILENAME):
        try:
            with open(SERVICE_ACCOUNT_JSON_FILENAME, 'r') as f:
                service_account_info = json.load(f)
            gc = gspread.service_account(filename=SERVICE_ACCOUNT_JSON_FILENAME)
            logging.info(f"✅ Loaded service account from JSON file: {SERVICE_ACCOUNT_JSON_FILENAME}")
        except Exception as e:
            logging.error(f"❌ Failed to authenticate using JSON file '{SERVICE_ACCOUNT_JSON_FILENAME}': {e}")
            logging.error("Please ensure the JSON file is valid and accessible.")
    else:
        logging.warning(f"⚠️ JSON file '{SERVICE_ACCOUNT_JSON_FILENAME}' not found. Falling back to environment variable.")
        service_account_json_str = os.getenv(GOOGLE_SERVICE_ACCOUNT_ENV_VAR)
        if service_account_json_str:
            try:
                service_account_info = json.loads(service_account_json_str)
                logging.info(f"✅ Loaded service account info from {GOOGLE_SERVICE_ACCOUNT_ENV_VAR} environment variable.")
            except json.JSONDecodeError as e:
                logging.error(f"❌ Failed to parse {GOOGLE_SERVICE_ACCOUNT_ENV_VAR} environment variable as JSON: {e}")
                service_account_info = None
            
            if service_account_info:
                try:
                    credentials = service_account.Credentials.from_service_account_info(
                        service_account_info,
                        scopes=['https://www.googleapis.com/auth/spreadsheets']
                    )
                    gc = gspread.authorize(credentials)
                    logging.info("✅ Authenticated using service account info from environment variable.")
                except Exception as e:
                    logging.error(f"❌ Authentication failed using environment variable credentials: {e}")
                    logging.error(f"Ensure your {GOOGLE_SERVICE_ACCOUNT_ENV_VAR} environment variable is correctly formatted JSON.")
        else:
            logging.error("❌ No service account JSON file or valid environment variable found.")
            logging.error(f"Please create '{SERVICE_ACCOUNT_JSON_FILENAME}' or set '{GOOGLE_SERVICE_ACCOUNT_ENV_VAR}' environment variable.")

    if gc:
        try:
            spreadsheets = gc.openall()
            logging.info(f"✅ Successfully accessed Google Sheets API. Found {len(spreadsheets)} spreadsheet(s).")
        except Exception as e:
            logging.error(f"❌ Google Sheets API access test failed: {e}")
            logging.error("This might indicate an issue with permissions or network connectivity, even if authentication seemed to pass.")
            gc = None # Set gc to None if API access fails to prevent further errors
    else:
        logging.critical("❌ Google Sheets client (gc) is not initialized. Bot commands requiring sheet access will fail.")

# Perform authentication on script start
authenticate_google_sheets()

# --- Connected Sheets Data Management ---
CONNECTED_SHEETS_FILE = 'connected_sheets.json'
connected_sheets = {} # Stores {guild_id: gspread.Spreadsheet object}

def save_connected_sheets() -> None:
    """Saves the connected_sheets dictionary to a JSON file."""
    # We only save the sheet URL, not the gspread WorkSheet object itself.
    # The WorkSheet object needs to be re-opened upon bot restart.
    data_to_save = {str(guild_id): sheet.url for guild_id, sheet in connected_sheets.items()}
    try:
        with open(CONNECTED_SHEETS_FILE, 'w') as f:
            json.dump(data_to_save, f, indent=4)
        logging.info(f"💾 Connected sheets data saved to {CONNECTED_SHEETS_FILE}")
    except Exception as e:
        logging.error(f"❌ Error saving connected sheets data: {e}")

async def load_connected_sheets() -> None:
    """Loads connected_sheets data from a JSON file and re-establishes sheet connections."""
    global connected_sheets
    if not gc:
        logging.warning("⚠️ Google Sheets client not initialized, cannot load connected sheets.")
        return

    if os.path.exists(CONNECTED_SHEETS_FILE):
        try:
            with open(CONNECTED_SHEETS_FILE, 'r') as f:
                loaded_data = json.load(f)
            
            re_established_connections = {}
            for guild_id_str, sheet_link in loaded_data.items():
                guild_id = int(guild_id_str)
                try:
                    sheet = gc.open_by_url(sheet_link) if "docs.google.com" in sheet_link else gc.open_by_key(sheet_link)
                    re_established_connections[guild_id] = sheet
                    logging.info(f"🔗 Re-established connection for guild ID {guild_id} to sheet: {sheet_link}")
                except Exception as e:
                    logging.error(f"❌ Failed to re-establish connection for guild ID {guild_id} to sheet {sheet_link}: {e}")
            
            connected_sheets = re_established_connections
            logging.info(f"✅ Loaded {len(connected_sheets)} connected sheet(s) from {CONNECTED_SHEETS_FILE}")
        except Exception as e:
            logging.error(f"❌ Error loading connected sheets data: {e}")
            connected_sheets = {} # Reset to empty if loading fails
    else:
        logging.info(f"ℹ️ {CONNECTED_SHEETS_FILE} not found. Starting with no connected sheets.")

# --- CSV Listening Channels Data Management ---
LISTENING_CHANNELS_FILE = 'listening_channels.json'
listening_channels = {} # Stores {guild_id: [channel_id, ...]}

def save_listening_channels() -> None:
    """Saves the listening_channels dictionary to a JSON file."""
    data_to_save = {str(guild_id): channel_ids for guild_id, channel_ids in listening_channels.items()}
    try:
        with open(LISTENING_CHANNELS_FILE, 'w') as f:
            json.dump(data_to_save, f, indent=4)
        logging.info(f"💾 Listening channels data saved to {LISTENING_CHANNELS_FILE}")
    except Exception as e:
        logging.error(f"❌ Error saving listening channels data: {e}")

def load_listening_channels() -> None:
    """Loads listening_channels data from a JSON file."""
    global listening_channels
    if os.path.exists(LISTENING_CHANNELS_FILE):
        try:
            with open(LISTENING_CHANNELS_FILE, 'r') as f:
                loaded_data = json.load(f)
            listening_channels = {int(guild_id_str): channel_ids for guild_id_str, channel_ids in loaded_data.items()}
        except Exception as e:
            logging.error(f"❌ Error loading listening channels data: {e}")
            listening_channels = {} # Reset to empty if loading fails
    else:
        logging.info(f"ℹ️ {LISTENING_CHANNELS_FILE} not found. Starting with no listening channels.")

# Load listening channels on script start
load_listening_channels()

# --- Configuration Constants ---

# Define primary colors for positions (in RGB for gspread formatting)
POSITION_COLORS = {
    "QB": {"red": 0.25, "green": 0.5, "blue": 1.0},        # Blue
    "RB": {"red": 1.0, "green": 0.25, "blue": 0.25},        # Red
    "WR": {"red": 0.25, "green": 1.0, "blue": 0.25},        # Green
    "TE": {"red": 1.0, "green": 0.65, "blue": 0.0},         # Orange
    "Center": {"red": 0.6, "green": 0.4, "blue": 0.2},      # Brown
    "DE": {"red": 0.6, "green": 0.2, "blue": 0.8},          # Purple
    "LB": {"red": 1.0, "green": 1.0, "blue": 0.0},          # Yellow
    "Kicker": {"red": 0.0, "green": 1.0, "blue": 1.0},      # Cyan
    "DB": {"red": 0.8, "green": 0.2, "blue": 0.6}           # Pink/Magenta for Defensive Backs
}

# Define sort keys for each position to determine top players based on new templates
POSITION_SORT_KEYS = {
    "QB": "Passes PTs",
    "RB": "Yards",
    "WR": "Yards",
    "TE": "yards",
    "Center": "snaps",
    "DE": "Sacks",
    "LB": "tackles",
    "Kicker": "Completed",
    "DB": "Ints"
}

# Define headers for each position based on the new sheet templates
SHEET_HEADERS = {
    "QB": ["Team", "username", "Passes PTs", "YDs", "C%", "TDs", "INT", "SCK", "3RD", "4TH", "SNAP"],
    "RB": ["Team", "username", "ATT", "Yards", "TDS", "Y/A"],
    "WR": ["Team", "Username", "Yards", "TDs", "Catch", "Target", "%", "Int allowd", "SNAP"],
    "TE": ["team", "username", "yards", "yac", "Tds", "%", "Catch", "targets", "sacks", "snap"],
    "Center": ["Team", "username", "snaps", "sacks", "pressures", "qbh", "SPS"],
    "DE": ["Team", "Username", "Sacks", "Pressures", "QBH", "Tackles", "Safeties", "snap"],
    "LB": ["Team", "Username", "ints", "tackles", "Targets", "CmpAll", "cmp%", "YDA", "TDA", "SNAP"],
    "Kicker": ["Team", "Username", "Completed", "ATT", "Long", "FG%"],
    "DB": ["Team", "Username", "Ints", "Tackles", "%", "CmpAll", "Targets", "YDA", "TDA", "SNAPS"]
}

# Mapping for sheet titles
SHEET_TITLES = {
    "QB": "Quarterback Statistics",
    "RB": "Running Back Statistics",
    "WR": "Wide Receiver Statistics",
    "TE": "Tight End Statistics",
    "Center": "Center Statistics",
    "DE": "Defensive End Statistics",
    "LB": "Linebacker Statistics",
    "Kicker": "Kicker Statistics",
    "DB": "Defensive Back Statistics"
}

# --- Utility Functions ---

def col_index_to_letter(col_index: int) -> str:
    """Converts a 0-based column index to an Excel-style column letter."""
    letter = ''
    while col_index >= 0:
        letter = chr(ord('A') + (col_index % 26)) + letter
        col_index = (col_index // 26) - 1
    return letter

def apply_sheet_formatting(ws: gspread.Worksheet, position_name: str, position_color: dict) -> None:
    """
     Applies consistent formatting to a given worksheet, including title, headers,
    and general aesthetics.
    """
    title = SHEET_TITLES.get(position_name, f"{position_name} Statistics")
    
    headers = SHEET_HEADERS.get(position_name, [])
    if not headers:
        logging.warning(f"No headers defined for position {position_name}. Skipping formatting.")
        return

    last_col_letter = col_index_to_letter(len(headers) - 1)
    
    # Update title in A1 and merge cells for the title row
    ws.update_cell(1, 1, title)
    ws.merge_cells(f"A1:{last_col_letter}1")
    
    # Format the title row (row 1)
    ws.format(f"A1:{last_col_letter}1", {
        "backgroundColor": position_color,
        "textFormat": {
            "foregroundColor": {"red": 1.0, "green": 1.0, "blue": 1.0}, # White text
            "bold": True,
            "fontSize": 14
        },
        "horizontalAlignment": "CENTER",
        "verticalAlignment": "MIDDLE"
    })

    # Format the header row (row 2)
    ws.format(f"A2:{last_col_letter}2", {
        "backgroundColor": {"red": 0.85, "green": 0.85, "blue": 0.85}, # Light gray background for headers
        "textFormat": {
            "bold": True
        },
        "horizontalAlignment": "CENTER"
    })

    # Set general text format for data rows (from row 3 onwards)
    ws.format(f"A3:{last_col_letter}1000", { # Apply to a large range to cover potential data
        "backgroundColor": {"red": 0.98, "green": 0.98, "blue": 0.98}, # Very light gray for data rows
        "textFormat": {
            "foregroundColor": {"red": 0.1, "green": 0.1, "blue": 0.1}, # Dark gray text
            "fontSize": 10
        },
        "horizontalAlignment": "LEFT",
        "verticalAlignment": "MIDDLE"
    })
    
    # Adjust column widths for better readability
    requests = []
    for i, header in enumerate(headers):
        pixel_size = 120 # Default width
        if header.lower() in ["username", "team"]:
            pixel_size = 150
        elif len(header) <= 3: # Shorter headers like C%, TDs
            pixel_size = 70
        
        requests.append({
            "updateDimensionProperties": {
                "range": {
                    "sheetId": ws.id,
                    "dimension": "COLUMNS",
                    "startIndex": i,
                    "endIndex": i + 1
                },
                "properties": {
                    "pixelSize": pixel_size
                },
                "fields": "pixelSize"
            }
        })
    
    if requests:
        try:
            ws.spreadsheet.batch_update({"requests": requests})
        except Exception as e:
            logging.error(f"Error applying column width formatting to sheet {ws.title}: {e}")

def get_or_create_worksheet(sheet: gspread.Spreadsheet, name: str, headers: list) -> tuple[gspread.Worksheet, bool]:
    """
    Retrieves an existing worksheet or creates a new one with specified headers
    and initial structure (title row, header row).
    Returns the worksheet and a boolean indicating if it was newly created.
    """
    is_newly_created = False
    try:
        ws = sheet.worksheet(name)
        # Check if the structure matches expectations (title in row 1, headers in row 2)
        current_title = ws.cell(1, 1).value
        current_headers = ws.row_values(2)
        
        expected_title = SHEET_TITLES.get(name, f"{name} Statistics")

        if current_title != expected_title or current_headers != headers:
            logging.warning(f"Sheet '{name}' structure does not match expected template. Attempting to fix.")
            # Clear existing content from row 1 and 2 and update
            ws.batch_update([
                {'range': 'A1:Z2', 'values': [[''] * len(headers), [''] * len(headers)]} # Clear top two rows
            ])
            ws.update(f"A2", [headers]) # Set headers in row 2
            is_newly_created = True # Treat as newly set up for formatting
            
    except gspread.exceptions.WorksheetNotFound:
        ws = sheet.add_worksheet(title=name, rows="100", cols=str(len(headers)))
        ws.update(f"A2", [headers]) # Set headers in row 2
        is_newly_created = True
        
    return ws, is_newly_created

def prepare_update_requests(
    ws: gspread.Worksheet, 
    username: str, 
    values_dict: dict, 
    all_sheet_values: list[list[str]], # Pass all sheet values to avoid re-fetching
    position_specific_update_logic: dict = None
) -> tuple[list[dict], dict]:
    """
    Prepares gspread batch update requests for a single player.
    Returns a tuple: (list of gspread update requests, dictionary of deltas).
    """
    header = all_sheet_values[1] if len(all_sheet_values) > 1 else [] # Headers are always in row 2 (index 1)
    if not header:
        logging.error(f"Cannot prepare update requests: Headers not found in sheet '{ws.title}'.")
        return [], {}

    username_col_index = -1
    team_col_index = -1
    for i, h in enumerate(header):
        if h.lower() == 'username':
            username_col_index = i
        if h.lower() == 'team':
            team_col_index = i
    
    if username_col_index == -1:
        logging.error(f"Cannot prepare update requests: 'username' column not found in headers for sheet '{ws.title}'.")
        return [], {}

    data_rows = all_sheet_values[2:] if len(all_sheet_values) > 2 else [] # Actual data starts from row 3 (index 2)

    row_index_in_sheet = None # This will be the 1-based row index in the sheet
    current_row_values = None
    for i, row in enumerate(data_rows):
        if len(row) > username_col_index and row[username_col_index] == username:
            row_index_in_sheet = i + 3 # +1 for 0-based list index, +2 for title/header rows
            current_row_values = row[:] # Make a copy to modify
            break
            
    deltas = {}
    requests = []

    if row_index_in_sheet:
        # Update existing row
        new_row_values = current_row_values[:]

        for key, val in values_dict.items():
            col_idx_for_key = -1
            if key.lower() == 'team':
                col_idx_for_key = team_col_index
            elif key in header:
                col_idx_for_key = header.index(key)

            if col_idx_for_key != -1 and val is not None:
                current_val_str = current_row_values[col_idx_for_key] if col_idx_for_key < len(current_row_values) else ''

                if position_specific_update_logic and key in position_specific_update_logic:
                    old_val_processed = float(current_val_str.replace('%', '')) if isinstance(current_val_str, str) and current_val_str.replace('%', '').replace('.', '', 1).isdigit() else 0.0
                    new_computed_val = position_specific_update_logic[key](current_val_str, val)
                    new_row_values[col_idx_for_key] = str(new_computed_val)
                    deltas[key] = new_computed_val - old_val_processed
                else:
                    try:
                        if isinstance(current_val_str, str) and current_val_str.endswith('%'):
                            current_val_str = current_val_str[:-1]
                        
                        current_val_num = float(current_val_str) if current_val_str else 0.0
                        delta_num = float(str(val).replace('%', ''))
                        new_val_computed = current_val_num + delta_num
                        new_row_values[col_idx_for_key] = str(new_val_computed)
                        deltas[key] = delta_num
                    except (ValueError, IndexError):
                        new_row_values[col_idx_for_key] = str(val)
                        old_val_for_delta = current_val_str if current_val_str else ''
                        if old_val_for_delta != str(val):
                            deltas[key] = str(val)
                        else:
                            deltas[key] = 0

        if len(new_row_values) < len(header):
            new_row_values.extend([""] * (len(header) - len(new_row_values)))
        elif len(new_row_values) > len(header):
            new_row_values = new_row_values[:len(header)]

        requests.append({
            'range': f"A{row_index_in_sheet}",
            'values': [new_row_values]
        })

    else:
        # Insert new row
        new_row_data = [""] * len(header)

        if team_col_index != -1:
            new_row_data[team_col_index] = values_dict.get('Team') if values_dict.get('Team') else ""
        if username_col_index != -1:
            new_row_data[username_col_index] = username

        for col_name, value in values_dict.items():
            col_idx_for_key = -1
            if col_name.lower() == 'team':
                col_idx_for_key = team_col_index
            elif col_name in header:
                col_idx_for_key = header.index(col_name)

            if col_idx_for_key != -1 and value is not None:
                new_row_data[col_idx_for_key] = str(value)
                try:
                    deltas[col_name] = float(str(value).replace('%', ''))
                except ValueError:
                    deltas[col_name] = str(value)
            
        # For new rows, we append. gspread's batch_update doesn't have a direct 'append' method
        # for multiple rows. The most efficient way is to get the next available row and update.
        # However, if we're doing many appends, it's better to collect all new rows and append them once.
        # For now, we'll return a special 'append' instruction.
        requests.append({
            'type': 'append_row', # Custom type to indicate append
            'values': new_row_data
        })
    return requests, deltas

# The `is_emoji` function is no longer strictly needed if `team_logo` is removed,
# but keeping it in case it's used elsewhere or for future features.
def is_emoji(s: str) -> bool:
    """Checks if a string contains at least one emoji."""
    emoji_pattern = re.compile(
        "["
        "\U0001F600-\U0001F64F"    # emoticons
        "\U0001F300-\U0001F5FF"    # symbols & pictographs
        "\U0001F680-\U0001F6FF"    # transport & map symbols
        "\U0001F1E0-\U0001F1FF"    # flags (iOS)
        "\*********-\U000027B0"
        "\U000024C2-\U0001F251"
        "]+", flags=re.UNICODE
    )
    return bool(emoji_pattern.search(s))

# --- CSV Parsing Logic (Improved) ---

def parse_csv_content(csv_string: str) -> list:
    """
    Parses CSV content, handling multiple sections (Passing, Receiving, etc.)
    and dynamic headers. Handles header normalization and special cases.
    Returns a list of dictionaries, where each dictionary represents a player's stats.
    """
    import re
    player_data = []
    f = io.StringIO(csv_string)
    reader = csv.reader(f)

    current_headers = None
    current_position = None

    # Section headers mapping to positions
    section_keywords = {
        "Passing": "QB",
        "Rushing": "RB",
        "Receiving": "WR",
        "Cornerback": "DB",
        "DLine": "DE",
        "Linebacker": "LB",
        "SpecialTeams": "Kicker",
    }

    # Header normalization for all positions
    header_variations = {
        # Common and position specific
        "username": "username",
        "player name": "username",

        # QB (Passing)
        "completions": "Completions",
        "attempts": "Attempts",
        "passing yards": "Passing Yards",
        "touchdowns": "Touchdowns",
        "interceptions": "Interceptions",
        "fumbles": "Fumbles",
        "interception tds": "Interception TDs",
        "sacks taken": "Sacks Taken",
        "3rd down conv.": "3rd Down Conv.",
        "4th down conv.": "4th Down Conv.",
        "longest throw": "Longest Throw",
        "<15 yd atts": "<15 YD ATTs",
        "<15 yd cmps": "<15 YD CMPs",
        "<35 yd atts": "<35 YD ATTs",
        "<35 yd cmps": "<35 YD CMPs",
        "<50 yd atts": "<50 YD ATTs",
        "<50 yd cmps": "<50 YD CMPs",
        ">50 yd atts": ">50 YD ATTs",
        ">50 yd cmps": ">50 YD CMPs",
        "snaps": "Snaps",

        # RB (Rushing)
        "rushing attempts": "Rushing Attempts",
        "rushing yards": "Rushing Yards",
        "average yards": "Average Yards",
        "carries over 20": "Carries Over 20",
        "fumbles": "Fumbles",
        "rushing first downs": "Rushing First Downs",
        "longest rush": "Longest Rush",

        # WR (Receiving)
        "receptionss": "Catch",
        "receptions": "Catch",
        "targets": "Targets",
        "receiving yards": "Receiving Yards",
        "yards after catch": "Yards After Catch",
        "receiving first downs": "Receiving First Downs",
        "longest reception": "Long",
        "catches in traffic": "Catches In Traffic",
        "drops": "Drops",
        "interceptions allowed": "Int allowd",
        "int allowd": "Int allowd",
        "toss ups": "Toss Ups",
        "3rd down conv.": "3rd Down Conv.",
        "4th down conv.": "4th Down Conv.",
        "<15 yd recs": "<15 YD RECs",
        "<35 yd recs": "<35 YD RECs",
        "<50 yd recs": "<50 YD RECs",
        ">50 yd recs": ">50 YD RECs",

        # DB (Cornerback)
        "tackles": "Tackles",
        "tackles for loss": "Tackles For Loss",
        "tackles for loss yards": "Tackles For Loss Yards",
        "pass break ups": "Pass Break Ups",
        "interceptions": "Interceptions",
        "interception tds": "Interception TDs",
        "ints in traffic": "Ints In Traffic",
        "interception yards": "Interception Yards",
        "longest interception return": "Longest Interception Return",
        "completions allowed": "CmpAll",
        "cmpall": "CmpAll",
        "passing yards allowed": "Passing Yards Allowed",
        "passing touchdowns allowed": "Passing Touchdowns Allowed",

        # DE (DLine)
        "rushes": "Rushes",
        "sacks": "Sacks",
        "sack loss yards": "Sack Loss Yards",
        "quarterback hits": "Quarterback Hits",
        "swats": "Swats",
        "qb pressures": "QB Pressures",
        "safeties": "Safeties",
        "fumbles forced": "Fumbles Forced",
        "fumbles recovered": "Fumbles Recovered",
        "fumbles yards": "Fumbles Yards",
        "fumble touchdowns": "Fumble Touchdowns",

        # Kicker (SpecialTeams)
        "field goals": "Field Goals",
        "field goal percent": "Field Goal Percent",
        "field goal yards": "Field Goal Yards",
        "longest field goal": "Longest Field Goal",
        "kick returns": "Kick Returns",
        "kick return yards": "Kick Return Yards",
        "kick return average": "Kick Return Average",
        "kick return touchdowns": "Kick Return Touchdowns",
        "punt returns": "Punt Returns",
        "punt return yards": "Punt Return Yards",
        "punt return average": "Punt Return Average",
        "punt return touchdowns": "Punt Return Touchdowns",
        "punt blocks": "Punt Blocks",
        "kicks": "Kicks",
        "kick touchbacks": "Kick Touchbacks",
        "punts": "Punts",
        "punt yards": "Punt Yards",
        "punts in 10yd line": "Punts In 10YD Line",
        "longest punt": "Longest Punt",
        "onside recoveries": "Onside Recoveries",
        "onside recovery yards": "Onside Recovery Yards",
        # generic
        "snaps": "Snaps",
    }

    for row_idx, row in enumerate(reader):
        # Remove possible BOM on first header row
        if row and row[0].startswith("\ufeff"):
            row[0] = row[0].replace("\ufeff", "")
        # Strip whitespace
        cleaned_row = [cell.strip() for cell in row]
        non_empty_cells = [cell for cell in cleaned_row if cell != '']

        if not non_empty_cells:
            continue

        first_cell_lower = non_empty_cells[0].lower()
        found_section = False
        for keyword, pos_name in section_keywords.items():
            if first_cell_lower == keyword.lower():
                current_position = pos_name
                current_headers = None
                found_section = True
                break
        if found_section:
            continue

        # If new headers line
        if current_position and not current_headers:
            # Remove surrounding quotes on headers and normalize
            mapped_headers = []
            for csv_header in cleaned_row:
                h = csv_header.replace('"', '').strip().lower()
                mapped = header_variations.get(h, csv_header.replace('"', '').strip())
                mapped_headers.append(mapped)
            current_headers = mapped_headers
            continue

        # Data row
        if current_position and current_headers:
            # Pad/trim row to header length
            if len(cleaned_row) < len(current_headers):
                cleaned_row.extend([''] * (len(current_headers) - len(cleaned_row)))
            elif len(cleaned_row) > len(current_headers):
                cleaned_row = cleaned_row[:len(current_headers)]

            player_dict = {}
            for i, header_key in enumerate(current_headers):
                player_dict[header_key] = cleaned_row[i] if i < len(cleaned_row) else ""
            player_name = player_dict.get("username") or player_dict.get("Username")
            if not player_name:
                continue

            stats_to_update = {}
            team_name = player_dict.get("Team", "")

            # --- Position-specific field extraction and calculation ---
            if current_position == "QB":
                stats_to_update = {
                    "Passes PTs": 0,
                    "YDs": int(player_dict.get("Passing Yards", 0) or 0),
                    "TDs": int(player_dict.get("Touchdowns", 0) or 0),
                    "INT": int(player_dict.get("Interceptions", 0) or 0),
                    "SCK": int(player_dict.get("Sacks Taken", 0) or 0),
                    "3RD": int(player_dict.get("3rd Down Conv.", 0) or 0),
                    "4TH": int(player_dict.get("4th Down Conv.", 0) or 0),
                    "SNAP": int(player_dict.get("Snaps", 0) or 0)
                }
                completions = int(player_dict.get("Completions", 0) or 0)
                attempts = int(player_dict.get("Attempts", 0) or 0)
                stats_to_update["C%"] = f"{round((completions/attempts)*100, 2) if attempts>0 else 0.0}%"

            elif current_position == "RB":
                att = int(float(player_dict.get("Rushing Attempts", 0) or 0))
                yards = int(float(player_dict.get("Rushing Yards", 0) or 0))
                tds = int(float(player_dict.get("Touchdowns", 0) or 0))
                ypa = float(player_dict.get("Average Yards", 0) or 0)
                stats_to_update = {
                    "ATT": att,
                    "Yards": yards,
                    "TDS": tds,
                    "Y/A": ypa
                }

            elif current_position == "WR":
                catch = int(float(player_dict.get("Catch", player_dict.get("Receptionss", 0)) or 0))
                target = int(float(player_dict.get("Targets", 0) or 0))
                tds = int(float(player_dict.get("Touchdowns", 0) or 0))
                yards = int(float(player_dict.get("Receiving Yards", 0) or 0))
                int_allowd = int(float(player_dict.get("Int allowd", player_dict.get("Interceptions Allowed", 0)) or 0))
                snap = int(float(player_dict.get("Snaps", 0) or 0))
                stats_to_update = {
                    "Catch": catch,
                    "Target": target,
                    "TDs": tds,
                    "Yards": yards,
                    "Int allowd": int_allowd,
                    "SNAP": snap,
                }
                stats_to_update["%"] = f"{round((catch/target)*100,2) if target>0 else 0.0}%"

            elif current_position == "DB":
                ints = int(float(player_dict.get("Interceptions", 0) or 0))
                tackles = int(float(player_dict.get("Tackles", 0) or 0))
                cmpall = int(float(player_dict.get("CmpAll", player_dict.get("Completions Allowed", 0)) or 0))
                targets = int(float(player_dict.get("Targets", 0) or 0))
                yda = int(float(player_dict.get("Passing Yards Allowed", 0) or 0))
                tda = int(float(player_dict.get("Passing Touchdowns Allowed", 0) or 0))
                snaps = int(float(player_dict.get("Snaps", 0) or 0))
                stats_to_update = {
                    "Ints": ints,
                    "Tackles": tackles,
                    "CmpAll": cmpall,
                    "Targets": targets,
                    "YDA": yda,
                    "TDA": tda,
                    "SNAPS": snaps,
                }
                stats_to_update["%"] = f"{round((cmpall/targets)*100,2) if targets>0 else 0.0}%"

            elif current_position == "DE":
                sacks = int(float(player_dict.get("Sacks", 0) or 0))
                pressures = int(float(player_dict.get("QB Pressures", 0) or 0))
                qbh = int(float(player_dict.get("Quarterback Hits", 0) or 0))
                tackles = int(float(player_dict.get("Tackles", 0) or 0))
                safeties = int(float(player_dict.get("Safeties", 0) or 0))
                snap = int(float(player_dict.get("Snaps", 0) or 0))
                stats_to_update = {
                    "Sacks": sacks,
                    "Pressures": pressures,
                    "QBH": qbh,
                    "Tackles": tackles,
                    "Safeties": safeties,
                    "snap": snap,
                }

            elif current_position == "Kicker":
                # Parse "Field Goals" (e.g. 1/1)
                fg_str = player_dict.get("Field Goals", "0/0")
                completed, att = 0, 0
                match = re.match(r"(\d+)\s*/\s*(\d+)", fg_str)
                if match:
                    completed, att = int(match.group(1)), int(match.group(2))
                fg_pct_str = player_dict.get("Field Goal Percent", "0.0%").replace("%", "")
                fg_pct = float(fg_pct_str) if fg_pct_str else 0.0
                long_fg = int(float(player_dict.get("Longest Field Goal", 0) or 0))
                stats_to_update = {
                    "Completed": completed,
                    "ATT": att,
                    "FG%": f"{fg_pct}%",
                    "Long": long_fg,
                }

            else:
                # Fallback - just copy all numeric fields except username and team
                for k, v in player_dict.items():
                    if k.lower() not in ["username", "team"] and v.strip() != "":
                        try:
                            stats_to_update[k] = int(float(v))
                        except Exception:
                            stats_to_update[k] = v

            player_data.append({
                "username": player_name,
                "position": current_position,
                "team": team_name,
                "stats": stats_to_update
            })
    return player_data





@bot.event
async def on_ready() -> None:
    """Event that fires when the bot is ready and connected to Discord."""
    logging.info(f'Logged in as {bot.user} (ID: {bot.user.id})')
    # Load connected sheets when the bot starts
    await load_connected_sheets()
    # Sync commands to make them appear as slash commands
    try:
        synced = await bot.tree.sync()
        logging.info(f"Synced {len(synced)} command(s)")
    except Exception as e:
        logging.error(f"Error syncing commands: {e}")

@bot.event
async def on_message(message: discord.Message) -> None:
    """Event that fires when a message is sent in a channel the bot can see."""
    # Ignore messages from bots themselves to prevent infinite loops
    if message.author.bot:
        return

    # Check if the channel is configured for CSV listening
    guild_id = message.guild.id if message.guild else None
    if guild_id and guild_id in listening_channels and message.channel.id in listening_channels[guild_id]:
        for attachment in message.attachments:
            if attachment.filename.lower().endswith('.csv'):
                logging.info(f"Detected CSV attachment in listening channel {message.channel.name} ({message.channel.id}) from {message.author.display_name}.")
                try:
                    csv_content = await attachment.read()
                    csv_string = csv_content.decode('utf-8')
                    
                    await process_csv_data(message.channel, message.guild.id, message.author, csv_string)
                    
                except Exception as e:
                    logging.error(f"Error processing CSV from on_message: {e}", exc_info=True)
                    await message.channel.send(f"❌ An error occurred while processing the CSV: {e}")
                return # Only process the first CSV attachment per message

    # Allow other commands to be processed
    await bot.process_commands(message)

# Helper function to process CSV data (used by both slash command and on_message)
async def process_csv_data(channel: discord.abc.Messageable, guild_id: int, author: discord.Member, csv_string: str):
    if not gc:
        await channel.send("❌ Google Sheets client is not initialized. Cannot process CSV. Please check bot's authentication setup.")
        return

    sheet = connected_sheets.get(guild_id)
    if not sheet:
        await channel.send("⚠️ No Google Sheet connected to this server. Use `/connect_sheet` first.")
        return

    try:
        processed_players = []
        transaction_id = str(uuid.uuid4())
        
        parsed_data = parse_csv_content(csv_string)

        if not parsed_data:
            await channel.send("ℹ️ No valid player data found in the CSV to process.")
            return

        # Caches for worksheets and their current data to reduce API calls
        worksheet_cache = {} # {position_name: gspread.Worksheet}
        sheet_data_cache = {} # {position_name: list[list[str]]}
        
        # Collect all update requests before sending them
        # {worksheet_id: {'updates': [{'range': 'A1', 'values': [[val]]}, ...], 'appends': [[row_data], ...], 'worksheet': gspread.Worksheet}}
        batch_requests_by_worksheet = {} 

        for player_entry in parsed_data:
            player_name = player_entry["username"]
            position = player_entry["position"]
            team_name = player_entry["team"]
            stats_to_update = player_entry["stats"]

            if position not in SHEET_HEADERS:
                logging.warning(f"Skipping player {player_name}: Unknown position '{position}'.")
                continue
            
            headers_for_position = SHEET_HEADERS[position]
            
            # Get worksheet from cache or create/fetch it
            if position not in worksheet_cache:
                ws, _ = get_or_create_worksheet(sheet, position, headers_for_position)
                worksheet_cache[position] = ws
                # Fetch all data for this worksheet once
                sheet_data_cache[position] = ws.get_all_values()
            else:
                ws = worksheet_cache[position]

            # Custom logic for 'Long' stat (Kicker)
            position_specific_logic = {}
            if position == "Kicker" and "Long" in stats_to_update:
                position_specific_logic["Long"] = lambda current_val_str, new_val: max(float(current_val_str or 0), float(new_val))

            try:
                # Prepare update requests using the cached sheet data
                update_requests, deltas = prepare_update_requests(
                    ws, player_name, 
                    {"Team": team_name, **stats_to_update}, # Include team in values_dict for prepare_update_requests
                    sheet_data_cache[position], 
                    position_specific_update_logic=position_specific_logic
                )
                
                if ws.id not in batch_requests_by_worksheet:
                    batch_requests_by_worksheet[ws.id] = {'updates': [], 'appends': [], 'worksheet': ws}

                for req in update_requests:
                    if req.get('type') == 'append_row':
                        batch_requests_by_worksheet[ws.id]['appends'].append(req['values'])
                        # Update local cache for subsequent players in the same CSV
                        sheet_data_cache[position].append(req['values']) 
                    else:
                        batch_requests_by_worksheet[ws.id]['updates'].append(req)
                        # Also update the local cache for the specific cell
                        # This is a simplified update; for complex scenarios, a more robust in-memory sheet representation might be needed
                        row_idx = int(re.search(r'\d+', req['range']).group()) - 1 # Convert A1 notation row to 0-based index
                        col_letter = re.match(r'[A-Z]+', req['range']).group()
                        col_idx = gspread.utils.a1_to_rowcol(col_letter + '1')[1] - 1 # Convert column letter to 0-based index
                        
                        # Ensure row_idx exists in cache, expand if necessary (for new rows)
                        while len(sheet_data_cache[position]) <= row_idx:
                            sheet_data_cache[position].append([''] * len(sheet_data_cache[position][1])) # Assuming headers are at index 1

                        # Ensure column exists in the specific row, expand if necessary
                        while len(sheet_data_cache[position][row_idx]) <= col_idx:
                            sheet_data_cache[position][row_idx].append('')

                        sheet_data_cache[position][row_idx][col_idx] = req['values'][0][0] # Update the specific cell in cache


                processed_players.append({
                    "username": player_name,
                    "position": position,
                    "team": team_name,
                    "deltas": deltas # Store the deltas for this player's stats
                })
            except Exception as e:
                logging.error(f"Error preparing update for player {player_name} for position {position}: {e}", exc_info=True)
                await channel.send(f"❌ Error preparing update for {player_name} ({position}): {e}")
                continue

        # Execute all collected batch updates and appends
        for ws_id, data in batch_requests_by_worksheet.items():
            ws = data['worksheet']
            if data['updates']:
                try:
                    ws.batch_update(data['updates'])
                    logging.info(f"Executed batch update for {len(data['updates'])} cells in sheet '{ws.title}'.")
                except Exception as e:
                    logging.error(f"Error executing batch update for sheet '{ws.title}': {e}", exc_info=True)
                    await channel.send(f"❌ Error executing batch update for sheet '{ws.title}': {e}")
            
            if data['appends']:
                try:
                    ws.append_rows(data['appends'])
                    logging.info(f"Executed batch append for {len(data['appends'])} rows in sheet '{ws.title}'.")
                except Exception as e:
                    logging.error(f"Error executing batch append for sheet '{ws.title}': {e}", exc_info=True)
                    await channel.send(f"❌ Error executing batch append for sheet '{ws.title}': {e}")


        if processed_players:
            # Log the entire transaction for reversal
            await log_transaction(sheet, transaction_id, author.id, author.display_name, processed_players)

            # Truncate summary_text if it's too long for Discord embed description limit (4096 characters)
            full_summary_lines = [f"- **{p['username']}** ({p['position']})" for p in processed_players]
            
            # Build the prefix and suffix for the embed description
            prefix = "Successfully updated stats for the following players:\n"
            suffix = f"\n\n**Transaction ID:** `{transaction_id}`"
            MAX_EMBED_DESC = 4096

            max_summary_length = MAX_EMBED_DESC - len(prefix) - len(suffix)
            summary_text = ""
            current_length = 0
            truncated_count = 0
            for line in full_summary_lines:
                line_len = len(line) + 1  # +1 for newline
                if current_length + line_len > max_summary_length:
                    break
                summary_text += line + "\n"
                current_length += line_len
                truncated_count += 1

            if truncated_count < len(full_summary_lines):
                summary_text += f"...and {len(full_summary_lines) - truncated_count} more players. View full details in the sheet.\n"

            embed_desc = f"{prefix}{summary_text}{suffix}"
            embed_desc = embed_desc[:MAX_EMBED_DESC]  # Always enforce the hard limit

            embed = discord.Embed(
                title="📊 CSV Stats Processed!",
                description=embed_desc,
                color=0x00ff00
            )
            embed.set_footer(text=f"Processed by {author.display_name} | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            await channel.send(embed=embed, view=StatActionView(transaction_id, sheet.url, summary_text))
        else:
            await channel.send("ℹ️ No valid player data found in the CSV to process after filtering.")

    except Exception as e:
        logging.error(f"Error processing CSV data: {e}", exc_info=True)
        await channel.send(f"❌ An error occurred while processing the CSV: {e}")


@bot.tree.command(name="connect_sheet", description="Connects a Google Sheet to this server.")
async def connect_sheet(interaction: discord.Interaction, sheet_link: str) -> None:
    """Connects a Google Sheet to the server using its link or ID."""
    if not gc:
        await interaction.response.send_message("❌ Google Sheets client is not initialized. Cannot connect sheet. Please check bot's authentication setup.", ephemeral=True)
        return

    try:
        sheet = gc.open_by_url(sheet_link) if "docs.google.com" in sheet_link else gc.open_by_key(sheet_link)
        connected_sheets[interaction.guild_id] = sheet
        save_connected_sheets() # Save the updated state
        await interaction.response.send_message("✅ Sheet connected successfully. Remember to give editor access to `<EMAIL>`", ephemeral=True)
    except Exception as e:
        logging.error(f"Failed to connect to sheet: {e}", exc_info=True)
        await interaction.response.send_message(f"❌ Failed to connect to sheet: {e}. Make sure the sheet link/ID is correct and the service account has editor access.", ephemeral=True)

@bot.tree.command(name="test_sheet_connection", description="Test the connected Google Sheet.")
async def test_sheet_connection(interaction: discord.Interaction) -> None:
    """Tests if the connected Google Sheet is accessible."""
    if not gc:
        await interaction.response.send_message("❌ Google Sheets client is not initialized. Cannot test sheet connection.", ephemeral=True)
        return

    sheet = connected_sheets.get(interaction.guild_id)
    if not sheet:
        await interaction.response.send_message("⚠️ No sheet connected to this server. Use `/connect_sheet` first.", ephemeral=True)
        return
    try:
        sheet.worksheets() # Try to list worksheets to confirm connection
        await interaction.response.send_message("✅ Sheet is accessible.", ephemeral=True)
    except Exception as e:
        logging.error(f"Sheet connection test failed: {e}", exc_info=True)
        await interaction.response.send_message(f"❌ Connection error: {e}. Please check the sheet permissions and ensure the link/ID is correct.", ephemeral=True)

@bot.tree.command(name="load_template", description="Loads all predefined sheet templates (QB, RB, WR, TE, etc.) into the connected Google Sheet.")
async def load_template(interaction: discord.Interaction) -> None:
    """
    Loads all predefined sheet templates (QB, RB, WR, TE, Center, DE, LB, Kicker, DB)
    into the connected Google Sheet, creating them if they don't exist and applying formatting.
    """
    await interaction.response.defer()
    if not gc:
        await interaction.followup.send("❌ Google Sheets client is not initialized. Cannot load templates. Please check bot's authentication setup.")
        return

    sheet = connected_sheets.get(interaction.guild_id)
    if not sheet:
        await interaction.followup.send("⚠️ No Google Sheet connected. Use `/connect_sheet` first.")
        return

    created_sheets = []
    failed_sheets = []

    for position, headers in SHEET_HEADERS.items():
        try:
            ws, _ = get_or_create_worksheet(sheet, position, headers)
            apply_sheet_formatting(ws, position, POSITION_COLORS.get(position))
            created_sheets.append(position)
        except Exception as e:
            failed_sheets.append(f"{position} ({e})")
            logging.error(f"Error loading template for {position}: {e}", exc_info=True)
            
    response_msg = f"✅ Attempted to load templates.\nSuccessfully created/checked and formatted sheets: {', '.join(created_sheets) if created_sheets else 'None'}.\n"
    if failed_sheets:
        response_msg += f"❌ Failed to create/check sheets: {', '.join(failed_sheets)}."
    
    await interaction.followup.send(response_msg)

@bot.tree.command(name="help", description="View available commands.")
async def help_cmd(interaction: discord.Interaction) -> None:
    """Displays a list of available commands and their descriptions."""
    embed = discord.Embed(title="📊 Stats Bot Help", color=0x00ff00)
    commands_info = [
        ("/connect_sheet", "Connect a Google Sheet by link or ID. **The connection will persist across bot restarts.** Make sure to give editor access to `<EMAIL>`"),
        ("/test_sheet_connection", "Test if the connected sheet is working."),
        ("/load_template", "Loads all predefined sheet templates (QB, RB, WR, TE, etc.) into the connected Google Sheet and applies beautiful formatting."),
        ("/process_csv_attachment", "Upload a CSV file to update player stats in the connected Google Sheet. Provides options to reverse the changes."),
        ("/enable_csv_listening", "Enable the bot to automatically process CSV files uploaded to a specific channel."),
        ("/disable_csv_listening", "Disable the bot from automatically processing CSV files in a specific channel."),
        ("/list_listening_channels", "List channels where the bot is currently listening for CSV uploads."),
        ("/diagnose_csv", "Analyzes an uploaded CSV file and suggests appropriate stat insertion commands for each player."),
        ("/insert_qb_stats", "Insert or update stats for a Quarterback."),
        ("/insert_rb_stats", "Insert or update stats for a Running Back."),
        ("/insert_wr_stats", "Insert or update stats for a Wide Receiver."),
        ("/insert_te_stats", "Insert or update stats for a Tight End."),
        ("/insert_center_stats", "Insert or update stats for a Center."),
        ("/insert_de_stats", "Insert or update stats for a Defensive End (Defensive Lineman)."),
        ("/insert_lb_stats", "Insert or update stats for a Linebacker."),
        ("/insert_kicker_stats", "Insert or update stats for a Kicker."),
        ("/insert_db_stats", "Insert or update stats for a Defensive Back."),
        ("/update_leaderboard_colors", "Updates the sheet with primary colors for top players in a specific position.")
    ]
    for name, desc in commands_info:
        embed.add_field(name=name, value=desc, inline=False)
    await interaction.response.send_message(embed=embed, ephemeral=True)

async def insert_stats_template(
    interaction: discord.Interaction, 
    position_name: str, 
    username: str, 
    team_name: str, 
    headers: list, 
    values: dict, 
    position_specific_update_logic: dict = None
) -> None:
    """
    A template function to handle inserting/updating stats for different positions.
    Handles 'Team' column, calculates percentages, and manages 'Long' stats.
    """
    await interaction.response.defer()
    if not gc:
        await interaction.followup.send("❌ Google Sheets client is not initialized. Cannot insert stats. Please check bot's authentication setup.")
        return

    sheet = connected_sheets.get(interaction.guild_id)
    if not sheet:
        await interaction.followup.send("⚠️ No Google Sheet connected. Use `/connect_sheet` first.")
        return
    
    try:
        ws, _ = get_or_create_worksheet(sheet, position_name, headers) 
        
        # Fetch all values once for this single player update
        all_sheet_values = ws.get_all_values()

        data_to_send = {**values}
        team_header_key = "Team" if "Team" in headers else ("team" if "team" in headers else None)
        if team_header_key:
            data_to_send[team_header_key] = team_name

        update_requests, deltas = prepare_update_requests(
            ws, username, data_to_send, all_sheet_values, position_specific_update_logic
        )

        # Execute updates/appends for this single player
        if update_requests:
            if update_requests[0].get('type') == 'append_row':
                await ws.append_row(update_requests[0]['values'])
            else:
                await ws.batch_update(update_requests)

        # Log this single stat insertion as a transaction for potential reversal
        processed_player_summary = [{
            "username": username,
            "position": position_name,
            "team": team_name,
            "deltas": deltas
        }]
        await log_transaction(sheet, str(uuid.uuid4()), interaction.user.id, interaction.user.display_name, processed_player_summary)


        team_display = f" ({team_name})" if team_name else " 🏈"
        await interaction.followup.send(f"✅ {position_name} stats for **{username}**{team_display} updated.")
    except Exception as e:
        logging.error(f"Error inserting stats for {position_name} - {username}: {e}", exc_info=True)
        await interaction.followup.send(f"❌ Failed to update {position_name} stats for **{username}**: {e}")

@bot.tree.command(name="insert_qb_stats", description="Insert stats for QB")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    passes_pts="Passing Points",
    yds="Yards",
    completed_passes="Completed Passes",
    pass_attempts="Total Pass Attempts",
    tds="Touchdowns",
    interceptions="Interceptions",
    sacks="Sacks",
    third_down_conversions="3rd Down Conversions",
    fourth_down_conversions="4th Down Conversions",
    snap="Snaps"
)
async def insert_qb_stats(
    interaction: discord.Interaction,
    username: str,
    team_name: str = "",
    passes_pts: int = 0,
    yds: int = 0,
    completed_passes: int = 0,
    pass_attempts: int = 0,
    tds: int = 0,
    interceptions: int = 0,
    sacks: int = 0,
    third_down_conversions: int = 0,
    fourth_down_conversions: int = 0,
    snap: int = 0
) -> None:
    """Inserts or updates Quarterback stats."""
    headers = SHEET_HEADERS["QB"]
    
    comp_pct = round((completed_passes / pass_attempts) * 100, 2) if pass_attempts else 0.0

    values = {
        "Passes PTs": passes_pts, "YDs": yds, "C%": f"{comp_pct}%", "TDs": tds,
        "INT": interceptions, "SCK": sacks, "3RD": third_down_conversions, 
        "4TH": fourth_down_conversions, "SNAP": snap
    }
    await insert_stats_template(interaction, "QB", username, team_name, headers, values)

@bot.tree.command(name="insert_rb_stats", description="Insert stats for RB")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    att="Rushing Attempts",
    yards="Rushing Yards",
    tds="Touchdowns"
)
async def insert_rb_stats(interaction: discord.Interaction, username: str, team_name: str = "", att: int = 0, yards: int = 0, tds: int = 0) -> None:
    """Inserts or updates Running Back stats."""
    headers = SHEET_HEADERS["RB"]
    
    yards_per_att = round(yards / att, 2) if att else 0.0

    values = {
        "ATT": att, "Yards": yards, "TDS": tds, "Y/A": yards_per_att
    }
    await insert_stats_template(interaction, "RB", username, team_name, headers, values)

@bot.tree.command(name="insert_wr_stats", description="Insert stats for WR")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    yards="Receiving Yards",
    tds="Receiving Touchdowns",
    catch="Receptions",
    target="Targets",
    int_allowed="Interceptions Allowed (for coverage/blocking WRs)",
    snap="Snaps"
)
async def insert_wr_stats(interaction: discord.Interaction, username: str, team_name: str = "", yards: int = 0, tds: int = 0, catch: int = 0, target: int = 0, int_allowed: int = 0, snap: int = 0) -> None:
    """Inserts or updates Wide Receiver stats."""
    headers = SHEET_HEADERS["WR"]
    
    catch_pct = round((catch / target) * 100, 2) if target else 0.0

    values = {
        "Yards": yards, "TDs": tds, "Catch": catch, "Target": target, 
        "%": f"{catch_pct}%", "Int allowd": int_allowed, "SNAP": snap
    }
    await insert_stats_template(interaction, "WR", username, team_name, headers, values)

@bot.tree.command(name="insert_te_stats", description="Insert stats for TE")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    yards="Receiving Yards",
    yac="Yards After Catch",
    tds="Touchdowns",
    catch="Receptions",
    targets="Targets",
    sacks="Sacks (allowed)",
    snap="Snaps"
)
async def insert_te_stats(interaction: discord.Interaction, username: str, team_name: str = "", yards: int = 0, yac: int = 0, tds: int = 0, catch: int = 0, targets: int = 0, sacks: int = 0, snap: int = 0) -> None:
    """Inserts or updates Tight End stats."""
    headers = SHEET_HEADERS["TE"]
    
    catch_pct = round((catch / targets) * 100, 2) if targets else 0.0

    values = {
        "yards": yards, "yac": yac, "Tds": tds, "%": f"{catch_pct}%",
        "Catch": catch, "targets": targets, "sacks": sacks, "snap": snap
    }
    await insert_stats_template(interaction, "TE", username, team_name, headers, values)

@bot.tree.command(name="insert_center_stats", description="Insert stats for Center")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    snaps="Snaps Played",
    sacks="Sacks Allowed",
    pressures="Pressures Allowed",
    qbh="QB Hits Allowed"
)
async def insert_center_stats(interaction: discord.Interaction, username: str, team_name: str = "", snaps: int = 0, sacks: int = 0, pressures: int = 0, qbh: int = 0) -> None:
    """Inserts or updates Center stats."""
    headers = SHEET_HEADERS["Center"]
    
    sps = round(sacks / snaps, 2) if snaps else 0.0

    values = {
        "snaps": snaps, "sacks": sacks, "pressures": pressures, "qbh": qbh, "SPS": sps
    }
    await insert_stats_template(interaction, "Center", username, team_name, headers, values)

@bot.tree.command(name="insert_de_stats", description="Insert stats for DE (Defensive Lineman)")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    sacks="Sacks",
    pressures="Pressures",
    qbh="QB Hits",
    tackles="Tackles",
    safeties="Safeties",
    snap="Snaps"
)
async def insert_de_stats(interaction: discord.Interaction, username: str, team_name: str = "", sacks: int = 0, pressures: int = 0, qbh: int = 0, tackles: int = 0, safeties: int = 0, snap: int = 0) -> None:
    """Inserts or updates Defensive End (Defensive Lineman) stats."""
    headers = SHEET_HEADERS["DE"]
    values = {
        "Sacks": sacks, "Pressures": pressures, "QBH": qbh, "Tackles": tackles,
        "Safeties": safeties, "snap": snap
    }
    await insert_stats_template(interaction, "DE", username, team_name, headers, values)

@bot.tree.command(name="insert_lb_stats", description="Insert stats for LB")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    ints="Interceptions",
    tackles="Tackles",
    targets="Targets (against in coverage)",
    cmp_all="Completions Allowed",
    yda="Yards Allowed",
    tda="Touchdowns Allowed",
    snap="Snaps"
)
async def insert_lb_stats(interaction: discord.Interaction, username: str, team_name: str = "", ints: int = 0, tackles: int = 0, targets: int = 0, cmp_all: int = 0, yda: int = 0, tda: int = 0, snap: int = 0) -> None:
    """Inserts or updates Linebacker stats."""
    headers = SHEET_HEADERS["LB"]
    
    cmp_pct = round((cmp_all / targets) * 100, 2) if targets else 0.0

    values = {
        "ints": ints, "tackles": tackles, "Targets": targets, "CmpAll": cmp_all,
        "cmp%": f"{cmp_pct}%", "YDA": yda, "TDA": tda, "SNAP": snap
    }
    await insert_stats_template(interaction, "LB", username, team_name, headers, values)

@bot.tree.command(name="insert_kicker_stats", description="Insert stats for Kicker")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    completed_fg="Field Goals Made",
    att_fg="Field Goal Attempts",
    long_fg="Longest Field Goal"
)
async def insert_kicker_stats(interaction: discord.Interaction, username: str, team_name: str = "", completed_fg: int = 0, att_fg: int = 0, long_fg: int = 0) -> None:
    """Inserts or updates Kicker stats."""
    headers = SHEET_HEADERS["Kicker"]
    
    fg_pct = round((completed_fg / att_fg) * 100, 2) if att_fg else 0.0

    # Custom logic for 'Long' stat: take the maximum
    kicker_update_logic = {
        "Long": lambda current_val_str, new_val: max(float(current_val_str or 0), float(new_val))
    }

    values = {
        "Completed": completed_fg, "ATT": att_fg, "Long": long_fg, "FG%": f"{fg_pct}%"
    }
    await insert_stats_template(interaction, "Kicker", username, team_name, headers, values, position_specific_update_logic=kicker_update_logic)

@bot.tree.command(name="insert_db_stats", description="Insert stats for DB (Defensive Back)")
@discord.app_commands.describe(
    username="Player's username",
    team_name="Team name (optional)",
    ints="Interceptions",
    tackles="Tackles",
    cmp_all="Completions Allowed",
    targets="Targets (against in coverage)",
    yda="Yards Allowed",
    tda="Touchdowns Allowed",
    snaps="Snaps"
)
async def insert_db_stats(interaction: discord.Interaction, username: str, team_name: str = "", ints: int = 0, tackles: int = 0, cmp_all: int = 0, targets: int = 0, yda: int = 0, tda: int = 0, snaps: int = 0) -> None:
    """Inserts or updates Defensive Back stats."""
    headers = SHEET_HEADERS["DB"]
    
    cmp_pct = round((cmp_all / targets) * 100, 2) if targets else 0.0

    values = {
        "Ints": ints, "Tackles": tackles, "CmpAll": cmp_all, "Targets": targets, 
        "%": f"{cmp_pct}%", "YDA": yda, "TDA": tda, "SNAPS": snaps
    }
    await insert_stats_template(interaction, "DB", username, team_name, headers, values)

@bot.tree.command(name="update_leaderboard_colors", description="Updates the sheet with primary colors for top players.")
@discord.app_commands.describe(position="The position to update (e.g., QB, RB, WR)")
async def update_leaderboard_colors(interaction: discord.Interaction, position: str) -> None:
    """Updates the Google Sheet to highlight top players for a given position."""
    await interaction.response.defer()
    if not gc:
        await interaction.followup.send("❌ Google Sheets client is not initialized. Cannot update leaderboard colors. Please check bot's authentication setup.")
        return

    sheet = connected_sheets.get(interaction.guild_id)
    if not sheet:
        await interaction.followup.send("⚠️ No Google Sheet connected. Use `/connect_sheet` first.")
        return

    position = position.upper()
    if position not in SHEET_HEADERS:
        await interaction.followup.send(f"❌ Invalid position: {position}. Supported positions are: {', '.join(SHEET_HEADERS.keys())}")
        return

    try:
        ws = sheet.worksheet(position)
        headers = ws.row_values(2) # Headers are in row 2
        sort_key = POSITION_SORT_KEYS.get(position)

        if not sort_key or sort_key not in headers:
            await interaction.followup.send(f"⚠️ Cannot sort {position} data. No valid sort key or sort key '{sort_key}' not found in headers for this position.")
            return

        # Get all records, skipping the first two rows (title and headers)
        all_values = ws.get_all_values()
        # Filter for actual data rows and ensure they are not empty
        records = [dict(zip(headers, row)) for row in all_values[2:] if any(cell.strip() for cell in row)]

        # Filter out rows where the sort_key is not a valid number
        valid_records = []
        for record in records:
            try:
                val_str = str(record.get(sort_key, '0'))
                if val_str.endswith('%'):
                    val_str = val_str[:-1]
                float(val_str) # Just try to convert to float
                valid_records.append(record)
            except ValueError:
                logging.warning(f"Skipping record with non-numeric sort key '{sort_key}' for {record.get('username')}: '{record.get(sort_key)}'")
                continue

        if not valid_records:
            await interaction.followup.send(f"ℹ️ No valid data found for {position} to sort and color.")
            return

        # Sort the records based on the sort_key in descending order
        sorted_records = sorted(
            valid_records,
            key=lambda x: float(str(x.get(sort_key, '0')).replace('%', '')), # Remove '%' for sorting
            reverse=True
        )

        top_players_count = min(3, len(sorted_records))
        top_players = sorted_records[:top_players_count]

        top_player_color = POSITION_COLORS.get(position, {"red": 0.5, "green": 0.5, "blue": 0.5}) # Default gray
        
        # Clear existing background colors for data rows (from row 3 onwards) before applying new ones
        last_col_letter = col_index_to_letter(len(headers) - 1)
        # Apply default light gray to all data rows first
        ws.format(f"A3:{last_col_letter}{len(all_values)}", {
            "backgroundColor": {"red": 0.98, "green": 0.98, "blue": 0.98}
        })

        # Apply formatting to top players and subtle background to others
        for record in sorted_records:
            username_to_find = record.get('username')
            if not username_to_find:
                continue

            player_row_index = -1
            for r_idx, sheet_row in enumerate(all_values):
                if r_idx < 2: continue # Skip title and header rows
                try:
                    # Check if the row has enough columns and the username matches
                    if len(sheet_row) > headers.index('username') and sheet_row[headers.index('username')] == username_to_find:
                        player_row_index = r_idx + 1 # +1 because sheet rows are 1-indexed
                        break
                except ValueError:
                    logging.warning(f"Username column not found in row {r_idx+1} for sheet {ws.title}. Skipping row.")
                    continue

            if player_row_index != -1:
                if record in top_players:
                    bg_color = top_player_color
                    text_color = {"red": 1.0, "green": 1.0, "blue": 1.0} # White text for top players
                    is_bold = True
                else:
                    # Apply a very light shade of the position color for other players
                    base_color = POSITION_COLORS.get(position, {"red": 0.95, "green": 0.95, "blue": 0.95})
                    bg_color = {
                        "red": base_color["red"] + 0.03,
                        "green": base_color["green"] + 0.03,
                        "blue": base_color["blue"] + 0.03
                    }
                    bg_color = {k: min(v, 1.0) for k, v in bg_color.items()} # Ensure color components don't exceed 1.0
                    text_color = {"red": 0.1, "green": 0.1, "blue": 0.1} # Dark text
                    is_bold = False

                ws.format(f"A{player_row_index}:{last_col_letter}{player_row_index}", {
                    "backgroundColor": bg_color,
                    "textFormat": {
                        "foregroundColor": text_color,
                        "bold": is_bold
                    }
                })

        await interaction.followup.send(f"✅ Leaderboard colors updated for {position} based on {sort_key}.")

    except Exception as e:
        logging.error(f"Failed to update leaderboard colors for {position}: {e}", exc_info=True)
        await interaction.followup.send(f"❌ Failed to update leaderboard colors for {position}: {e}")


# --- Transaction Management for Reversal ---
# This will be a new worksheet in the connected Google Sheet
TRANSACTION_LOG_SHEET_NAME = "Reversal_Log"
TRANSACTION_LOG_HEADERS = ["TransactionID", "Timestamp", "UserID", "Username", "Position", "PlayerName", "Team", "StatName", "DeltaValue"]

async def get_or_create_transaction_log_sheet(sheet: gspread.Spreadsheet) -> gspread.Worksheet:
    """Gets or creates the transaction log worksheet."""
    try:
        ws = sheet.worksheet(TRANSACTION_LOG_SHEET_NAME)
        # Check headers and update if necessary
        current_headers = ws.row_values(1)
        if current_headers != TRANSACTION_LOG_HEADERS:
            logging.warning(f"Transaction log sheet '{TRANSACTION_LOG_SHEET_NAME}' headers mismatch. Updating.")
            ws.clear()
            ws.append_row(TRANSACTION_LOG_HEADERS)
            # Apply basic formatting to the header row
            last_col_letter = col_index_to_letter(len(TRANSACTION_LOG_HEADERS) - 1)
            ws.format(f"A1:{last_col_letter}1", {
                "backgroundColor": {"red": 0.9, "green": 0.9, "blue": 0.9},
                "textFormat": {"bold": True},
                "horizontalAlignment": "CENTER"
            })
    except gspread.exceptions.WorksheetNotFound:
        ws = sheet.add_worksheet(title=TRANSACTION_LOG_SHEET_NAME, rows="100", cols=str(len(TRANSACTION_LOG_HEADERS)))
        ws.append_row(TRANSACTION_LOG_HEADERS)
        # Apply basic formatting to the header row
        last_col_letter = col_index_to_letter(len(TRANSACTION_LOG_HEADERS) - 1)
        ws.format(f"A1:{last_col_letter}1", {
            "backgroundColor": {"red": 0.9, "green": 0.9, "blue": 0.9},
            "textFormat": {"bold": True},
            "horizontalAlignment": "CENTER"
        })
    return ws

async def log_transaction(sheet: gspread.Spreadsheet, transaction_id: str, user_id: int, username: str, processed_data: list) -> None:
    """Logs the details of a stat update transaction to the Reversal_Log sheet."""
    ws = await get_or_create_transaction_log_sheet(sheet)
    rows_to_append = []
    timestamp = datetime.now().isoformat()

    for entry in processed_data:
        position = entry["position"]
        player_name = entry["username"]
        team = entry["team"]
        deltas = entry["deltas"]

        for stat_name, delta_value in deltas.items():
            # Skip 'Team' and 'username' as they are not numerical stats to reverse
            if stat_name.lower() in ['team', 'username']:
                continue
            rows_to_append.append([
                transaction_id,
                timestamp,
                str(user_id),
                username,
                position,
                player_name,
                team,
                stat_name,
                str(delta_value) # Store as string to handle percentages/non-numeric if needed
            ])
    if rows_to_append:
        try:
            ws.append_rows(rows_to_append)
            logging.info(f"Logged {len(rows_to_append)} entries for transaction {transaction_id}")
        except Exception as e:
            logging.error(f"Failed to log transaction {transaction_id}: {e}")

async def get_transaction_deltas(sheet: gspread.Spreadsheet, transaction_id: str) -> list:
    """Retrieves all deltas for a given transaction ID from the Reversal_Log sheet."""
    ws = await get_or_create_transaction_log_sheet(sheet)
    all_records = ws.get_all_records()
    
    deltas_for_transaction = []
    for record in all_records:
        if record.get("TransactionID") == transaction_id:
            deltas_for_transaction.append(record)
    return deltas_for_transaction

async def clear_transaction_log(sheet: gspread.Spreadsheet, transaction_id: str) -> None:
    """Clears entries for a given transaction ID from the Reversal_Log sheet."""
    ws = await get_or_create_transaction_log_sheet(sheet)
    all_values = ws.get_all_values()
    
    # Find rows to delete (excluding header)
    rows_to_keep = [all_values[0]] # Keep the header row
    for r_idx, row in enumerate(all_values[1:]): # Start from index 1 to skip header
        if row and row[0] != transaction_id: # Assuming TransactionID is the first column
            rows_to_keep.append(row)
    
    try:
        # Clear the entire sheet and then re-append rows to keep
        ws.clear()
        if rows_to_keep:
            ws.append_rows(rows_to_keep)
        logging.info(f"Cleared transaction {transaction_id} from log.")
    except Exception as e:
        logging.error(f"Failed to clear transaction {transaction_id} from log: {e}")

# --- Discord UI Views ---
class StatActionView(discord.ui.View):
    def __init__(self, transaction_id: str, sheet_url: str, processed_players_summary: str):
        super().__init__(timeout=300) # Timeout after 5 minutes
        self.transaction_id = transaction_id
        self.sheet_url = sheet_url
        self.processed_players_summary = processed_players_summary

        self.add_item(discord.ui.Button(label="View Sheet", style=discord.ButtonStyle.link, url=self.sheet_url))

    @discord.ui.button(label="Reverse Stats", style=discord.ButtonStyle.red, custom_id="reverse_stats_button")
    async def reverse_stats_button_callback(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.defer(ephemeral=True)

        if not interaction.user.guild_permissions.administrator:
            await interaction.followup.send("❌ You need administrator permissions to reverse stats.", ephemeral=True)
            return

        sheet = connected_sheets.get(interaction.guild_id)
        if not sheet:
            await interaction.followup.send("⚠️ No Google Sheet connected to this server.", ephemeral=True)
            return

        try:
            deltas_to_reverse = await get_transaction_deltas(sheet, self.transaction_id)
            if not deltas_to_reverse:
                await interaction.followup.send(f"ℹ️ No stats found to reverse for transaction ID `{self.transaction_id}`. Perhaps they were already reversed or the log was cleared.", ephemeral=True)
                return

            reversed_count = 0
            # Group updates by worksheet for batch processing
            updates_by_worksheet = {} # {worksheet_title: {'requests': [{'range': "A1", 'values': [[val]]}, ...], 'worksheet': gspread.Worksheet}}

            for record in deltas_to_reverse:
                position = record.get("Position")
                player_name = record.get("PlayerName")
                stat_name = record.get("StatName")
                delta_value_str = record.get("DeltaValue", "0")

                if not all([position, player_name, stat_name, delta_value_str is not None]):
                    logging.warning(f"Skipping malformed reversal record: {record}")
                    continue

                try:
                    delta_value = float(delta_value_str.replace('%', ''))
                except ValueError:
                    logging.warning(f"Skipping reversal for non-numeric stat '{stat_name}' with delta '{delta_value_str}' for player '{player_name}'.")
                    continue

                # Get the worksheet from cache or create/fetch it
                if position not in updates_by_worksheet:
                    ws, _ = get_or_create_worksheet(sheet, position, SHEET_HEADERS.get(position, []))
                    updates_by_worksheet[position] = {"worksheet": ws, "requests": []}
                else:
                    ws = updates_by_worksheet[position]["worksheet"]

                headers = ws.row_values(2) # Fetch headers once per worksheet
                username_col_index = -1
                for i, h in enumerate(headers):
                    if h.lower() == 'username':
                        username_col_index = i
                        break
                
                if username_col_index == -1:
                    logging.error(f"Username column not found in sheet '{ws.title}' for reversal.")
                    continue

                # Fetch all values for the current worksheet to find the player's row and current stat value
                all_sheet_values = ws.get_all_values()
                player_row_index_in_sheet = None
                for i, sheet_row in enumerate(all_sheet_values[2:]): # Data starts from row 3 (index 2)
                    if len(sheet_row) > username_col_index and sheet_row[username_col_index] == player_name:
                        player_row_index_in_sheet = i + 3 # +1 for 0-based list index, +2 for title/header rows
                        break
                
                if player_row_index_in_sheet:
                    col_idx_for_stat = -1
                    if stat_name in headers:
                        col_idx_for_stat = headers.index(stat_name)
                    
                    if col_idx_for_stat != -1:
                        # Get the current cell value from the fetched all_sheet_values
                        current_cell_value = all_sheet_values[player_row_index_in_sheet - 1][col_idx_for_stat] # -1 for 0-based index
                        
                        if isinstance(current_cell_value, str) and current_cell_value.endswith('%'):
                            current_cell_value = current_cell_value[:-1]
                        
                        try:
                            current_stat_val_num = float(current_cell_value) if current_cell_value else 0.0
                            reversed_value = current_stat_val_num - delta_value
                            
                            # Format back to percentage if it was one
                            if delta_value_str.endswith('%'):
                                new_val_str = f"{round(reversed_value, 2)}%"
                            else:
                                new_val_str = str(round(reversed_value, 2))
                            
                            cell_range = f"{col_index_to_letter(col_idx_for_stat)}{player_row_index_in_sheet}"
                            updates_by_worksheet[position]["requests"].append({
                                "range": cell_range,
                                "values": [[new_val_str]]
                            })
                            reversed_count += 1

                        except ValueError:
                            logging.warning(f"Could not reverse non-numeric or malformed stat '{stat_name}' for player '{player_name}'. Value: '{current_cell_value}'")
                            pass
                    else:
                        logging.warning(f"Stat '{stat_name}' column not found for player '{player_name}' in sheet '{ws.title}'.")
                else:
                    logging.warning(f"Player '{player_name}' not found in sheet '{ws.title}' for reversal.")

            # Execute batch updates for each worksheet
            for ws_data in updates_by_worksheet.values():
                if ws_data["requests"]:
                    try:
                        ws_data["worksheet"].batch_update(ws_data["requests"])
                        logging.info(f"Batch updated {len(ws_data['requests'])} cells in sheet '{ws_data['worksheet'].title}' for reversal.")
                    except Exception as e:
                        logging.error(f"Error during batch update for reversal in sheet '{ws_data['worksheet'].title}': {e}")

            await clear_transaction_log(sheet, self.transaction_id)
            await interaction.followup.send(f"✅ Successfully reversed **{reversed_count}** stat entries for transaction ID `{self.transaction_id}`.")
            # Disable the button after reversal
            button.disabled = True
            await interaction.message.edit(view=self)

        except Exception as e:
            logging.error(f"Error during stat reversal for transaction ID {self.transaction_id}: {e}", exc_info=True)
            await interaction.followup.send(f"❌ An error occurred while reversing stats: {e}", ephemeral=True)


# --- Modified Command: /process_csv_attachment ---
@bot.tree.command(name="process_csv_attachment", description="Processes an uploaded CSV file to update player stats.")
@discord.app_commands.describe(attachment="The CSV file to process.")
async def process_csv_attachment(interaction: discord.Interaction, attachment: discord.Attachment):
    await interaction.response.defer(ephemeral=True)

    if not attachment.filename.lower().endswith('.csv'):
        await interaction.followup.send("❌ Please upload a `.csv` file.", ephemeral=True)
        return
    
    try:
        csv_content = await attachment.read()
        csv_string = csv_content.decode('utf-8')
        await process_csv_data(interaction.followup, interaction.guild_id, interaction.user, csv_string)

    except Exception as e:
        logging.error(f"Error reading or processing CSV attachment: {e}", exc_info=True)
        await interaction.followup.send(f"❌ An error occurred while processing your CSV: {e}", ephemeral=True)

# --- New Commands for CSV Listening ---

@bot.tree.command(name="enable_csv_listening", description="Enables bot to automatically process CSVs in this channel.")
@discord.app_commands.default_permissions(administrator=True)
async def enable_csv_listening(interaction: discord.Interaction, channel: discord.TextChannel = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to enable CSV listening.", ephemeral=True)
        return

    target_channel = channel or interaction.channel
    guild_id = target_channel.guild.id

    if guild_id not in listening_channels:
        listening_channels[guild_id] = []
    
    if target_channel.id in listening_channels[guild_id]:
        await interaction.response.send_message(f"ℹ️ This channel ({target_channel.mention}) is already enabled for CSV listening.", ephemeral=True)
    else:
        listening_channels[guild_id].append(target_channel.id)
        save_listening_channels()
        await interaction.response.send_message(f"✅ Enabled CSV listening in {target_channel.mention}. I will now process any CSV files uploaded here.", ephemeral=False)

@bot.tree.command(name="disable_csv_listening", description="Disables bot from automatically processing CSVs in this channel.")
@discord.app_commands.default_permissions(administrator=True)
async def disable_csv_listening(interaction: discord.Interaction, channel: discord.TextChannel = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to disable CSV listening.", ephemeral=True)
        return

    target_channel = channel or interaction.channel
    guild_id = target_channel.guild.id

    if guild_id in listening_channels and target_channel.id in listening_channels[guild_id]:
        listening_channels[guild_id].remove(target_channel.id)
        if not listening_channels[guild_id]: # If no channels left for this guild, remove guild entry
            del listening_channels[guild_id]
        save_listening_channels()
        await interaction.response.send_message(f"✅ Disabled CSV listening in {target_channel.mention}.", ephemeral=False)
    else:
        await interaction.response.send_message(f"ℹ️ This channel ({target_channel.mention}) is not currently enabled for CSV listening.", ephemeral=True)

@bot.tree.command(name="list_listening_channels", description="Lists channels where the bot is listening for CSV uploads.")
async def list_listening_channels(interaction: discord.Interaction):
    guild_id = interaction.guild_id
    channels_for_guild = listening_channels.get(guild_id, [])

    if not channels_for_guild:
        await interaction.response.send_message("ℹ️ I am not currently listening for CSV uploads in any channels on this server.", ephemeral=True)
        return

    channel_mentions = []
    for channel_id in channels_for_guild:
        channel = bot.get_channel(channel_id)
        if channel:
            channel_mentions.append(channel.mention)
        else:
            channel_mentions.append(f"<#{channel_id}> (Channel not found)")
    
    response_text = "✅ I am currently listening for CSV uploads in the following channels:\n" + "\n".join(channel_mentions)
    await interaction.response.send_message(response_text, ephemeral=True)


# --- Modified Command: /diagnose_csv ---
@bot.tree.command(name="diagnose_csv", description="Analyzes a CSV file and suggests stat insertion commands.")
@discord.app_commands.describe(attachment="The CSV file to diagnose.")
async def diagnose_csv(interaction: discord.Interaction, attachment: discord.Attachment):
    import logging
    from datetime import datetime
    try:
        await interaction.response.defer(ephemeral=True)
        content = await attachment.read()
        csv_string = content.decode("utf-8")
        player_data = parse_csv_content(csv_string)
        if not player_data:
            await interaction.followup.send("❌ No player data found in the CSV.", ephemeral=True)
            return

        # Try to guess position from the first player's keys
        first = player_data[0]
        position_guess = None
        for pos, headers in SHEET_HEADERS.items():
            if all(h in first for h in headers if h.lower() != "team"):
                position_guess = pos
                break

        # Compose embed(s)
        embeds = []
        base_embed = discord.Embed(
            title="CSV Diagnosis",
            description=f"Detected {len(player_data)} player entries.{' Position: ' + position_guess if position_guess else ''}",
            color=discord.Color.blurple()
        )
        fields = []
        for p in player_data:
            name = p.get("username", "Unknown")
            team = p.get("Team", p.get("team", ""))
            summary = f"Team: {team}\n" + "\n".join(f"{k}: {v}" for k, v in p.items() if k not in ("username", "Team", "team"))
            fields.append({"name": name, "value": summary})

        # Paginate if needed
        def chunk_embeds(fields, base_embed, max_chars=5900, max_fields=25):
            embeds = []
            current_embed = base_embed.copy()
            current_length = len(base_embed.description or "")
            field_count = 0
            for field in fields:
                field_str = f"{field['name']}{field['value']}"
                if (current_length + len(field_str) > max_chars) or (field_count >= max_fields):
                    embeds.append(current_embed)
                    current_embed = base_embed.copy()
                    current_length = len(base_embed.description or "")
                    field_count = 0
                current_embed.add_field(name=field['name'], value=field['value'], inline=False)
                current_length += len(field_str)
                field_count += 1
            if field_count > 0 or not embeds:
                embeds.append(current_embed)
            return embeds

        embeds = chunk_embeds(fields, base_embed)
        for i, embed in enumerate(embeds):
            embed.set_footer(
                text=f"Page {i+1}/{len(embeds)} | Diagnosed by {interaction.user.display_name} | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
    except Exception as e:
        logging.error(f"Error diagnosing CSV: {e}", exc_info=True)
        await interaction.followup.send(f"❌ An error occurred while diagnosing your CSV: {e}", ephemeral=True)
    await interaction.response.defer(ephemeral=True)

    if not attachment.filename.lower().endswith('.csv'):
        await interaction.followup.send("❌ Please upload a `.csv` file.", ephemeral=True)
        return

    try:
        csv_content = await attachment.read()
        csv_string = csv_content.decode('utf-8')
        
        parsed_data = parse_csv_content(csv_string)
        
        diagnosed_commands = []

        if not parsed_data:
            await interaction.followup.send("ℹ️ No diagnosable player data found in the CSV.", ephemeral=True)
            return

        for player_entry in parsed_data:
            player_name = player_entry["username"]
            position = player_entry["position"]
            team_name = player_entry["team"]
            stats = player_entry["stats"]

            if position not in SHEET_HEADERS:
                diagnosed_commands.append(f"⚠️ Skipping player **{player_name}**: Unknown position '{position}'.")
                continue
            
            command_name = f"insert_{position.lower()}_stats"
            command_params = []
            
            # Add username and team_name
            command_params.append(f'username="{player_name}"')
            if team_name:
                command_params.append(f'team_name="{team_name}"')

            # Map parsed stats to command parameters
            # This requires knowing the exact parameter names for each insert_X_stats command
            # This part needs to be carefully aligned with the @discord.app_commands.describe arguments
            
            # General mapping from SHEET_HEADERS keys to command parameter names
            # This is a simplified mapping; for complex cases, direct mapping per position might be needed.
            param_name_map = {
                "Passes PTs": "passes_pts", "YDs": "yds", "TDs": "tds", "INT": "interceptions", "SCK": "sacks",
                "3RD": "third_down_conversions", "4TH": "fourth_down_conversions", "SNAP": "snap",
                "ATT": "att", "Yards": "yards", "TDS": "tds", # RB
                "Catch": "catch", "Target": "target", "Int allowd": "int_allowed", # WR
                "yac": "yac", "targets": "targets", "sacks": "sacks", # TE
                "snaps": "snaps", "pressures": "pressures", "qbh": "qbh", # Center
                "Sacks": "sacks", "Pressures": "pressures", "QBH": "qbh", "Tackles": "tackles", "Safeties": "safeties", # DE
                "ints": "ints", "tackles": "tackles", "Targets": "targets", "CmpAll": "cmp_all", "YDA": "yda", "TDA": "tda", # LB, DB
                "Completed": "completed_fg", "ATT": "att_fg", "Long": "long_fg", # Kicker
            }

            # Special handling for calculated fields that have input parameters
            if position == "QB":
                # QB C% is calculated from completed_passes and pass_attempts
                # We need to find the raw values if they were in the CSV
                completed_passes_val = stats.get("Completions")
                pass_attempts_val = stats.get("Attempts")
                if completed_passes_val is not None:
                    command_params.append(f"completed_passes={repr(completed_passes_val)}")
                if pass_attempts_val is not None:
                    command_params.append(f"pass_attempts={repr(pass_attempts_val)}")
                # Remove C% from general stats as it's calculated by the command
                stats.pop("C%", None)
            elif position == "Kicker":
                # Kicker FG% is calculated from completed and att
                completed_fg_val = stats.get("Completed")
                att_fg_val = stats.get("ATT")
                if completed_fg_val is not None:
                    command_params.append(f"completed_fg={repr(completed_fg_val)}")
                if att_fg_val is not None:
                    command_params.append(f"att_fg={repr(att_fg_val)}")
                stats.pop("FG%", None)
            elif position in ["WR", "TE", "LB", "DB"]:
                # Catch % or Cmp% is calculated from Catch/CmpAll and Target/Targets
                catch_cmp_val = stats.get("Catch") or stats.get("CmpAll")
                targets_val = stats.get("Target") or stats.get("Targets")
                
                if position == "WR":
                    if catch_cmp_val is not None: command_params.append(f"catch={repr(catch_cmp_val)}")
                    if targets_val is not None: command_params.append(f"target={repr(targets_val)}")
                elif position == "TE":
                    if catch_cmp_val is not None: command_params.append(f"catch={repr(catch_cmp_val)}")
                    if targets_val is not None: command_params.append(f"targets={repr(targets_val)}")
                elif position == "LB" or position == "DB":
                    if catch_cmp_val is not None: command_params.append(f"cmp_all={repr(catch_cmp_val)}")
                    if targets_val is not None: command_params.append(f"targets={repr(targets_val)}")
                stats.pop("%", None) # Remove the calculated percentage
            elif position == "RB":
                # Y/A is calculated from Yards and ATT
                stats.pop("Y/A", None)
            elif position == "Center":
                # SPS is calculated from sacks and snaps
                stats.pop("SPS", None)


            for stat_key, stat_value in stats.items():
                param_name = param_name_map.get(stat_key, stat_key.lower().replace(' ', '_'))
                
                # Ensure values are properly quoted for string parameters in the command suggestion
                if isinstance(stat_value, str) and not (stat_value.startswith('"') and stat_value.endswith('"')):
                    param_value_str = repr(stat_value) # Use repr to handle quoting correctly
                else:
                    param_value_str = str(stat_value)

                # Avoid adding parameters that are None or empty strings unless explicitly needed
                if stat_value is not None and str(stat_value).strip() != '':
                    command_params.append(f'{param_name}={param_value_str}')
            
            diagnosed_commands.append(f"**{player_name} ({position}):**\n`/{command_name} {' '.join(command_params)}`")
            
        if not diagnosed_commands:
            await interaction.followup.send("ℹ️ No diagnosable player data found in the CSV.", ephemeral=True)
            return

        # Split commands into multiple embeds if too long
        current_embed_description = ""
        embeds = []
        for cmd_str in diagnosed_commands:
            if len(current_embed_description) + len(cmd_str) + 50 > 4000: # Discord embed description limit is 4096
                embed = discord.Embed(
                    title="🔍 CSV Diagnosis & Suggested Commands",
                    description=current_embed_description,
                    color=0x00aaff
                )
                embeds.append(embed)
                current_embed_description = ""
            current_embed_description += cmd_str + "\n\n"
        
        if current_embed_description:
            embed = discord.Embed(
                title="🔍 CSV Diagnosis & Suggested Commands",
                description=current_embed_description,
                color=0x00aaff
            )
            embeds.append(embed)

        for i, embed in enumerate(embeds):
            embed.set_footer(text=f"Page {i+1}/{len(embeds)} | Diagnosed by {interaction.user.display_name} | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        logging.error(f"Error diagnosing CSV: {e}", exc_info=True)
        await interaction.followup.send(f"❌ An error occurred while diagnosing your CSV: {e}", ephemeral=True)





