#!/usr/bin/env python3
"""
Database Migration Script - Adds missing transaction_log_channel column
This script fixes the schema mismatch between Supabase and SQLite databases.

Upload this file to your cloud server (Cybrancee) and run it to fix the sync error.
"""

import sqlite3
import os

def migrate_database():
    """Add missing transaction_log_channel column to guild_settings table"""
    
    db_path = 'transaction_bot.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        print("   Make sure you're running this script in the same directory as your bot.")
        return False
    
    try:
        print(f"🔧 Connecting to database: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if the column already exists
        cursor.execute("PRAGMA table_info(guild_settings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📋 Current guild_settings columns: {columns}")
        
        if 'transaction_log_channel' in columns:
            print("✅ Column 'transaction_log_channel' already exists - no migration needed")
            conn.close()
            return True
        
        # Add the missing column
        print("🔄 Adding missing column 'transaction_log_channel'...")
        cursor.execute("ALTER TABLE guild_settings ADD COLUMN transaction_log_channel INTEGER")
        
        # Commit the change
        conn.commit()
        
        # Verify the column was added
        cursor.execute("PRAGMA table_info(guild_settings)")
        updated_columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📋 Updated guild_settings columns: {updated_columns}")
        
        if 'transaction_log_channel' in updated_columns:
            print("✅ Successfully added 'transaction_log_channel' column!")
            print("✅ Database migration completed successfully!")
        else:
            print("❌ Failed to add column - please check the logs")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        return False

def main():
    print("🗄️ Database Migration Script")
    print("=" * 50)
    print("This script will add the missing 'transaction_log_channel' column")
    print("to the guild_settings table in your SQLite database.")
    print()
    
    success = migrate_database()
    
    if success:
        print()
        print("🎉 Migration completed successfully!")
        print("You can now restart your bot and the Supabase sync should work.")
    else:
        print()
        print("❌ Migration failed!")
        print("Please check the error messages above and try again.")

if __name__ == "__main__":
    main()
