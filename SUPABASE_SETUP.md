# Supabase Setup Guide for Discord Bot

## Prerequisites
1. A Supabase account (free tier available)
2. Python 3.8 or higher
3. Your Discord bot token

## Step 1: Create Supabase Project
1. Go to [Supabase](https://supabase.com) and create an account
2. Create a new project
3. Wait for the project to be set up (takes a few minutes)

## Step 2: Set Up Database Tables
1. In your Supabase dashboard, go to the SQL Editor
2. Copy and paste the contents of `supabase_migration.sql` 
3. Click "Run" to execute the migration script
4. Verify that all tables were created in the "Table Editor"

## Step 3: Configure Row Level Security (RLS)
1. In Supabase dashboard, go to Authentication > Settings
2. Enable RLS for security (recommended for production)
3. Create policies for your tables if needed

## Step 4: Get Your Credentials
1. Go to Settings > API in your Supabase dashboard
2. Copy your Project URL
3. Copy your `anon` public key (or service_role key for more permissions)

## Step 5: Set Environment Variables
Create a `.env` file in your bot directory with:

```env
# Database Configuration
DATABASE_TYPE=supabase
SUPABASE_URL=your_project_url_here
SUPABASE_KEY=your_anon_key_here

# Discord Bot Token
DISCORD_BOT_TOKEN=your_bot_token_here
```

## Step 6: Install Dependencies
```bash
pip install -r requirements.txt
```

## Step 7: Run Your Bot
```bash
python "import sqlite3.py"
```

## Fallback to SQLite
If Supabase setup fails, the bot will automatically fall back to SQLite:
- No configuration needed
- Database file created locally
- All features work the same way

## Benefits of Supabase
- **Cloud-hosted**: No local database files
- **Scalable**: Handles more concurrent users
- **Real-time**: Built-in real-time features
- **Dashboard**: Easy to view/edit data via web interface
- **Backups**: Automatic backups and point-in-time recovery
- **Security**: Built-in RLS and authentication

## Switching Between Databases
To switch between SQLite and Supabase, simply change the `DATABASE_TYPE` environment variable:
- `DATABASE_TYPE=sqlite` for SQLite
- `DATABASE_TYPE=supabase` for Supabase

## Troubleshooting
1. **Connection Error**: Check your SUPABASE_URL and SUPABASE_KEY
2. **Table Not Found**: Make sure you ran the migration script
3. **Permission Denied**: Check your RLS policies
4. **Bot Crashes**: Check the console for error messages

## Migration from SQLite to Supabase
If you have existing SQLite data, you can export it and import into Supabase:
1. Export your SQLite data to CSV
2. Import the CSV files into Supabase tables
3. Update your environment variables
4. Restart your bot

## Production Notes
- Use the `service_role` key for production (has more permissions)
- Enable RLS and create appropriate policies
- Monitor your usage in the Supabase dashboard
- Consider upgrading to Pro tier for production use
