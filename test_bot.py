#!/usr/bin/env python3
"""
Quick test script to check for syntax errors and import issues
in the Discord bot without actually running it.
"""

import ast
import sys
import os

def check_syntax(file_path):
    """Check if the Python file has valid syntax."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST to check for syntax errors
        ast.parse(content)
        print(f"✅ Syntax check PASSED for {file_path}")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error in {file_path}:")
        print(f"   Line {e.lineno}: {e.text}")
        print(f"   Error: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Error reading {file_path}: {e}")
        return False

def check_imports(file_path):
    """Check which imports might fail."""
    print(f"\n🔍 Checking imports for {file_path}...")
    
    required_packages = [
        'discord',
        'gspread', 
        'google.oauth2',
        'pytz'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package} - Available")
        except ImportError:
            print(f"   ❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("   Install with: pip install discord.py gspread google-auth pytz")
    else:
        print(f"\n✅ All required packages are available")
    
    return len(missing_packages) == 0

if __name__ == "__main__":
    bot_file = "import sqlite3.py"
    
    if not os.path.exists(bot_file):
        print(f"❌ File not found: {bot_file}")
        sys.exit(1)
    
    print("🤖 Testing Discord Bot File...")
    print("=" * 50)
    
    # Check syntax
    syntax_ok = check_syntax(bot_file)
    
    # Check imports
    imports_ok = check_imports(bot_file)
    
    print("\n" + "=" * 50)
    if syntax_ok:
        if imports_ok:
            print("🎉 Bot file looks good! Ready to run.")
        else:
            print("⚠️  Bot file syntax is OK, but some packages are missing.")
            print("   Install missing packages then try running the bot.")
    else:
        print("❌ Bot file has syntax errors that need to be fixed.")
    
    print("\n💡 To run the bot:")
    print("   1. Set environment variable: set DISCORD_BOT_TOKEN=your_token_here")
    print("   2. Run: python \"import sqlite3.py\"")
