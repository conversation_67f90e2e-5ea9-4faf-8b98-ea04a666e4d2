import discord
from discord import app_commands
from discord.ext import commands
from typing import List, Optional
import asyncio

class RealtimeRoleSearch(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
    
    @app_commands.command(name="mention_role_realtime", description="Search and mention a role with real-time results")
    async def mention_role_realtime(self, interaction: discord.Interaction):
        # Defer the response to give us time to set up the UI
        await interaction.response.defer(ephemeral=True)
        
        # Create the initial view with an empty search box
        view = RoleSearchView(interaction.guild)
        
        # Send the initial message with the view
        message = await interaction.followup.send(
            "Start typing to search for roles:",
            view=view,
            ephemeral=True
        )
        
        # Store the message for the view to update
        view.message = message
        
        # Wait for the view to finish (timeout or selection made)
        await view.wait()
        
        # Check if a role was selected
        if view.selected_role:
            role = interaction.guild.get_role(int(view.selected_role))
            if role:
                # Send a message with the selected role mention
                await interaction.channel.send(f"{interaction.user.mention} mentioned {role.mention}")
                await interaction.followup.send(f"You mentioned {role.mention}", ephemeral=True)
        else:
            # If no role was selected (timeout or cancelled)
            await interaction.followup.send("No role was selected.", ephemeral=True)

class RoleSearchView(discord.ui.View):
    def __init__(self, guild):
        super().__init__(timeout=180)  # 3 minute timeout
        self.guild = guild
        self.message = None
        self.selected_role = None
        self.current_search = ""
        self.searching = False
        
        # Add the search input
        self.add_item(RoleSearchInput(self))
        
        # Add the results select menu (initially empty)
        self.results_select = RoleResultsSelect(self, [])
        self.add_item(self.results_select)
        
        # Add a cancel button
        self.cancel_button = discord.ui.Button(
            style=discord.ButtonStyle.secondary,
            label="Cancel",
            custom_id="cancel"
        )
        self.cancel_button.callback = self.cancel_callback
        self.add_item(self.cancel_button)
    
    async def cancel_callback(self, interaction: discord.Interaction):
        await interaction.response.send_message("Search cancelled.", ephemeral=True)
        self.stop()
    
    async def update_results(self, search_term):
        """Update the results based on the search term"""
        # Prevent multiple concurrent searches
        if self.searching:
            return
        
        self.searching = True
        self.current_search = search_term
        
        # Get all roles in the guild
        roles = self.guild.roles
        
        # Filter roles based on the search term
        if search_term:
            matching_roles = [
                role for role in roles 
                if search_term.lower() in role.name.lower() and not role.is_default()
            ]
            
            # Sort by relevance
            exact_matches = [role for role in matching_roles if role.name.lower() == search_term.lower()]
            starts_with = [role for role in matching_roles if role.name.lower().startswith(search_term.lower()) and role not in exact_matches]
            contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
            
            sorted_roles = exact_matches + starts_with + contains
        else:
            # If no search term, show some default roles (excluding @everyone)
            sorted_roles = [role for role in roles if not role.is_default()][:25]
        
        # Update the select menu with the new results
        self.remove_item(self.results_select)
        self.results_select = RoleResultsSelect(self, sorted_roles[:25])  # Discord limits to 25 options
        self.add_item(self.results_select)
        
        # Update the message with the new view
        if self.message:
            await self.message.edit(
                content=f"Search results for '{search_term}' ({len(sorted_roles)} found):" if search_term else "Start typing to search for roles:",
                view=self
            )
        
        self.searching = False

class RoleSearchInput(discord.ui.TextInput):
    def __init__(self, parent_view):
        self.parent_view = parent_view
        super().__init__(
            label="Search for roles",
            placeholder="Type to search...",
            required=False,
            min_length=0,
            max_length=100,
            style=discord.TextStyle.short,
            custom_id="role_search_input"
        )
    
    async def callback(self, interaction: discord.Interaction):
        # Get the search term
        search_term = self.value
        
        # Update the results based on the search term
        await self.parent_view.update_results(search_term)
        
        # Acknowledge the interaction
        await interaction.response.defer()

class RoleResultsSelect(discord.ui.Select):
    def __init__(self, parent_view, roles):
        self.parent_view = parent_view
        
        # Create options from roles
        options = []
        for role in roles[:25]:  # Discord limits to 25 options
            options.append(
                discord.SelectOption(
                    label=role.name,
                    value=str(role.id),
                    description=f"Members: {len(role.members)}",
                    emoji="👥"
                )
            )
        
        # If no roles found, add a placeholder option
        if not options:
            options.append(
                discord.SelectOption(
                    label="No roles found",
                    value="none",
                    description="Try a different search term"
                )
            )
        
        super().__init__(
            placeholder="Select a role" if options else "No roles found",
            options=options,
            disabled=not options or options[0].value == "none",
            custom_id="role_results_select"
        )
    
    async def callback(self, interaction: discord.Interaction):
        # Get the selected role ID
        selected_value = self.values[0]
        
        # If a valid role was selected
        if selected_value != "none":
            self.parent_view.selected_role = selected_value
            role = interaction.guild.get_role(int(selected_value))
            
            if role:
                await interaction.response.send_message(f"You selected: {role.mention}", ephemeral=True)
                # Stop the view
                self.parent_view.stop()
            else:
                await interaction.response.send_message("Role not found.", ephemeral=True)
        else:
            await interaction.response.send_message("Please select a valid role.", ephemeral=True)

# Alternative implementation using a modal with real-time updates
class RoleSearchModal(discord.ui.Modal):
    def __init__(self, guild):
        super().__init__(title="Search for Roles")
        self.guild = guild
        
        # Add a text input for the search
        self.search_input = discord.ui.TextInput(
            label="Start typing to search for roles",
            placeholder="Type role name...",
            required=False,
            min_length=0,
            max_length=100
        )
        self.add_item(self.search_input)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Get the search term
        search_term = self.search_input.value
        
        # Filter roles based on the search term
        roles = self.guild.roles
        matching_roles = [
            role for role in roles 
            if search_term.lower() in role.name.lower() and not role.is_default()
        ]
        
        # Sort by relevance
        exact_matches = [role for role in matching_roles if role.name.lower() == search_term.lower()]
        starts_with = [role for role in matching_roles if role.name.lower().startswith(search_term.lower()) and role not in exact_matches]
        contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
        
        sorted_roles = exact_matches + starts_with + contains
        
        # Create a select menu with the results
        view = discord.ui.View(timeout=60)
        
        if sorted_roles:
            # Add a select menu with the results
            select = discord.ui.Select(
                placeholder="Select a role",
                options=[
                    discord.SelectOption(
                        label=role.name,
                        value=str(role.id),
                        description=f"Members: {len(role.members)}"
                    ) for role in sorted_roles[:25]  # Discord limits to 25 options
                ]
            )
            
            async def select_callback(interaction: discord.Interaction):
                role_id = select.values[0]
                role = interaction.guild.get_role(int(role_id))
                
                if role:
                    await interaction.response.send_message(f"You selected: {role.mention}", ephemeral=True)
                else:
                    await interaction.response.send_message("Role not found.", ephemeral=True)
            
            select.callback = select_callback
            view.add_item(select)
            
            await interaction.response.send_message(
                f"Found {len(sorted_roles)} matching roles for '{search_term}':",
                view=view,
                ephemeral=True
            )
        else:
            await interaction.response.send_message(f"No roles found matching '{search_term}'.", ephemeral=True)

class RoleSearchCommand(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
    
    @app_commands.command(name="search_role_modal", description="Search for a role using a modal")
    async def search_role_modal(self, interaction: discord.Interaction):
        # Create and show the modal
        modal = RoleSearchModal(interaction.guild)
        await interaction.response.send_modal(modal)

async def setup(bot):
    await bot.add_cog(RealtimeRoleSearch(bot))
    await bot.add_cog(RoleSearchCommand(bot))
