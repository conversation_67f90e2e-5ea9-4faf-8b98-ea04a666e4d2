# Discord Role Select Menu

This project adds role selection and autocomplete features to a Discord bot, allowing users to easily mention roles by typing part of the role name.

## Features

### 1. Role Autocomplete
- As you type in a command parameter, matching roles will appear in a dropdown
- Select a role from the dropdown to mention it

### 2. Role Select UI
- Interactive UI with a search box and results that update as you type
- Select roles from a dropdown menu after searching

### 3. Real-time Role Search
- Type in a text box and see matching roles appear in real-time
- Select a role to mention it in your message

## Commands

The bot provides several commands for role selection:

- `/mention_role` - Select a role using Discord's built-in autocomplete
- `/send_with_role` - Send a message that mentions a role
- `/select_role` - Open an interactive UI to search for and select a role
- `/message_with_role` - Send a message with a role mention using the interactive UI
- `/mention_role_realtime` - Search for roles with real-time results as you type
- `/search_role_modal` - Search for a role using a modal dialog

## Setup Instructions

1. Make sure you have Python 3.8+ installed
2. Install the required packages:
   ```
   pip install discord.py
   ```
3. Replace `YOUR_TOKEN_HERE` in `main.py` with your Discord bot token
4. Run the bot:
   ```
   python main.py
   ```

## How It Works

The role selection feature works in three different ways:

1. **Discord's Built-in Autocomplete**: Uses Discord's autocomplete feature to show matching roles as you type in a command parameter.

2. **Interactive UI**: Provides a button that opens a modal with a text input. As you type, it filters the roles and displays them in a select menu.

3. **Real-time Search**: Creates a view with a text input and a select menu that updates in real-time as you type.

## Example Usage

### Using the Autocomplete Feature
1. Type `/mention_role` and start typing a role name
2. Select a role from the dropdown that appears
3. The bot will send a message mentioning the selected role

### Using the Interactive UI
1. Type `/select_role` to open the role selection UI
2. Click the "Search Roles" button
3. Type part of a role name in the modal that appears
4. Select a role from the dropdown of matching roles

### Using the Real-time Search
1. Type `/mention_role_realtime` to open the real-time search
2. Type in the search box to see matching roles appear in the dropdown
3. Select a role from the dropdown to mention it

## Customization

You can customize the appearance and behavior of the role selection UI by modifying the following files:

- `role_autocomplete.py` - For the basic autocomplete functionality
- `role_select_ui.py` - For the interactive UI with modals
- `realtime_role_search.py` - For the real-time search functionality

## Troubleshooting

- If commands don't appear in Discord, make sure you've invited the bot with the `applications.commands` scope
- If the bot doesn't respond, check that you've set the correct token in `main.py`
- If roles don't appear in the search results, ensure the bot has the `GUILD_MEMBERS` intent enabled
