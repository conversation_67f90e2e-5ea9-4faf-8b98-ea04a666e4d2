-- Check slot_configs table schema
.schema slot_configs

-- Check guild_settings table schema  
.schema guild_settings

-- Check teams table schema
.schema teams

-- List all tables
.tables

-- Check slot_configs columns specifically
PRAGMA table_info(slot_configs);

-- Check guild_settings columns
PRAGMA table_info(guild_settings);

-- Check teams columns
PRAGMA table_info(teams);

-- Try to add missing columns
ALTER TABLE slot_configs ADD COLUMN weekly_games_channel INTEGER;
ALTER TABLE slot_configs ADD COLUMN score_channel INTEGER;
ALTER TABLE slot_configs ADD COLUMN created_at TEXT;
ALTER TABLE slot_configs ADD COLUMN updated_at TEXT;
ALTER TABLE guild_settings ADD COLUMN transaction_log_channel INTEGER;
ALTER TABLE teams ADD COLUMN guild_id INTEGER;

-- Check final schema
PRAGMA table_info(slot_configs);
PRAGMA table_info(guild_settings);
PRAGMA table_info(teams);
