# Autosetup Command Timeout Fix

## Problem
The `/autosetup` command was throwing a **Discord 10062 "Unknown interaction"** error, which occurs when the bot takes too long (>3 seconds) to respond to or defer a Discord interaction.

## Root Cause
1. **Permission Check Delay**: The `@app_commands.checks.has_permissions(administrator=True)` decorator runs before the command function, potentially causing delays.
2. **Database Lock Conflicts**: Concurrent Supabase sync operations could lock the SQLite database, causing the autosetup command to hang while waiting for database access.
3. **No Database Timeout**: SQLite connections without timeouts could hang indefinitely if the database is locked.
4. **No Error Handling**: Database errors weren't caught, potentially causing the command to fail silently or hang.

## Fixes Applied

### 1. Immediate Interaction Defer with <PERSON>rro<PERSON> Handling
```python
@bot.tree.command(name="autosetup", description="...")
@app_commands.checks.has_permissions(administrator=True)
async def autosetup(interaction: discord.Interaction):
    try:
        # Defer immediately to prevent interaction timeout
        await interaction.response.defer(ephemeral=True)
    except discord.InteractionResponse:
        # Interaction might have already been responded to
        pass
    except Exception as e:
        print(f"❌ Failed to defer autosetup interaction: {e}")
        try:
            await interaction.response.send_message("❌ Setup failed to start. Please try again.", ephemeral=True)
        except:
            pass
        return
```

### 2. Sync Conflict Detection
```python
# Check if Supabase sync is in progress - if so, warn user and exit
if hasattr(sync_supabase_data_on_startup, '_syncing') and sync_supabase_data_on_startup._syncing:
    await interaction.followup.send("⚠️ **Database sync in progress.** Please wait a few minutes and try again.", ephemeral=True)
    return
```

### 3. Database Operation Error Handling
Wrapped all database operations in try-catch blocks:
```python
try:
    bot.cursor.execute("INSERT OR IGNORE INTO guild_settings (guild_id) VALUES (?", (guild.id,))
    # ... other database operations
    bot.conn.commit()
    progress.append("✅ Operation successful")
except sqlite3.OperationalError as e:
    if "database is locked" in str(e).lower():
        progress.append("❌ Database is busy. Please try again in a moment.")
        return
    else:
        progress.append(f"❌ Database error: {e}")
except Exception as e:
    progress.append(f"❌ Unexpected error: {e}")
```

### 4. Database Connection Timeouts
Added 30-second timeouts to all SQLite connections:
```python
# Main bot connection
self.conn = sqlite3.connect(db_path, timeout=30.0)

# DatabaseAdapter connection  
self.sqlite_conn = sqlite3.connect('transaction_bot.db', timeout=30.0)

# Backup/sync connections
local_conn = sqlite3.connect('transaction_bot.db', timeout=30.0)
verify_conn = sqlite3.connect('transaction_bot.db', timeout=30.0)
```

## Expected Results
1. **No More 10062 Errors**: The interaction is deferred immediately, preventing Discord timeouts.
2. **Graceful Database Conflict Handling**: If a sync is running, users get a clear message to try again later.
3. **Better Error Reporting**: Database errors are caught and reported to users instead of causing silent failures.
4. **Improved Reliability**: Database operations won't hang indefinitely due to connection timeouts.

## Testing
To test the fix:
1. Run the bot with the updated code
2. Try the `/autosetup` command
3. It should either:
   - Complete successfully with progress updates, or
   - Show a clear error message if there are issues
4. It should **never** show the "Unknown interaction" error again

## Additional Notes
- The fix maintains full functionality while adding robustness
- Users will get clear feedback if database operations fail
- The sync-in-progress check prevents most database lock conflicts
- Connection timeouts prevent indefinite hangs
