import sqlite3
import os

# Check if database exists
db_path = 'transaction_bot.db'
if not os.path.exists(db_path):
    print(f"❌ Database file {db_path} not found")
    exit(1)

print("🔧 Adding missing columns to SQLite database...")

# Connect to database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

try:
    # Add transaction_log_channel to guild_settings
    try:
        cursor.execute("ALTER TABLE guild_settings ADD COLUMN transaction_log_channel INTEGER")
        print("✅ Added transaction_log_channel column to guild_settings")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("ℹ️ transaction_log_channel column already exists in guild_settings")
        else:
            print(f"❌ Error adding transaction_log_channel: {e}")
    
    # Add weekly_games_channel to slot_configs
    try:
        cursor.execute("ALTER TABLE slot_configs ADD COLUMN weekly_games_channel INTEGER")
        print("✅ Added weekly_games_channel column to slot_configs")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("ℹ️ weekly_games_channel column already exists in slot_configs")
        else:
            print(f"❌ Error adding weekly_games_channel: {e}")
    
    # Add score_channel to slot_configs (if missing)
    try:
        cursor.execute("ALTER TABLE slot_configs ADD COLUMN score_channel INTEGER")
        print("✅ Added score_channel column to slot_configs")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("ℹ️ score_channel column already exists in slot_configs")
        else:
            print(f"❌ Error adding score_channel: {e}")
    
    # Add created_at to slot_configs (if missing)
    try:
        cursor.execute("ALTER TABLE slot_configs ADD COLUMN created_at TEXT")
        print("✅ Added created_at column to slot_configs")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("ℹ️ created_at column already exists in slot_configs")
        else:
            print(f"❌ Error adding created_at: {e}")
    
    # Add updated_at to slot_configs (if missing)
    try:
        cursor.execute("ALTER TABLE slot_configs ADD COLUMN updated_at TEXT")
        print("✅ Added updated_at column to slot_configs")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("ℹ️ updated_at column already exists in slot_configs")
        else:
            print(f"❌ Error adding updated_at: {e}")
    
    # Add guild_id to teams (if missing)
    try:
        cursor.execute("ALTER TABLE teams ADD COLUMN guild_id INTEGER")
        print("✅ Added guild_id column to teams")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("ℹ️ guild_id column already exists in teams")
        else:
            print(f"❌ Error adding guild_id: {e}")
    
    # Commit changes
    conn.commit()
    print("✅ All changes committed successfully")
    
    # Verify the schema after changes
    print("\n🔍 Verifying updated schema...")
    
    # Check slot_configs
    cursor.execute("PRAGMA table_info(slot_configs)")
    columns = cursor.fetchall()
    has_weekly_games = any(col[1] == 'weekly_games_channel' for col in columns)
    has_score = any(col[1] == 'score_channel' for col in columns)
    has_created = any(col[1] == 'created_at' for col in columns)
    has_updated = any(col[1] == 'updated_at' for col in columns)
    
    print(f"slot_configs - weekly_games_channel: {has_weekly_games}")
    print(f"slot_configs - score_channel: {has_score}")
    print(f"slot_configs - created_at: {has_created}")
    print(f"slot_configs - updated_at: {has_updated}")
    
    # Check guild_settings
    cursor.execute("PRAGMA table_info(guild_settings)")
    columns = cursor.fetchall()
    has_transaction_log = any(col[1] == 'transaction_log_channel' for col in columns)
    
    print(f"guild_settings - transaction_log_channel: {has_transaction_log}")
    
    # Check teams
    cursor.execute("PRAGMA table_info(teams)")
    columns = cursor.fetchall()
    has_guild_id = any(col[1] == 'guild_id' for col in columns)
    
    print(f"teams - guild_id: {has_guild_id}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    conn.rollback()
finally:
    conn.close()

print("\n✅ Schema update completed")
