#!/usr/bin/env python3
"""
Remove Duplicate Teams from Supabase

This script removes duplicate team entries from Supabase, keeping only one copy of each unique team.
"""

import sys

def remove_duplicate_teams():
    """
    Remove duplicate teams from Supabase database, keeping only the first occurrence of each unique team.
    """
    
    try:
        from supabase import create_client
        
        # Your Supabase credentials
        SUPABASE_URL = "https://ueheoabegwccfkholyku.supabase.co"
        SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlaGVvYWJlZ3djY2ZraG9seWt1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDM0MDEsImV4cCI6MjA2NzUxOTQwMX0.puxraTE7UGm9wkfqQvTxIp7NAINODFhMOXmgFpRPBKo"
        
        print("🔗 Connecting to Supabase...")
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
    except ImportError:
        print("❌ Supabase package not available. Install with: pip install supabase")
        return
    except Exception as e:
        print(f"❌ Error connecting to Supabase: {e}")
        return
    
    print("🧹 Starting duplicate team removal...")
    
    try:
        # Get all teams from Supabase
        print("📋 Fetching all teams from database...")
        teams_response = supabase.table('league_teams').select('*').execute()
        all_teams = teams_response.data
        
        print(f"📊 Total teams found: {len(all_teams)}")
        
        # Group teams by unique identifiers (guild_id + role_id + team_name)
        seen_teams = {}
        duplicates_to_delete = []
        
        for team in all_teams:
            # Check if required fields exist
            if 'role_id' not in team or 'team_name' not in team:
                print(f"  ⚠️  Skipping team with missing fields: {team}")
                continue
                
            # Create unique key for each team (use role_id + team_name since some might not have guild_id)
            guild_id = team.get('guild_id', 'unknown')
            unique_key = f"{guild_id}_{team['role_id']}_{team['team_name']}"
            
            if unique_key in seen_teams:
                # This is a duplicate - mark for deletion
                duplicates_to_delete.append(team)
                print(f"  🔍 Found duplicate: {team['team_name']} (Role ID: {team['role_id']})")
            else:
                # First occurrence - keep this one
                seen_teams[unique_key] = team
                print(f"  ✅ Keeping: {team['team_name']} (Role ID: {team['role_id']})")
        
        print(f"\n📊 Analysis:")
        print(f"  • Unique teams to keep: {len(seen_teams)}")
        print(f"  • Duplicate teams to delete: {len(duplicates_to_delete)}")
        
        if len(duplicates_to_delete) == 0:
            print("🎉 No duplicates found! Your database is already clean.")
            return
        
        # Delete duplicates
        print(f"\n🗑️  Deleting {len(duplicates_to_delete)} duplicate entries...")
        deleted_count = 0
        
        for duplicate in duplicates_to_delete:
            try:
                # Delete using role_id and team_name as primary identifiers
                delete_conditions = {
                    'role_id': duplicate['role_id'], 
                    'team_name': duplicate['team_name']
                }
                
                # Add guild_id if it exists
                if 'guild_id' in duplicate and duplicate['guild_id']:
                    delete_conditions['guild_id'] = duplicate['guild_id']
                    
                # Add slot_id if it exists  
                if 'slot_id' in duplicate and duplicate['slot_id']:
                    delete_conditions['slot_id'] = duplicate['slot_id']
                
                delete_response = supabase.table('league_teams').delete().match(delete_conditions).execute()
                
                deleted_count += 1
                print(f"  🗑️  Deleted duplicate: {duplicate['team_name']}")
                
            except Exception as e:
                print(f"  ❌ Error deleting duplicate {duplicate['team_name']}: {e}")
        
        print(f"\n✅ Duplicate removal completed!")
        print(f"📊 Final Results:")
        print(f"  • Duplicates deleted: {deleted_count}")
        print(f"  • Unique teams remaining: {len(seen_teams)}")
        
        # Verify the cleanup
        print("\n🔍 Verifying cleanup...")
        final_teams_response = supabase.table('league_teams').select('*').execute()
        final_count = len(final_teams_response.data)
        
        print(f"  • Final team count: {final_count}")
        print(f"  • Expected team count: {len(seen_teams)}")
        
        if final_count == len(seen_teams):
            print("  ✅ Cleanup verification successful!")
        else:
            print("  ⚠️  Warning: Final count doesn't match expected count")
        
        print("\n⚠️  Next steps:")
        print("1. Run /sync_data in Discord to pull the cleaned data to your bot")
        print("2. Test /list_teams to verify it works correctly")
        
    except Exception as e:
        print(f"❌ Error during duplicate removal: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧹 Supabase Duplicate Team Removal Tool")
    print("=" * 50)
    print("This will remove duplicate team entries from your Supabase database.")
    print("Only one copy of each unique team will be kept.")
    print()
    
    # Run automatically (since we know there are duplicates)
    print("🚀 Starting automatic duplicate removal...")
    remove_duplicate_teams()
