#!/usr/bin/env python3
"""
Simple Supabase Setup Script for Discord Bot
This script helps you set up Supabase database for your Discord bot.
"""

import os
import sys

def main():
    print("🤖 Discord Bot - Supabase Setup Helper")
    print("=" * 50)
    
    print("\n1. First, create a Supabase project at https://supabase.com")
    print("2. Go to Settings > API to get your credentials")
    
    # Get user input for Supabase credentials
    supabase_url = input("\n📍 Enter your Supabase Project URL: ").strip()
    supabase_key = input("🔑 Enter your Supabase anon/service_role key: ").strip()
    
    if not supabase_url or not supabase_key:
        print("❌ Error: Both URL and key are required!")
        return
    
    # Create .env file
    env_content = f"""# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_bot_token_here

# Database Configuration
DATABASE_TYPE=supabase
SUPABASE_URL={supabase_url}
SUPABASE_KEY={supabase_key}

# Optional: Set to 'sqlite' to use local database instead
# DATABASE_TYPE=sqlite
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ Created .env file with Supabase configuration")
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return
    
    print("\n📋 Next steps:")
    print("1. Run the SQL migration script in your Supabase SQL Editor:")
    print("   - Go to your Supabase dashboard")
    print("   - Click on 'SQL Editor'")
    print("   - Copy and paste the contents of 'supabase_migration.sql'")
    print("   - Click 'Run' to create all tables")
    
    print("\n2. Install Python dependencies:")
    print("   pip install -r requirements.txt")
    
    print("\n3. Set your Discord bot token in the .env file")
    print("   Replace 'your_bot_token_here' with your actual bot token")
    
    print("\n4. Run your bot:")
    print('   python "import sqlite3.py"')
    
    print("\n🎉 Setup complete! Your bot will now use Supabase database.")
    print("   If Supabase connection fails, it will automatically fall back to SQLite.")

if __name__ == "__main__":
    main()
