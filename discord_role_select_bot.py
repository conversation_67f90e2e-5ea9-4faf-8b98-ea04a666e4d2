import discord
from discord import app_commands, ui
from discord.ext import commands
from discord.ui import But<PERSON>, View, Select, Modal, TextInput
from typing import List, Optional, Dict, Any
import asyncio
import os
import sys
import subprocess

# Check if required packages are installed
try:
    import discord
except ImportError:
    print("Discord.py is not installed. Installing now...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "discord.py"])
    print("Discord.py installed successfully!")
    import discord

# Bot configuration
TOKEN_PROMPT = """
Please enter your Discord bot token:
(You can find this in the Discord Developer Portal: https://discord.com/developers/applications)
> """

# Ask for token if not provided
if len(sys.argv) > 1:
    DISCORD_TOKEN = sys.argv[1]
else:
    DISCORD_TOKEN = input(TOKEN_PROMPT)

# Create bot with necessary intents
intents = discord.Intents.default()
intents.message_content = True
intents.members = True  # Required for role-related functionality
bot = commands.Bot(command_prefix='!', intents=intents)

#########################################
# Role Autocomplete Implementation
#########################################

# Autocomplete function for roles
async def role_autocomplete(interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
    # Get all roles in the guild
    roles = interaction.guild.roles
    
    # Filter roles based on the current input (case-insensitive)
    if current:
        matching_roles = [
            role for role in roles 
            if current.lower() in role.name.lower() and not role.is_default()
        ]
    else:
        # If no input yet, return some roles (excluding @everyone)
        matching_roles = [role for role in roles if not role.is_default()][:25]
    
    # Sort by relevance (exact matches first, then starts with, then contains)
    exact_matches = [role for role in matching_roles if role.name.lower() == current.lower()]
    starts_with = [role for role in matching_roles if role.name.lower().startswith(current.lower()) and role not in exact_matches]
    contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
    
    sorted_roles = exact_matches + starts_with + contains
    
    # Return up to 25 choices (Discord's limit)
    return [
        app_commands.Choice(name=role.name, value=str(role.id))
        for role in sorted_roles[:25]
    ]

# Command that uses the autocomplete
@bot.tree.command(name="mention_role", description="Mention a role by typing its name")
@app_commands.autocomplete(role=role_autocomplete)
async def mention_role(interaction: discord.Interaction, role: str):
    # Get the role from the ID
    role_obj = interaction.guild.get_role(int(role))
    
    if role_obj:
        await interaction.response.send_message(f"You selected: {role_obj.mention}")
    else:
        await interaction.response.send_message("Role not found.", ephemeral=True)

# Command to send a message with role mention
@bot.tree.command(name="send_with_role", description="Send a message with a role mention")
@app_commands.autocomplete(role=role_autocomplete)
async def send_with_role(interaction: discord.Interaction, role: str, message: str):
    # Get the role from the ID
    role_obj = interaction.guild.get_role(int(role))
    
    if role_obj:
        await interaction.response.send_message(f"{message} {role_obj.mention}")
    else:
        await interaction.response.send_message("Role not found.", ephemeral=True)

#########################################
# Role Select UI Implementation
#########################################

# Modal with text input for role selection
class RoleSelectModal(discord.ui.Modal):
    def __init__(self, title="Select a Role"):
        super().__init__(title=title)
        
        # Add a text input for the role name
        self.role_input = discord.ui.TextInput(
            label="Start typing a role name",
            placeholder="Type to search for roles...",
            required=True,
            min_length=1,
            max_length=100
        )
        self.add_item(self.role_input)
        
        # Store the selected role ID
        self.selected_role_id = None
    
    async def on_submit(self, interaction: discord.Interaction):
        # This will be called when the user submits the modal
        # We'll handle this in the command
        pass

# Select menu for roles
class RoleSelectView(discord.ui.View):
    def __init__(self, roles, original_interaction):
        super().__init__(timeout=60)
        self.original_interaction = original_interaction
        self.selected_role_id = None
        
        # Create a select menu with the roles
        self.select = discord.ui.Select(
            placeholder="Select a role",
            min_values=1,
            max_values=1,
            options=[
                discord.SelectOption(
                    label=role.name,
                    value=str(role.id),
                    description=f"Members: {len(role.members)}"
                ) for role in roles[:25]  # Discord limits to 25 options
            ]
        )
        
        # Set the callback
        self.select.callback = self.select_callback
        self.add_item(self.select)
        
        # Add a cancel button
        self.cancel_button = discord.ui.Button(
            style=discord.ButtonStyle.secondary,
            label="Cancel",
            custom_id="cancel"
        )
        self.cancel_button.callback = self.cancel_callback
        self.add_item(self.cancel_button)
    
    async def select_callback(self, interaction: discord.Interaction):
        self.selected_role_id = self.select.values[0]
        # Get the role object
        role = interaction.guild.get_role(int(self.selected_role_id))
        
        if role:
            await interaction.response.send_message(f"You selected: {role.mention}", ephemeral=True)
            # Stop listening for interactions
            self.stop()
        else:
            await interaction.response.send_message("Role not found.", ephemeral=True)
    
    async def cancel_callback(self, interaction: discord.Interaction):
        await interaction.response.send_message("Selection cancelled.", ephemeral=True)
        self.stop()

# View with a button to open the role search modal
class RoleSearchView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=180)
    
    @discord.ui.button(label="Search Roles", style=discord.ButtonStyle.primary)
    async def search_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Create and show the modal
        modal = RoleSelectModal()
        await interaction.response.send_modal(modal)
        
        # Wait for the modal to be submitted
        await modal.wait()
        
        # After submission, filter roles based on the input
        search_term = modal.role_input.value.lower()
        roles = interaction.guild.roles
        
        # Filter and sort roles
        matching_roles = [role for role in roles if search_term in role.name.lower() and not role.is_default()]
        
        # Sort by relevance
        exact_matches = [role for role in matching_roles if role.name.lower() == search_term]
        starts_with = [role for role in matching_roles if role.name.lower().startswith(search_term) and role not in exact_matches]
        contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
        
        sorted_roles = exact_matches + starts_with + contains
        
        if sorted_roles:
            # Show the select menu with matching roles
            view = RoleSelectView(sorted_roles, interaction)
            await interaction.followup.send(
                f"Found {len(sorted_roles)} matching roles. Select one:",
                view=view,
                ephemeral=True
            )
        else:
            await interaction.followup.send("No matching roles found.", ephemeral=True)

@bot.tree.command(name="select_role", description="Select a role using a dynamic menu")
async def select_role(interaction: discord.Interaction):
    await interaction.response.send_message(
        "Click the button below to search for a role:",
        view=RoleSearchView(),
        ephemeral=True
    )

# Command to send a message with a role mention using the UI
@bot.tree.command(name="message_with_role", description="Send a message mentioning a role")
@app_commands.describe(message="The message to send")
async def message_with_role(interaction: discord.Interaction, message: str):
    # Create a custom view for this specific command
    class MessageRoleView(discord.ui.View):
        def __init__(self, message_content):
            super().__init__(timeout=180)
            self.message_content = message_content
        
        @discord.ui.button(label="Add Role Mention", style=discord.ButtonStyle.primary)
        async def add_role_button(self, interaction: discord.Interaction, button: discord.ui.Button):
            # Create and show the modal
            modal = RoleSelectModal(title="Select Role to Mention")
            await interaction.response.send_modal(modal)
            
            # Wait for the modal to be submitted
            await modal.wait()
            
            # After submission, filter roles based on the input
            search_term = modal.role_input.value.lower()
            roles = interaction.guild.roles
            
            # Filter and sort roles
            matching_roles = [role for role in roles if search_term in role.name.lower() and not role.is_default()]
            
            # Sort by relevance
            exact_matches = [role for role in matching_roles if role.name.lower() == search_term]
            starts_with = [role for role in matching_roles if role.name.lower().startswith(search_term) and role not in exact_matches]
            contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
            
            sorted_roles = exact_matches + starts_with + contains
            
            if sorted_roles:
                # Create a select menu for the matching roles
                class RoleMentionSelect(discord.ui.Select):
                    def __init__(self, roles, message_content):
                        self.message_content = message_content
                        options = [
                            discord.SelectOption(
                                label=role.name,
                                value=str(role.id),
                                description=f"Members: {len(role.members)}"
                            ) for role in roles[:25]
                        ]
                        super().__init__(placeholder="Select a role to mention", options=options)
                    
                    async def callback(self, interaction: discord.Interaction):
                        role_id = self.values[0]
                        role = interaction.guild.get_role(int(role_id))
                        
                        if role:
                            # Send the message with the role mention
                            await interaction.channel.send(f"{self.message_content} {role.mention}")
                            await interaction.response.send_message("Message sent with role mention!", ephemeral=True)
                        else:
                            await interaction.response.send_message("Role not found.", ephemeral=True)
                
                # Create a view with the select menu
                select_view = discord.ui.View(timeout=60)
                select_view.add_item(RoleMentionSelect(sorted_roles, self.message_content))
                
                await interaction.followup.send(
                    f"Found {len(sorted_roles)} matching roles. Select one to mention:",
                    view=select_view,
                    ephemeral=True
                )
            else:
                await interaction.followup.send("No matching roles found.", ephemeral=True)
    
    # Send the initial message with the button
    await interaction.response.send_message(
        "Click the button below to add a role mention to your message:",
        view=MessageRoleView(message),
        ephemeral=True
    )

#########################################
# Realtime Role Search Implementation
#########################################

@bot.tree.command(name="mention_role_realtime", description="Search and mention a role with real-time results")
async def mention_role_realtime(interaction: discord.Interaction):
    # Defer the response to give us time to set up the UI
    await interaction.response.defer(ephemeral=True)
    
    # Create the initial view with an empty search box
    view = RealtimeRoleSearchView(interaction.guild)
    
    # Send the initial message with the view
    message = await interaction.followup.send(
        "Start typing to search for roles:",
        view=view,
        ephemeral=True
    )
    
    # Store the message for the view to update
    view.message = message
    
    # Wait for the view to finish (timeout or selection made)
    await view.wait()
    
    # Check if a role was selected
    if view.selected_role:
        role = interaction.guild.get_role(int(view.selected_role))
        if role:
            # Send a message with the selected role mention
            await interaction.channel.send(f"{interaction.user.mention} mentioned {role.mention}")
            await interaction.followup.send(f"You mentioned {role.mention}", ephemeral=True)
    else:
        # If no role was selected (timeout or cancelled)
        await interaction.followup.send("No role was selected.", ephemeral=True)

class RealtimeRoleSearchView(discord.ui.View):
    def __init__(self, guild):
        super().__init__(timeout=180)  # 3 minute timeout
        self.guild = guild
        self.message = None
        self.selected_role = None
        self.current_search = ""
        self.searching = False
        
        # Add the search input
        self.search_input = discord.ui.TextInput(
            label="Search for roles",
            placeholder="Type to search...",
            required=False,
            min_length=0,
            max_length=100,
            style=discord.TextStyle.short,
            custom_id="role_search_input"
        )
        
        # Add a search button
        self.search_button = discord.ui.Button(
            label="Search",
            style=discord.ButtonStyle.primary,
            custom_id="search_button"
        )
        self.search_button.callback = self.search_callback
        self.add_item(self.search_button)
        
        # Add a cancel button
        self.cancel_button = discord.ui.Button(
            style=discord.ButtonStyle.secondary,
            label="Cancel",
            custom_id="cancel"
        )
        self.cancel_button.callback = self.cancel_callback
        self.add_item(self.cancel_button)
    
    async def search_callback(self, interaction: discord.Interaction):
        # Create and show the modal with the search input
        modal = discord.ui.Modal(title="Search for Roles")
        modal.add_item(self.search_input)
        
        await interaction.response.send_modal(modal)
        
        # Wait for the modal to be submitted
        try:
            await asyncio.wait_for(modal.wait(), timeout=60.0)
        except asyncio.TimeoutError:
            return
        
        # Get the search term
        search_term = self.search_input.value
        
        # Update the results based on the search term
        await self.update_results(search_term, interaction)
    
    async def cancel_callback(self, interaction: discord.Interaction):
        await interaction.response.send_message("Search cancelled.", ephemeral=True)
        self.stop()
    
    async def update_results(self, search_term, interaction):
        """Update the results based on the search term"""
        # Prevent multiple concurrent searches
        if self.searching:
            return
        
        self.searching = True
        self.current_search = search_term
        
        # Get all roles in the guild
        roles = self.guild.roles
        
        # Filter roles based on the search term
        if search_term:
            matching_roles = [
                role for role in roles 
                if search_term.lower() in role.name.lower() and not role.is_default()
            ]
            
            # Sort by relevance
            exact_matches = [role for role in matching_roles if role.name.lower() == search_term.lower()]
            starts_with = [role for role in matching_roles if role.name.lower().startswith(search_term.lower()) and role not in exact_matches]
            contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
            
            sorted_roles = exact_matches + starts_with + contains
        else:
            # If no search term, show some default roles (excluding @everyone)
            sorted_roles = [role for role in roles if not role.is_default()][:25]
        
        # Create a select menu with the results
        if sorted_roles:
            # Create a select menu for the matching roles
            select = discord.ui.Select(
                placeholder="Select a role",
                options=[
                    discord.SelectOption(
                        label=role.name,
                        value=str(role.id),
                        description=f"Members: {len(role.members)}",
                        emoji="👥"
                    ) for role in sorted_roles[:25]  # Discord limits to 25 options
                ]
            )
            
            async def select_callback(interaction: discord.Interaction):
                role_id = select.values[0]
                self.selected_role = role_id
                role = interaction.guild.get_role(int(role_id))
                
                if role:
                    await interaction.response.send_message(f"You selected: {role.mention}", ephemeral=True)
                    self.stop()
                else:
                    await interaction.response.send_message("Role not found.", ephemeral=True)
            
            select.callback = select_callback
            
            # Create a new view with the select menu
            results_view = discord.ui.View(timeout=60)
            results_view.add_item(select)
            
            # Add a back button
            back_button = discord.ui.Button(
                style=discord.ButtonStyle.secondary,
                label="Back to Search",
                custom_id="back"
            )
            
            async def back_callback(interaction: discord.Interaction):
                # Go back to the search view
                await interaction.response.edit_message(
                    content="Start typing to search for roles:",
                    view=self
                )
            
            back_button.callback = back_callback
            results_view.add_item(back_button)
            
            # Update the message with the results view
            await interaction.response.edit_message(
                content=f"Search results for '{search_term}' ({len(sorted_roles)} found):",
                view=results_view
            )
        else:
            # No results found
            await interaction.response.edit_message(
                content=f"No roles found matching '{search_term}'. Try a different search term.",
                view=self
            )
        
        self.searching = False

# Alternative implementation using a modal with real-time updates
class RoleSearchModal(discord.ui.Modal):
    def __init__(self, guild):
        super().__init__(title="Search for Roles")
        self.guild = guild
        
        # Add a text input for the search
        self.search_input = discord.ui.TextInput(
            label="Start typing to search for roles",
            placeholder="Type role name...",
            required=False,
            min_length=0,
            max_length=100
        )
        self.add_item(self.search_input)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Get the search term
        search_term = self.search_input.value
        
        # Filter roles based on the search term
        roles = self.guild.roles
        matching_roles = [
            role for role in roles 
            if search_term.lower() in role.name.lower() and not role.is_default()
        ]
        
        # Sort by relevance
        exact_matches = [role for role in matching_roles if role.name.lower() == search_term.lower()]
        starts_with = [role for role in matching_roles if role.name.lower().startswith(search_term.lower()) and role not in exact_matches]
        contains = [role for role in matching_roles if role not in exact_matches and role not in starts_with]
        
        sorted_roles = exact_matches + starts_with + contains
        
        # Create a select menu with the results
        view = discord.ui.View(timeout=60)
        
        if sorted_roles:
            # Add a select menu with the results
            select = discord.ui.Select(
                placeholder="Select a role",
                options=[
                    discord.SelectOption(
                        label=role.name,
                        value=str(role.id),
                        description=f"Members: {len(role.members)}"
                    ) for role in sorted_roles[:25]  # Discord limits to 25 options
                ]
            )
            
            async def select_callback(interaction: discord.Interaction):
                role_id = select.values[0]
                role = interaction.guild.get_role(int(role_id))
                
                if role:
                    await interaction.response.send_message(f"You selected: {role.mention}", ephemeral=True)
                else:
                    await interaction.response.send_message("Role not found.", ephemeral=True)
            
            select.callback = select_callback
            view.add_item(select)
            
            await interaction.response.send_message(
                f"Found {len(sorted_roles)} matching roles for '{search_term}':",
                view=view,
                ephemeral=True
            )
        else:
            await interaction.response.send_message(f"No roles found matching '{search_term}'.", ephemeral=True)

@bot.tree.command(name="search_role_modal", description="Search for a role using a modal")
async def search_role_modal(interaction: discord.Interaction):
    # Create and show the modal
    modal = RoleSearchModal(interaction.guild)
    await interaction.response.send_modal(modal)

#########################################
# Bot Setup and Event Handlers
#########################################

@bot.event
async def on_ready():
    print(f"Logged in as {bot.user.name} (ID: {bot.user.id})")
    
    # Sync commands with Discord
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")
        print(f"Error details: {str(e)}")

@bot.tree.command(name="ping", description="Check if the bot is responsive")
async def ping(interaction: discord.Interaction):
    await interaction.response.send_message("Pong! Bot is online and responsive.")

# Run the bot
if __name__ == "__main__":
    print("Starting Discord Role Select Bot...")
    print(f"Bot will be using token: {DISCORD_TOKEN[:5]}...{DISCORD_TOKEN[-5:]}")
    print("Once the bot is running, you can use the following commands:")
    print("  /ping - Check if the bot is responsive")
    print("  /mention_role - Select a role using Discord's autocomplete")
    print("  /select_role - Select a role using an interactive UI")
    print("  /mention_role_realtime - Search for roles with real-time results")
    print("  /send_with_role - Send a message with a role mention")
    print("  /message_with_role - Send a message with a role mention using the UI")
    print("  /search_role_modal - Search for a role using a modal")
    print("\nPress Ctrl+C to stop the bot")
    
    try:
        bot.run(DISCORD_TOKEN)
    except discord.errors.LoginFailure:
        print("\nError: Invalid Discord token. Please check your token and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError: {str(e)}")
        sys.exit(1)
