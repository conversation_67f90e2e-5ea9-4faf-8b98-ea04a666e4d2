#!/usr/bin/env python3
"""
Automated fix script for Supabase compatibility
This script will replace all bot.cursor.execute() calls with bot.db.execute() calls
and bot.conn.commit() calls with bot.db.commit() calls
"""

import re
import os

def apply_database_fixes(file_path):
    """Apply all database compatibility fixes to the file"""
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    print(f"🔧 Applying database compatibility fixes to {file_path}")
    
    # Fix 1: Replace bot.cursor.execute() with bot.db.execute()
    # Pattern: bot.cursor.execute( -> bot.db.execute(
    cursor_execute_pattern = r'bot\.cursor\.execute\('
    content = re.sub(cursor_execute_pattern, 'bot.db.execute(', content)
    
    # Fix 2: Replace bot.conn.commit() with bot.db.commit()
    conn_commit_pattern = r'bot\.conn\.commit\(\)'
    content = re.sub(conn_commit_pattern, 'bot.db.commit()', content)
    
    # Fix 3: Replace bot.cursor.fetchone() with safe_get_first_row(result)
    # This is more complex as we need to track the result variable
    fetchone_pattern = r'(\w+) = bot\.cursor\.fetchone\(\)'
    content = re.sub(fetchone_pattern, r'\1 = safe_get_first_row(result)', content)
    
    # Fix 4: Replace bot.cursor.fetchall() with result (since execute now returns the result)
    fetchall_pattern = r'(\w+) = bot\.cursor\.fetchall\(\)'
    content = re.sub(fetchall_pattern, r'\1 = result', content)
    
    # Fix 5: Replace standalone bot.cursor.fetchone() calls
    standalone_fetchone = r'bot\.cursor\.fetchone\(\)'
    content = re.sub(standalone_fetchone, 'safe_get_first_row(result)', content)
    
    # Fix 6: Replace standalone bot.cursor.fetchall() calls
    standalone_fetchall = r'bot\.cursor\.fetchall\(\)'
    content = re.sub(standalone_fetchall, 'result', content)
    
    # Fix 7: Fix patterns where execute and fetch are on separate lines
    # Replace patterns like:
    # bot.db.execute("query", params)
    # result = safe_get_first_row(result)
    # with:
    # result = bot.db.execute("query", params)
    # data = safe_get_first_row(result)
    
    execute_fetchone_pattern = r'bot\.db\.execute\((.*?)\)\s*\n\s*(\w+) = safe_get_first_row\(result\)'
    content = re.sub(execute_fetchone_pattern, r'result = bot.db.execute(\1)\n    \2 = safe_get_first_row(result)', content, flags=re.MULTILINE)
    
    # Fix 8: Handle cases where we need to assign the result
    # Pattern: bot.db.execute("SELECT... -> result = bot.db.execute("SELECT...
    select_pattern = r'^(\s*)bot\.db\.execute\(("SELECT[^"]*"[^)]*)\)$'
    content = re.sub(select_pattern, r'\1result = bot.db.execute(\2)', content, flags=re.MULTILINE)
    
    # Fix 9: Handle INSERT OR IGNORE patterns that might need special handling
    insert_ignore_pattern = r'bot\.db\.execute\(("INSERT OR IGNORE[^"]*")[^)]*\)'
    
    # Count changes made
    cursor_changes = len(re.findall(r'bot\.db\.execute\(', content)) - len(re.findall(r'bot\.db\.execute\(', original_content))
    commit_changes = len(re.findall(r'bot\.db\.commit\(\)', content)) - len(re.findall(r'bot\.db\.commit\(\)', original_content))
    
    print(f"   ✅ Replaced {abs(cursor_changes)} cursor.execute() calls")
    print(f"   ✅ Replaced {abs(commit_changes)} conn.commit() calls")
    
    # Write the fixed content back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return content != original_content

def add_improved_database_adapter(file_path):
    """Add the improved database adapter to the file"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the existing DatabaseAdapter class and replace it
    adapter_start = content.find('class DatabaseAdapter:')
    if adapter_start == -1:
        print("❌ Could not find DatabaseAdapter class")
        return False
    
    # Find the end of the class (next class or significant dedent)
    lines = content[adapter_start:].split('\n')
    class_end_line = 0
    indent_level = None
    
    for i, line in enumerate(lines):
        if i == 0:  # First line is the class definition
            continue
        
        # Skip empty lines and comments
        if not line.strip() or line.strip().startswith('#'):
            continue
        
        # Determine indent level from first non-empty line
        if indent_level is None and line.strip():
            indent_level = len(line) - len(line.lstrip())
            continue
        
        # Check if we've reached the end of the class
        if line.strip() and (len(line) - len(line.lstrip())) <= indent_level and not line.startswith(' '):
            # We've reached a line that's at the same level or less indented than the class
            class_end_line = i
            break
    
    if class_end_line == 0:
        # Class extends to end of file
        class_end_line = len(lines)
    
    adapter_end = adapter_start + sum(len(line) + 1 for line in lines[:class_end_line])
    
    # Read the improved adapter from our fixes file
    improved_adapter = '''
class DatabaseAdapter:
    """Enhanced database adapter with better Supabase support and error handling"""
    
    def __init__(self):
        self.db_type = DATABASE_TYPE_SETTING
        self.supabase_client = None
        self.sqlite_conn = None
        self.sqlite_cursor = None
        self.retry_count = 3
        self.retry_delay = 1.0
        
        print(f"🗄️ Database Type: {self.db_type}")
        
        if self.db_type == 'supabase' and SUPABASE_AVAILABLE:
            self.setup_supabase()
        else:
            self.setup_sqlite()
    
    def setup_supabase(self):
        """Initialize Supabase connection with better error handling"""
        if not SUPABASE_URL_SETTING or not SUPABASE_KEY_SETTING:
            print("⚠️ Supabase URL or KEY not configured")
            print("   Please add your Supabase credentials at the top of the file")
            print("   Falling back to SQLite...")
            self.db_type = 'sqlite'
            self.setup_sqlite()
            return
        
        try:
            from supabase import create_client
            self.supabase_client = create_client(SUPABASE_URL_SETTING, SUPABASE_KEY_SETTING)
            print("✅ Connected to Supabase database")
            # Test connection
            self.supabase_client.table('guild_settings').select('*').limit(1).execute()
            print("✅ Supabase database connection verified")
        except Exception as e:
            print(f"❌ Failed to connect to Supabase: {e}")
            print("   Falling back to SQLite...")
            self.db_type = 'sqlite'
            self.setup_sqlite()
    
    def setup_sqlite(self):
        """Initialize SQLite connection"""
        try:
            self.sqlite_conn = sqlite3.connect('transaction_bot.db', timeout=30.0)
            self.sqlite_cursor = self.sqlite_conn.cursor()
            print("✅ Connected to SQLite database")
        except Exception as e:
            print(f"❌ Failed to connect to SQLite: {e}")
            raise
    
    def execute(self, query: str, params: tuple = None):
        """Execute a query with retry logic - handles both SQLite and Supabase"""
        for attempt in range(self.retry_count):
            try:
                if self.db_type == 'supabase':
                    return self.execute_supabase(query, params)
                else:
                    return self.execute_sqlite(query, params)
            except Exception as e:
                if attempt == self.retry_count - 1:
                    print(f"❌ Database operation failed after {self.retry_count} attempts: {e}")
                    print(f"   Query: {query}")
                    print(f"   Params: {params}")
                    raise
                print(f"⚠️ Database operation failed (attempt {attempt + 1}/{self.retry_count}): {e}")
                import time
                time.sleep(self.retry_delay * (attempt + 1))
    
    def execute_sqlite(self, query: str, params: tuple = None):
        """Execute SQLite query"""
        try:
            if params:
                self.sqlite_cursor.execute(query, params)
            else:
                self.sqlite_cursor.execute(query)
            return self.sqlite_cursor.fetchall()
        except Exception as e:
            print(f"SQLite error: {e}")
            raise
    
    def execute_supabase(self, query: str, params: tuple = None):
        """Execute Supabase query (converts SQL to Supabase API calls)"""
        try:
            query_upper = query.upper().strip()
            
            if query_upper.startswith('SELECT'):
                return self.handle_select_query(query, params)
            elif query_upper.startswith('INSERT'):
                return self.handle_insert_query(query, params)
            elif query_upper.startswith('UPDATE'):
                return self.handle_update_query(query, params)
            elif query_upper.startswith('DELETE'):
                return self.handle_delete_query(query, params)
            elif query_upper.startswith('CREATE TABLE'):
                # Skip table creation for Supabase (tables should be created via migration)
                print(f"Skipping table creation for Supabase: {query}")
                return []
            elif query_upper.startswith('PRAGMA'):
                # Skip SQLite PRAGMA statements for Supabase
                return []
            else:
                print(f"Unsupported query type for Supabase: {query}")
                return []
        except Exception as e:
            print(f"Supabase error: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            raise
    
    def handle_select_query(self, query: str, params: tuple = None):
        """Handle SELECT queries for Supabase - simplified version"""
        query_lower = query.lower()
        
        # Extract table name
        if 'from guild_settings' in query_lower:
            table = self.supabase_client.table('guild_settings')
            if params and 'where guild_id = ?' in query_lower:
                result = table.select('*').eq('guild_id', str(params[0])).execute()
                return [(row.get('guild_id'), row.get('admin_role'), row.get('log_channel'),
                       row.get('application_channel'), row.get('suspended_role'), row.get('suspended_channel'),
                       row.get('application_blacklist_role'), row.get('transaction_log_channel')) for row in result.data]
        
        elif 'from slots' in query_lower:
            table = self.supabase_client.table('slots')
            if params:
                if 'where guild_id = ?' in query_lower:
                    if 'and (slot_id = ? or slot_name = ?)' in query_lower:
                        result = table.select('*').eq('guild_id', str(params[0])).or_(f'slot_id.eq.{params[1]},slot_name.eq.{params[2]}').execute()
                    elif 'and is_default = 1' in query_lower:
                        result = table.select('*').eq('guild_id', str(params[0])).eq('is_default', 1).execute()
                    else:
                        result = table.select('*').eq('guild_id', str(params[0])).execute()
                elif 'where slot_id = ?' in query_lower:
                    result = table.select('*').eq('slot_id', params[0]).execute()
                else:
                    result = table.select('*').execute()
            else:
                result = table.select('*').execute()
            return [(row.get('slot_id'), row.get('guild_id'), row.get('slot_name'), 
                   row.get('description'), row.get('is_default', 0)) for row in result.data]
        
        # Add more table handlers as needed
        elif 'from league_teams' in query_lower:
            table = self.supabase_client.table('league_teams')
            if params and 'where guild_id = ? and slot_id = ?' in query_lower:
                result = table.select('*').eq('guild_id', str(params[0])).eq('slot_id', params[1]).execute()
            elif params and 'where slot_id = ?' in query_lower:
                result = table.select('*').eq('slot_id', params[0]).execute()
            else:
                result = table.select('*').execute()
            return [(row.get('role_id'), row.get('slot_id'), row.get('team_name'), row.get('emoji')) for row in result.data]
        
        # Handle COUNT queries
        if 'select count(*)' in query_lower:
            table_name = None
            if 'from slots' in query_lower:
                table_name = 'slots'
            elif 'from league_teams' in query_lower:
                table_name = 'league_teams'
            elif 'from guild_settings' in query_lower:
                table_name = 'guild_settings'
            
            if table_name:
                table = self.supabase_client.table(table_name)
                result = table.select('*', count='exact').execute()
                return [(result.count,)]
        
        print(f"Unhandled SELECT query: {query}")
        return []
    
    def handle_insert_query(self, query: str, params: tuple = None):
        """Handle INSERT queries for Supabase - simplified version"""
        query_lower = query.lower()
        
        if 'into guild_settings' in query_lower and params:
            table = self.supabase_client.table('guild_settings')
            if 'insert or ignore' in query_lower:
                # Check if exists first
                existing = table.select('guild_id').eq('guild_id', str(params[0])).execute()
                if existing.data:
                    return []
            
            data = {'guild_id': str(params[0])}
            if len(params) > 1 and params[1]:
                data['admin_role'] = str(params[1])
            table.insert(data).execute()
        
        elif 'into slots' in query_lower and params:
            table = self.supabase_client.table('slots')
            data = {
                'slot_id': params[0],
                'guild_id': str(params[1]),
                'slot_name': params[2],
                'description': params[3] if len(params) > 3 else None,
                'is_default': params[4] if len(params) > 4 else 0
            }
            table.insert(data).execute()
        
        elif 'into league_teams' in query_lower and params:
            table = self.supabase_client.table('league_teams')
            data = {
                'role_id': str(params[0]),
                'slot_id': params[1],
                'team_name': params[2],
                'emoji': params[3] if len(params) > 3 else ''
            }
            table.insert(data).execute()
        
        # Add more INSERT handlers as needed
        else:
            print(f"Unhandled INSERT query: {query}")
        
        return []
    
    def handle_update_query(self, query: str, params: tuple = None):
        """Handle UPDATE queries for Supabase - simplified version"""
        query_lower = query.lower()
        
        if 'update guild_settings' in query_lower and params:
            table = self.supabase_client.table('guild_settings')
            # Simple UPDATE handling - you may need to enhance this
            guild_id = str(params[-1])  # Assume last param is guild_id
            update_data = {}
            # Add logic to parse SET clause based on your specific queries
            table.update(update_data).eq('guild_id', guild_id).execute()
        
        # Add more UPDATE handlers as needed
        else:
            print(f"Unhandled UPDATE query: {query}")
        
        return []
    
    def handle_delete_query(self, query: str, params: tuple = None):
        """Handle DELETE queries for Supabase - simplified version"""
        query_lower = query.lower()
        
        if 'from slots' in query_lower and params and 'where slot_id = ?' in query_lower:
            table = self.supabase_client.table('slots')
            table.delete().eq('slot_id', params[0]).execute()
        
        elif 'from league_teams' in query_lower and params:
            table = self.supabase_client.table('league_teams')
            if 'where slot_id = ?' in query_lower:
                table.delete().eq('slot_id', params[0]).execute()
            elif 'where role_id = ?' in query_lower:
                table.delete().eq('role_id', str(params[0])).execute()
        
        # Add more DELETE handlers as needed
        else:
            print(f"Unhandled DELETE query: {query}")
        
        return []
    
    def fetchone(self):
        """SQLite compatibility method"""
        if self.db_type == 'sqlite':
            return self.sqlite_cursor.fetchone()
        return None
    
    def fetchall(self):
        """SQLite compatibility method"""
        if self.db_type == 'sqlite':
            return self.sqlite_cursor.fetchall()
        return []
    
    def commit(self):
        """Commit changes - SQLite compatibility"""
        if self.db_type == 'sqlite':
            self.sqlite_conn.commit()
        # Supabase auto-commits
    
    def close(self):
        """Close connection"""
        if self.db_type == 'sqlite' and self.sqlite_conn:
            self.sqlite_conn.close()
'''
    
    # Replace the old adapter with the new one
    new_content = content[:adapter_start] + improved_adapter + content[adapter_end:]
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ Updated DatabaseAdapter class with improved Supabase support")
    return True

def main():
    """Main function to apply all fixes"""
    file_path = r'c:\Users\<USER>\Downloads\bot\import sqlite3.py'
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    print("🚀 Starting Supabase compatibility fixes...")
    
    # Apply database call fixes
    if apply_database_fixes(file_path):
        print("✅ Database call fixes applied")
    else:
        print("⚠️ No changes needed for database calls")
    
    # Add improved database adapter
    if add_improved_database_adapter(file_path):
        print("✅ Improved database adapter added")
    
    print("🎉 All fixes applied successfully!")
    print("\nNext steps:")
    print("1. Test the bot with SQLite first")
    print("2. Install Supabase: pip install supabase")
    print("3. Set up your Supabase database with the migration script")
    print("4. Change DATABASE_TYPE to 'supabase' in the config")
    print("5. Test the bot with Supabase")

if __name__ == "__main__":
    main()
