#!/usr/bin/env python3
"""
Complete Database Schema Fix Script for Discord Bot
This script fixes all known schema mismatches between Supabase and SQLite.

INSTRUCTIONS FOR CYBRANCEE HOSTING:
1. Upload this file to your bot's directory on Cybrancee
2. Run: python fix_database_schema.py
3. Restart your bot
4. The Supabase sync should now work without errors

This script will:
- Add missing columns to existing tables
- Verify all required tables exist
- Fix any schema mismatches
- Provide detailed feedback on what was changed
"""

import sqlite3
import os
import sys

def check_and_fix_guild_settings(cursor, conn):
    """Fix guild_settings table schema"""
    print("🔧 Checking guild_settings table...")
    
    # Check if table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='guild_settings'")
    if not cursor.fetchone():
        print("❌ guild_settings table doesn't exist - creating it...")
        cursor.execute('''
        CREATE TABLE guild_settings (
            guild_id INTEGER PRIMARY KEY,
            admin_role INTEGER,
            log_channel INTEGER,
            application_channel INTEGER,
            suspended_role INTEGER,
            suspended_channel INTEGER,
            application_blacklist_role INTEGER,
            transaction_log_channel INTEGER
        )''')
        print("✅ Created guild_settings table")
        return True
    
    # Check current columns
    cursor.execute("PRAGMA table_info(guild_settings)")
    columns = [column[1] for column in cursor.fetchall()]
    
    required_columns = {
        "guild_id": "INTEGER PRIMARY KEY",
        "admin_role": "INTEGER",
        "log_channel": "INTEGER", 
        "application_channel": "INTEGER",
        "suspended_role": "INTEGER",
        "suspended_channel": "INTEGER",
        "application_blacklist_role": "INTEGER",
        "transaction_log_channel": "INTEGER"
    }
    
    changes_made = False
    for col_name, col_type in required_columns.items():
        if col_name not in columns:
            if col_name != "guild_id":  # Can't add PRIMARY KEY column to existing table
                print(f"   Adding missing column: {col_name}")
                cursor.execute(f"ALTER TABLE guild_settings ADD COLUMN {col_name} {col_type}")
                changes_made = True
    
    if changes_made:
        conn.commit()
        print("✅ Fixed guild_settings table schema")
    else:
        print("✅ guild_settings table schema is correct")
    
    return changes_made

def check_and_fix_slots(cursor, conn):
    """Fix slots table schema"""
    print("🔧 Checking slots table...")
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='slots'")
    if not cursor.fetchone():
        print("❌ slots table doesn't exist - creating it...")
        cursor.execute('''
        CREATE TABLE slots (
            slot_id TEXT PRIMARY KEY,
            guild_id INTEGER NOT NULL,
            slot_name TEXT NOT NULL,
            description TEXT,
            is_default INTEGER DEFAULT 0,
            weeks_per_season INTEGER DEFAULT 17,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )''')
        print("✅ Created slots table")
        return True
    
    cursor.execute("PRAGMA table_info(slots)")
    columns = [column[1] for column in cursor.fetchall()]
    
    required_columns = {
        "weeks_per_season": "INTEGER DEFAULT 17"
    }
    
    changes_made = False
    for col_name, col_type in required_columns.items():
        if col_name not in columns:
            print(f"   Adding missing column: {col_name}")
            cursor.execute(f"ALTER TABLE slots ADD COLUMN {col_name} {col_type}")
            changes_made = True
    
    if changes_made:
        conn.commit()
        print("✅ Fixed slots table schema")
    else:
        print("✅ slots table schema is correct")
    
    return changes_made

def check_and_fix_slot_command_settings(cursor, conn):
    """Fix slot_command_settings table schema"""
    print("🔧 Checking slot_command_settings table...")
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='slot_command_settings'")
    if not cursor.fetchone():
        print("❌ slot_command_settings table doesn't exist - creating it...")
        cursor.execute('''
        CREATE TABLE slot_command_settings (
            slot_id TEXT PRIMARY KEY,
            guild_id TEXT NOT NULL,
            sign_enabled TEXT DEFAULT 'on',
            release_enabled TEXT DEFAULT 'on',
            offer_enabled TEXT DEFAULT 'on',
            roster_cap INTEGER DEFAULT 25,
            money_cap_enabled TEXT DEFAULT 'on',
            money_cap_amount INTEGER DEFAULT 0
        )''')
        print("✅ Created slot_command_settings table")
        return True
    
    cursor.execute("PRAGMA table_info(slot_command_settings)")
    columns = [column[1] for column in cursor.fetchall()]
    
    required_columns = {
        "guild_id": "TEXT NOT NULL",
        "sign_enabled": "TEXT DEFAULT 'on'",
        "release_enabled": "TEXT DEFAULT 'on'",
        "offer_enabled": "TEXT DEFAULT 'on'",
        "roster_cap": "INTEGER DEFAULT 25",
        "money_cap_enabled": "TEXT DEFAULT 'on'",
        "money_cap_amount": "INTEGER DEFAULT 0"
    }
    
    changes_made = False
    for col_name, col_type in required_columns.items():
        if col_name not in columns:
            print(f"   Adding missing column: {col_name}")
            # For NOT NULL columns, we need to provide a default
            if "NOT NULL" in col_type and col_name == "guild_id":
                cursor.execute(f"ALTER TABLE slot_command_settings ADD COLUMN {col_name} TEXT")
            else:
                cursor.execute(f"ALTER TABLE slot_command_settings ADD COLUMN {col_name} {col_type}")
            changes_made = True
    
    if changes_made:
        conn.commit()
        print("✅ Fixed slot_command_settings table schema")
    else:
        print("✅ slot_command_settings table schema is correct")
    
    return changes_made

def verify_database_integrity(cursor):
    """Verify all tables exist and have the expected basic structure"""
    print("🔍 Verifying database integrity...")
    
    required_tables = [
        'guild_settings',
        'slots', 
        'slot_configs',
        'slot_command_settings',
        'slot_demand_config',
        'league_teams',
        'contracts',
        'transactions',
        'applications',
        'gametimes',
        'disband_transactions',
        'live_management_lists'
    ]
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    existing_tables = [row[0] for row in cursor.fetchall()]
    
    missing_tables = []
    for table in required_tables:
        if table not in existing_tables:
            missing_tables.append(table)
    
    if missing_tables:
        print(f"⚠️ Missing tables: {missing_tables}")
        print("   These will be created when the bot runs setup_database()")
    else:
        print("✅ All required tables exist")

def main():
    print("🗄️ Database Schema Fix Script")
    print("=" * 60)
    print("This script will fix schema mismatches between Supabase and SQLite")
    print("that are causing sync errors.\n")
    
    db_path = 'transaction_bot.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        print("   Make sure you're running this script in the same directory as your bot.")
        print(f"   Current directory: {os.getcwd()}")
        print("   Files in current directory:")
        for file in os.listdir('.'):
            print(f"     {file}")
        return False
    
    try:
        print(f"🔗 Connecting to database: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Enable foreign key support
        cursor.execute("PRAGMA foreign_keys = ON")
        
        total_changes = 0
        
        # Fix each table schema
        total_changes += check_and_fix_guild_settings(cursor, conn)
        total_changes += check_and_fix_slots(cursor, conn)
        total_changes += check_and_fix_slot_command_settings(cursor, conn)
        
        # Verify overall integrity
        verify_database_integrity(cursor)
        
        # Final commit
        conn.commit()
        conn.close()
        
        print("\n" + "=" * 60)
        if total_changes > 0:
            print(f"✅ Database schema fix completed successfully!")
            print(f"📊 Made {total_changes} schema changes")
            print("🔄 Restart your bot - the Supabase sync should now work!")
        else:
            print("✅ Database schema was already correct!")
            print("🤔 If you're still getting sync errors, check the Supabase credentials.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during schema fix: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
