#!/usr/bin/env python3
"""
Test local SQLite database schema to ensure columns are properly set up.
"""

import sqlite3
import os

def check_local_database():
    print("🔍 Checking Local SQLite Database Schema")
    print("=" * 50)
    
    db_path = 'transaction_bot.db'
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} does not exist")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check guild_settings table
    print("\n📋 guild_settings table schema:")
    cursor.execute("PRAGMA table_info(guild_settings)")
    columns = cursor.fetchall()
    if columns:
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
    else:
        print("  ❌ Table does not exist")
    
    # Check if data exists
    cursor.execute("SELECT COUNT(*) FROM guild_settings")
    count = cursor.fetchone()[0]
    print(f"  📊 Records: {count}")
    
    if count > 0:
        cursor.execute("SELECT * FROM guild_settings LIMIT 1")
        sample = cursor.fetchone()
        print(f"  📄 Sample record: {sample}")
    
    conn.close()

if __name__ == "__main__":
    check_local_database()
