import discord
from discord import app_commands, ui
from discord.ext import commands
import sqlite3
import traceback
from datetime import datetime as DateTime, timezone as TimeZone
from typing import Optional, List
import random
import re

# Enhanced trade command that integrates with the slot-based database system

def get_emoji_url(emoji_str: Optional[str]) -> Optional[str]:
    """Extract custom emoji ID and convert to URL if possible."""
    if not emoji_str:
        return None
    custom_emoji_match = re.match(r'<a?:[\w]+:(\d+)>', emoji_str)
    if custom_emoji_match:
        emoji_id = custom_emoji_match.group(1)
        return f"https://cdn.discordapp.com/emojis/{emoji_id}.png"
    return None

def get_slot_config(guild_id, slot_id):
    """Get slot configuration from the transaction_bot.db database"""
    conn = None
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT * FROM slot_configs 
        WHERE guild_id = ? AND slot_id = ?
        ''', (guild_id, slot_id))
        
        row = cursor.fetchone()
        if row:
            columns = [description[0] for description in cursor.description]
            config_dict = dict(zip(columns, row))
            return config_dict
        return None
    except sqlite3.Error as e:
        print(f"Database error in get_slot_config: {e}")
        return None
    finally:
        if conn:
            conn.close()

def get_user_team_info(guild_id, user_id):
    """Get user's team information from the database"""
    conn = None
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        
        # Get user's team role from league_teams table
        cursor.execute('''
        SELECT lt.role_id, lt.slot_id, lt.team_name, lt.emoji, s.slot_name
        FROM league_teams lt
        JOIN slots s ON lt.slot_id = s.slot_id
        WHERE s.guild_id = ?
        ''', (guild_id,))
        
        teams = cursor.fetchall()
        
        # Check which team the user belongs to
        for role_id, slot_id, team_name, emoji, slot_name in teams:
            # This would need to be checked against the user's actual roles in Discord
            # For now, we'll return the structure and let the calling function handle role checking
            pass
            
        return teams
    except sqlite3.Error as e:
        print(f"Database error in get_user_team_info: {e}")
        return []
    finally:
        if conn:
            conn.close()

def is_authorized(member, slot_config):
    """Check if member has permission to perform trades based on slot configuration"""
    if not slot_config:
        return False
    
    # Get the list of authorized role IDs from slot configuration
    authorized_role_ids = [
        slot_config.get('franchise_owner_role'),
        slot_config.get('general_manager_role'),
        slot_config.get('head_coach_role')
    ]
    
    # Filter out None values and ensure they are integers if present
    valid_authorized_ids = [r_id for r_id in authorized_role_ids if r_id is not None]
    
    # Check if any of the member's roles match any of the valid authorized role IDs
    return any(role.id in valid_authorized_ids for role in member.roles)

def log_trade_transaction(guild_id, slot_id, trade_details):
    """Log the trade transaction to the database"""
    conn = None
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        
        # Insert trade record into transactions table
        cursor.execute('''
        INSERT INTO transactions (
            guild_id, slot_id, transaction_type, player_id, 
            from_team_role, to_team_role, timestamp, details
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            guild_id, slot_id, 'trade', 
            trade_details.get('player_id'),
            trade_details.get('from_team_role'),
            trade_details.get('to_team_role'),
            DateTime.now(TimeZone.utc).isoformat(),
            str(trade_details)
        ))
        
        conn.commit()
        print(f"Trade transaction logged for guild {guild_id}, slot {slot_id}")
    except sqlite3.Error as e:
        print(f"Database error in log_trade_transaction: {e}")
    finally:
        if conn:
            conn.close()

class TradeView(discord.ui.View):
    def __init__(self, original_interaction: discord.Interaction, your_players: List[discord.Member], 
                 other_players: List[discord.Member], your_team_role: discord.Role, 
                 other_team_role: discord.Role, your_team_emoji: str, other_team_emoji: str, 
                 your_assets: Optional[str], other_assets: Optional[str], slot_id: str):
        super().__init__(timeout=300)  # 5 minute timeout
        self.original_interaction = original_interaction
        self.your_players = your_players
        self.other_players = other_players
        self.your_team_role = your_team_role
        self.other_team_role = other_team_role
        self.your_team_emoji = your_team_emoji
        self.other_team_emoji = other_team_emoji
        self.your_assets = your_assets
        self.other_assets = other_assets
        self.slot_id = slot_id

    @discord.ui.button(label="Accept Trade", style=discord.ButtonStyle.green, emoji="✅")
    async def accept_trade(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            slot_config = get_slot_config(interaction.guild_id, self.slot_id)
            member = interaction.guild.get_member(interaction.user.id)
            
            # Check if the user accepting is authorized and is from the other_team
            if not is_authorized(member, slot_config) or self.other_team_role not in member.roles:
                await interaction.response.send_message(
                    "❌ You don't have permission to accept this trade or you are not part of the receiving team.",
                    ephemeral=True
                )
                return

            # Execute the trade (swap roles for all players)
            trade_details = {
                'type': 'multi_player_trade',
                'your_team': self.your_team_role.id,
                'other_team': self.other_team_role.id,
                'your_players': [p.id for p in self.your_players],
                'other_players': [p.id for p in self.other_players],
                'your_assets': self.your_assets,
                'other_assets': self.other_assets,
                'accepted_by': interaction.user.id
            }
            
            # Perform role swaps
            for player in self.your_players:
                await player.remove_roles(self.your_team_role, reason="Trade accepted")
                await player.add_roles(self.other_team_role, reason="Trade accepted")
                
                # Log individual player transaction
                log_trade_transaction(interaction.guild_id, self.slot_id, {
                    'player_id': player.id,
                    'from_team_role': self.your_team_role.id,
                    'to_team_role': self.other_team_role.id,
                    'trade_id': f"trade_{interaction.id}"
                })
            
            for player in self.other_players:
                await player.remove_roles(self.other_team_role, reason="Trade accepted")
                await player.add_roles(self.your_team_role, reason="Trade accepted")
                
                # Log individual player transaction
                log_trade_transaction(interaction.guild_id, self.slot_id, {
                    'player_id': player.id,
                    'from_team_role': self.other_team_role.id,
                    'to_team_role': self.your_team_role.id,
                    'trade_id': f"trade_{interaction.id}"
                })

            # Get updated team counts
            your_team_count = len([m for m in interaction.guild.members if self.your_team_role in m.roles])
            other_team_count = len([m for m in interaction.guild.members if self.other_team_role in m.roles])

            # Determine embed color
            team_colors = []
            if self.your_team_role.color != discord.Color.default():
                team_colors.append(self.your_team_role.color)
            if self.other_team_role.color != discord.Color.default() and self.other_team_role.color != self.your_team_role.color:
                team_colors.append(self.other_team_role.color)
            
            embed_color = random.choice(team_colors) if team_colors else discord.Color.green()

            # Create success embed
            success_embed = discord.Embed(
                title="🤝 Trade Completed!",
                description=f"Trade between {self.your_team_emoji} **{self.your_team_role.name}** and {self.other_team_emoji} **{self.other_team_role.name}** has been completed.",
                color=embed_color,
                timestamp=DateTime.now(TimeZone.utc)
            )
            
            # Add player details
            your_players_text = "\n".join([f"• {player.display_name}" for player in self.your_players])
            other_players_text = "\n".join([f"• {player.display_name}" for player in self.other_players])
            
            success_embed.add_field(
                name=f"{self.your_team_emoji} {self.your_team_role.name} → {self.other_team_emoji} {self.other_team_role.name}",
                value=your_players_text or "No players",
                inline=True
            )
            
            success_embed.add_field(
                name=f"{self.other_team_emoji} {self.other_team_role.name} → {self.your_team_emoji} {self.your_team_role.name}",
                value=other_players_text or "No players",
                inline=True
            )
            
            success_embed.add_field(name="\u200b", value="\u200b", inline=False)  # Empty field for spacing
            
            # Add assets if any
            if self.your_assets:
                success_embed.add_field(
                    name=f"{self.your_team_emoji} Additional Assets",
                    value=self.your_assets,
                    inline=True
                )
            
            if self.other_assets:
                success_embed.add_field(
                    name=f"{self.other_team_emoji} Additional Assets",
                    value=self.other_assets,
                    inline=True
                )
            
            # Add team counts
            success_embed.add_field(
                name="📊 Updated Team Sizes",
                value=f"{self.your_team_emoji} **{self.your_team_role.name}**: {your_team_count} members\n{self.other_team_emoji} **{self.other_team_role.name}**: {other_team_count} members",
                inline=False
            )
            
            success_embed.set_footer(text=f"Trade accepted by {interaction.user.display_name} • Slot: {self.slot_id}")
            
            # Disable all buttons
            for item in self.children:
                item.disabled = True
            
            await interaction.response.edit_message(embed=success_embed, view=self)
            
            # Send to trade channel if configured
            if slot_config and slot_config.get('trade_channel'):
                trade_channel = interaction.guild.get_channel(slot_config['trade_channel'])
                if trade_channel:
                    await trade_channel.send(embed=success_embed)
                    
        except Exception as e:
            print(f"Error in trade acceptance: {e}")
            traceback.print_exc()
            await interaction.response.send_message(
                "❌ An error occurred while processing the trade. Please try again.",
                ephemeral=True
            )

    @discord.ui.button(label="Decline Trade", style=discord.ButtonStyle.red, emoji="❌")
    async def decline_trade(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            slot_config = get_slot_config(interaction.guild_id, self.slot_id)
            member = interaction.guild.get_member(interaction.user.id)
            
            # Check if the user declining is authorized and is from the other_team
            if not is_authorized(member, slot_config) or self.other_team_role not in member.roles:
                await interaction.response.send_message(
                    "❌ You don't have permission to decline this trade or you are not part of the receiving team.",
                    ephemeral=True
                )
                return
            
            # Create decline embed
            decline_embed = discord.Embed(
                title="❌ Trade Declined",
                description=f"Trade between {self.your_team_emoji} **{self.your_team_role.name}** and {self.other_team_emoji} **{self.other_team_role.name}** has been declined.",
                color=discord.Color.red(),
                timestamp=DateTime.now(TimeZone.utc)
            )
            
            decline_embed.set_footer(text=f"Trade declined by {interaction.user.display_name}")
            
            # Disable all buttons
            for item in self.children:
                item.disabled = True
            
            await interaction.response.edit_message(embed=decline_embed, view=self)
            
        except Exception as e:
            print(f"Error in trade decline: {e}")
            traceback.print_exc()
            await interaction.response.send_message(
                "❌ An error occurred while declining the trade.",
                ephemeral=True
            )

    async def on_timeout(self):
        """Called when the view times out"""
        for item in self.children:
            item.disabled = True
        
        timeout_embed = discord.Embed(
            title="⏰ Trade Expired",
            description="This trade proposal has expired due to inactivity.",
            color=discord.Color.orange()
        )
        
        try:
            await self.original_interaction.edit_original_response(embed=timeout_embed, view=self)
        except:
            pass  # Message might have been deleted

# This would be added to your main bot file
def setup_trade_command(bot):
    """Setup function to add the trade command to the bot"""
    
    @bot.tree.command(name="trade", description="Propose a trade between teams")
    @app_commands.describe(
        your_players="Players from your team to trade (mention them)",
        other_players="Players from the other team you want (mention them)", 
        other_team="The team role you want to trade with",
        your_assets="Additional assets your team is offering (optional)",
        other_assets="Additional assets you want from the other team (optional)",
        slot="The slot/league this trade is for"
    )
    async def trade(interaction: discord.Interaction, 
                   your_players: str,
                   other_players: str, 
                   other_team: discord.Role,
                   your_assets: Optional[str] = None,
                   other_assets: Optional[str] = None,
                   slot: Optional[str] = None):
        
        await interaction.response.defer(ephemeral=True)
        
        try:
            # Get available slots for this guild
            conn = sqlite3.connect('transaction_bot.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ?", (interaction.guild_id,))
            available_slots = cursor.fetchall()
            conn.close()
            
            if not available_slots:
                await interaction.followup.send(
                    "❌ No slots configured for this server. Please set up slots first.",
                    ephemeral=True
                )
                return
            
            # If no slot specified, use the first available slot
            if not slot:
                slot_id = available_slots[0][0]
            else:
                # Find matching slot
                slot_id = None
                for s_id, s_name in available_slots:
                    if slot.lower() in s_name.lower() or slot == s_id:
                        slot_id = s_id
                        break
                
                if not slot_id:
                    slot_list = "\n".join([f"• {s_name} ({s_id})" for s_id, s_name in available_slots])
                    await interaction.followup.send(
                        f"❌ Slot '{slot}' not found. Available slots:\n{slot_list}",
                        ephemeral=True
                    )
                    return
            
            # Get slot configuration
            slot_config = get_slot_config(interaction.guild_id, slot_id)
            if not slot_config:
                await interaction.followup.send(
                    "❌ Slot configuration not found. Please configure the slot first.",
                    ephemeral=True
                )
                return
            
            # Check if user is authorized to make trades
            if not is_authorized(interaction.user, slot_config):
                await interaction.followup.send(
                    "❌ You don't have permission to propose trades. You need to have a franchise owner, general manager, or head coach role.",
                    ephemeral=True
                )
                return
            
            # Parse mentioned players
            your_player_members = []
            other_player_members = []
            
            # Extract user mentions from strings
            import re
            your_mentions = re.findall(r'<@!?(\d+)>', your_players)
            other_mentions = re.findall(r'<@!?(\d+)>', other_players)
            
            for user_id in your_mentions:
                member = interaction.guild.get_member(int(user_id))
                if member:
                    your_player_members.append(member)
            
            for user_id in other_mentions:
                member = interaction.guild.get_member(int(user_id))
                if member:
                    other_player_members.append(member)
            
            # Get user's team role
            user_team_role = None
            conn = sqlite3.connect('transaction_bot.db')
            cursor = conn.cursor()
            
            cursor.execute("""
            SELECT lt.role_id, lt.team_name, lt.emoji 
            FROM league_teams lt 
            WHERE lt.slot_id = ?
            """, (slot_id,))
            
            teams = cursor.fetchall()
            conn.close()
            
            for role_id, team_name, emoji in teams:
                role = interaction.guild.get_role(int(role_id))
                if role and role in interaction.user.roles:
                    user_team_role = role
                    user_team_emoji = emoji
                    break
            
            if not user_team_role:
                await interaction.followup.send(
                    "❌ You are not part of any team in this slot.",
                    ephemeral=True
                )
                return
            
            # Get other team emoji
            other_team_emoji = "🏆"  # Default emoji
            for role_id, team_name, emoji in teams:
                if int(role_id) == other_team.id:
                    other_team_emoji = emoji
                    break
            
            # Validate that players belong to correct teams
            for player in your_player_members:
                if user_team_role not in player.roles:
                    await interaction.followup.send(
                        f"❌ {player.display_name} is not on your team ({user_team_role.name}).",
                        ephemeral=True
                    )
                    return
            
            for player in other_player_members:
                if other_team not in player.roles:
                    await interaction.followup.send(
                        f"❌ {player.display_name} is not on {other_team.name}.",
                        ephemeral=True
                    )
                    return
            
            # Create trade proposal embed
            embed = discord.Embed(
                title="🤝 Trade Proposal",
                description=f"Trade proposed between {user_team_emoji} **{user_team_role.name}** and {other_team_emoji} **{other_team.name}**",
                color=discord.Color.blue(),
                timestamp=DateTime.now(TimeZone.utc)
            )
            
            # Add player details
            your_players_text = "\n".join([f"• {player.display_name}" for player in your_player_members]) or "No players"
            other_players_text = "\n".join([f"• {player.display_name}" for player in other_player_members]) or "No players"
            
            embed.add_field(
                name=f"{user_team_emoji} {user_team_role.name} Offers",
                value=your_players_text,
                inline=True
            )
            
            embed.add_field(
                name=f"{other_team_emoji} {other_team.name} Offers",
                value=other_players_text,
                inline=True
            )
            
            embed.add_field(name="\u200b", value="\u200b", inline=False)  # Empty field for spacing
            
            # Add assets if any
            if your_assets:
                embed.add_field(
                    name=f"{user_team_emoji} Additional Assets",
                    value=your_assets,
                    inline=True
                )
            
            if other_assets:
                embed.add_field(
                    name=f"{other_team_emoji} Requested Assets",
                    value=other_assets,
                    inline=True
                )
            
            embed.set_footer(text=f"Proposed by {interaction.user.display_name} • Slot: {slot_id}")
            
            # Create view with buttons
            view = TradeView(
                interaction, your_player_members, other_player_members,
                user_team_role, other_team, user_team_emoji, other_team_emoji,
                your_assets, other_assets, slot_id
            )
            
            await interaction.followup.send(
                f"📋 Trade proposal sent! {other_team.mention} members with management permissions can accept or decline.",
                embed=embed,
                view=view
            )
            
        except Exception as e:
            print(f"Error in trade command: {e}")
            traceback.print_exc()
            await interaction.followup.send(
                "❌ An error occurred while creating the trade proposal. Please try again.",
                ephemeral=True
            )

# Example of how to integrate this into your main bot file:
# from trade_command import setup_trade_command
# setup_trade_command(bot)