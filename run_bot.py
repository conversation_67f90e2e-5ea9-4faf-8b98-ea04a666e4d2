import subprocess
import sys
import os

def install_requirements():
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("All required packages installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("Failed to install required packages. Please install them manually.")
        return False

def run_bot():
    print("Starting the Discord bot...")
    try:
        subprocess.check_call([sys.executable, "main.py"])
    except subprocess.CalledProcessError:
        print("An error occurred while running the bot.")
        print("Make sure you've set your Discord token in the .env file.")

if __name__ == "__main__":
    # Check if .env file exists and has a token
    if not os.path.exists(".env"):
        print("Warning: .env file not found.")
        token = input("Please enter your Discord bot token: ")
        with open(".env", "w") as f:
            f.write(f"DISCORD_TOKEN={token}")
        print(".env file created with your token.")
    
    # Install requirements
    if install_requirements():
        # Run the bot
        run_bot()
