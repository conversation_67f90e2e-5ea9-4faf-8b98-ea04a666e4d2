#!/usr/bin/env python3
"""
Add the missing transaction_log_channel column to guild_settings table.
"""

import sqlite3
import os

def fix_guild_settings_schema():
    print("🔧 Fixing guild_settings table schema")
    print("=" * 50)
    
    db_path = 'transaction_bot.db'
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} does not exist")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check current schema
    print("📋 Current guild_settings schema:")
    cursor.execute("PRAGMA table_info(guild_settings)")
    columns = cursor.fetchall()
    existing_columns = [col[1] for col in columns]
    
    for col in columns:
        print(f"  - {col[1]} ({col[2]})")
    
    # Add missing column if needed
    if 'transaction_log_channel' not in existing_columns:
        print("\n🔧 Adding missing transaction_log_channel column...")
        try:
            cursor.execute("ALTER TABLE guild_settings ADD COLUMN transaction_log_channel INTEGER")
            conn.commit()
            print("✅ Successfully added transaction_log_channel column")
        except sqlite3.OperationalError as e:
            print(f"❌ Failed to add column: {e}")
    else:
        print("\n✅ transaction_log_channel column already exists")
    
    # Check final schema
    print("\n📋 Updated guild_settings schema:")
    cursor.execute("PRAGMA table_info(guild_settings)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"  - {col[1]} ({col[2]})")
    
    conn.close()

if __name__ == "__main__":
    fix_guild_settings_schema()
