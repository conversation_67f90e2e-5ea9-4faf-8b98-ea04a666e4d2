

import discord
from discord import app_commands
from discord.ext import commands
import sqlite3
import traceback
import datetime
from typing import Optional
import discord
from discord import app_commands
from discord.ext import commands
import sqlite3
import traceback
import datetime

# Bot configuration
DISCORD_TOKEN = "MTM1MTY1Mjg4OTkzNTc0NTEwNA.GbAR9z.zrTEjOraY-tkj2pzg6BA5pKBikw0pie0cTZLkE"  # Paste your token between the quotes



# Database setup
import sqlite3
import discord
from discord import app_commands
from discord.ext import commands
import traceback
import discord
from discord import app_commands
from discord.ext import commands
import json
import os
import asyncio
import sqlite3
from typing import Dict, Any, Optional

# Initialize database
def init_db():
    conn = sqlite3.connect('league_config.db')
    cursor = conn.cursor()
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS configs (
        guild_id INTEGER PRIMARY KEY,
        league_type TEXT,
        franchise_owner_role INTEGER,
        gm_role INTEGER,
        hc_role INTEGER,
        ac_role INTEGER,
        free_agent_role INTEGER,
        referee_role INTEGER,
        streamer_role INTEGER,
        transaction_channel INTEGER,
        gametime_channel INTEGER
    )
    ''')
    
    # Create disband_transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS disband_transactions (
        disband_id TEXT PRIMARY KEY,
        guild_id INTEGER NOT NULL,
        team_role_id INTEGER NOT NULL,
        team_name TEXT NOT NULL,
        team_emoji TEXT,
        disband_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        affected_members_data TEXT NOT NULL,
        initiator_id INTEGER NOT NULL,
        message_id INTEGER,
        is_undone INTEGER DEFAULT 0
    )
    ''')
    
    conn.commit()
    return conn

# Configuration class with database integration
class LeagueConfig:
    def __init__(self, guild_id=None):
        self.guild_id = guild_id
        self.league_type = "generic"
        self.franchise_owner_role = None
        self.gm_role = None
        self.hc_role = None
        self.ac_role = None
        self.free_agent_role = None
        self.referee_role = None
        self.streamer_role = None
        self.transaction_channel = None
        self.gametime_channel = None
        
        # Load from DB if guild_id provided
        if guild_id:
            self.load_from_db()
    
    def load_from_db(self):
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM configs WHERE guild_id = ?', (self.guild_id,))
        row = cursor.fetchone()
        if row:
            self.league_type = row[1]
            self.franchise_owner_role = row[2]
            self.gm_role = row[3]
            self.hc_role = row[4]
            self.ac_role = row[5]
            self.free_agent_role = row[6]
            self.referee_role = row[7]
            self.streamer_role = row[8]
            self.transaction_channel = row[9]
            self.gametime_channel = row[10]
        conn.close()
    
    def save_to_db(self):
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        cursor.execute('''
        INSERT OR REPLACE INTO configs VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            self.guild_id,
            self.league_type,
            self.franchise_owner_role,
            self.gm_role,
            self.hc_role,
            self.ac_role,
            self.free_agent_role,
            self.referee_role,
            self.streamer_role,
            self.transaction_channel,
            self.gametime_channel
        ))
        conn.commit()
        conn.close()

# Active setup sessions storage
active_setups = {}

# Create bot with necessary intents
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix='!', intents=intents)

# Utility function to get role/channel mention
def get_mention(guild, item_id, is_role=True):
    if not item_id:
        return "Not Set"
    
    if is_role:
        item = guild.get_role(item_id)
        return item.mention if item else "Not Set"
    else:
        item = guild.get_channel(item_id)
        return f"#{item.name}" if item else "Not Set"

# Create compact embeds for different pages
def create_page_embed(page: int, config: LeagueConfig, guild: discord.Guild) -> discord.Embed:
    pages = [
        # Page 0: League Type
        discord.Embed(
            title="League Configuration - League Type",
            description=f"Select the type of league you're setting up\n\nCurrent Selection: **{config.league_type.capitalize()}**",
            color=discord.Color.blue()
        ).set_footer(text="Page 1/5"),
        
        # Page 1: Management Roles
        discord.Embed(
            title="League Configuration - Management Roles",
            description="Set up the management roles for your league",
            color=discord.Color.blue()
        ).add_field(name="Franchise Owner", value=get_mention(guild, config.franchise_owner_role), inline=True
        ).add_field(name="General Manager", value=get_mention(guild, config.gm_role), inline=True
        ).add_field(name="Head Coach", value=get_mention(guild, config.hc_role), inline=True
        ).add_field(name="Assistant Coach", value=get_mention(guild, config.ac_role), inline=True
        ).set_footer(text="Page 2/5"),
        
        # Page 2: Staff & Free Agent Roles
        discord.Embed(
            title="League Configuration - Staff & Free Agent Roles",
            description="Set up your staff and free agent roles",
            color=discord.Color.blue()
        ).add_field(name="Streamer", value=get_mention(guild, config.streamer_role), inline=True
        ).add_field(name="Referee", value=get_mention(guild, config.referee_role), inline=True
        ).add_field(name="Free Agent", value=get_mention(guild, config.free_agent_role), inline=True
        ).set_footer(text="Page 3/5"),
        
        # Page 3: Channels
        discord.Embed(
            title="League Configuration - Channels",
            description="Set up your notification channels",
            color=discord.Color.blue()
        ).add_field(name="Transaction Channel", value=get_mention(guild, config.transaction_channel, False), inline=True
        ).add_field(name="Game Time Channel", value=get_mention(guild, config.gametime_channel, False), inline=True
        ).set_footer(text="Page 4/5"),
        
        # Page 4: Review
        discord.Embed(
            title="League Configuration - Review",
            description=(
                f"**League Type:** {config.league_type.capitalize()}\n\n"
                f"**Management Roles:**\n"
                f"• Franchise Owner: {get_mention(guild, config.franchise_owner_role)}\n"
                f"• General Manager: {get_mention(guild, config.gm_role)}\n"
                f"• Head Coach: {get_mention(guild, config.hc_role)}\n"
                f"• Assistant Coach: {get_mention(guild, config.ac_role)}\n\n"
                f"**Staff & Free Agent Roles:**\n"
                f"• Streamer: {get_mention(guild, config.streamer_role)}\n"
                f"• Referee: {get_mention(guild, config.referee_role)}\n"
                f"• Free Agent: {get_mention(guild, config.free_agent_role)}\n\n"
                f"**Channels:**\n"
                f"• Transaction Channel: {get_mention(guild, config.transaction_channel, False)}\n"
                f"• Game Time Channel: {get_mention(guild, config.gametime_channel, False)}"
            ),
            color=discord.Color.blue()
        ).set_footer(text="Page 5/5")
    ]
    
    return pages[page]

# League type selection view
class LeagueTypeView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=180)
        
        for league_type in ["generic", "football", "basketball"]:
            self.add_item(discord.ui.Button(
                label=league_type.capitalize(),
                style=discord.ButtonStyle.primary,
                custom_id=f"league_{league_type}"
            ))

# Role input modal
class RoleInputModal(discord.ui.Modal):
    def __init__(self, role_type: str):
        self.role_type = role_type
        role_name = role_type.replace('_', ' ').title()
        super().__init__(title=f"Set {role_name}")
        
        self.role_input = discord.ui.TextInput(
            label=f"Enter Role ID for {role_name}",
            placeholder="123456789012345678",
            required=True
        )
        self.add_item(self.role_input)
    
    async def on_submit(self, interaction: discord.Interaction):
        guild_id = interaction.guild_id
        if guild_id not in active_setups:
            return
        
        try:
            role_id = int(self.role_input.value.strip())
            role = interaction.guild.get_role(role_id)
            
            if role:
                setattr(active_setups[guild_id]["config"], self.role_type, role_id)
                await interaction.response.send_message(f"Set {self.role_type.replace('_', ' ').title()} to {role.mention}", ephemeral=True)
            else:
                await interaction.response.send_message(f"Could not find a role with ID {role_id}", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Please enter a valid role ID (numbers only)", ephemeral=True)
        
        # Update the main setup message
        await update_setup_message(interaction, guild_id, deferred=True)

# Channel input modal
class ChannelInputModal(discord.ui.Modal):
    def __init__(self, channel_type: str):
        self.channel_type = channel_type
        channel_name = channel_type.replace('_', ' ').title()
        super().__init__(title=f"Set {channel_name}")
        
        self.channel_input = discord.ui.TextInput(
            label=f"Enter Channel ID for {channel_name}",
            placeholder="123456789012345678",
            required=True
        )
        self.add_item(self.channel_input)
    
    async def on_submit(self, interaction: discord.Interaction):
        guild_id = interaction.guild_id
        if guild_id not in active_setups:
            return
        
        try:
            channel_id = int(self.channel_input.value.strip())
            channel = interaction.guild.get_channel(channel_id)
            
            if channel and isinstance(channel, discord.TextChannel):
                setattr(active_setups[guild_id]["config"], self.channel_type, channel_id)
                await interaction.response.send_message(f"Set {self.channel_type.replace('_', ' ').title()} to #{channel.name}", ephemeral=True)
            else:
                await interaction.response.send_message(f"Could not find a text channel with ID {channel_id}", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Please enter a valid channel ID (numbers only)", ephemeral=True)
        
        # Update the main setup message
        await update_setup_message(interaction, guild_id, deferred=True)

# Base view for common functionality
class BaseSetupView(discord.ui.View):
    def __init__(self, page: int, is_final: bool = False):
        super().__init__(timeout=180)
        
        # Add navigation buttons
        self.add_item(discord.ui.Button(
            style=discord.ButtonStyle.secondary,
            label="Previous",
            custom_id="nav_prev",
            disabled=(page == 0)
        ))
        
        self.add_item(discord.ui.Button(
            style=discord.ButtonStyle.secondary,
            label="Next",
            custom_id="nav_next",
            disabled=is_final
        ))
        
        # Add save button on final page
        if is_final:
            self.add_item(discord.ui.Button(
                style=discord.ButtonStyle.success,
                label="Save Configuration",
                custom_id="save_config"
            ))

# Management roles view
class ManagementRolesView(BaseSetupView):
    def __init__(self, page: int):
        super().__init__(page)
        
        # Add role buttons
        role_types = [
            ("Franchise Owner", "franchise_owner_role"),
            ("General Manager", "gm_role"),
            ("Head Coach", "hc_role"),
            ("Assistant Coach", "ac_role")
        ]
        
        for label, role_id in role_types:
            self.add_item(discord.ui.Button(
                style=discord.ButtonStyle.secondary,
                label=f"Set {label}",
                custom_id=f"set_{role_id}"
            ))

# Staff roles view
class StaffRolesView(BaseSetupView):
    def __init__(self, page: int):
        super().__init__(page)
        
        # Add role buttons
        role_types = [
            ("Streamer", "streamer_role"),
            ("Referee", "referee_role"),
            ("Free Agent", "free_agent_role")
        ]
        
        for label, role_id in role_types:
            self.add_item(discord.ui.Button(
                style=discord.ButtonStyle.secondary,
                label=f"Set {label}",
                custom_id=f"set_{role_id}"
            ))

# Channels view
class ChannelsView(BaseSetupView):
    def __init__(self, page: int):
        super().__init__(page)
        
        # Add channel buttons
        channel_types = [
            ("Transaction Channel", "transaction_channel"),
            ("Game Time Channel", "gametime_channel")
        ]
        
        for label, channel_id in channel_types:
            self.add_item(discord.ui.Button(
                style=discord.ButtonStyle.secondary,
                label=f"Set {label}",
                custom_id=f"set_{channel_id}"
            ))

# Function to create the appropriate view for each page
def create_page_view(page: int, guild: discord.Guild) -> discord.ui.View:
    if page == 0:  # League Type
        view = LeagueTypeView()
        for item in BaseSetupView(page).children:
            view.add_item(item)
        return view
    elif page == 1:  # Management Roles
        return ManagementRolesView(page)
    elif page == 2:  # Staff Roles
        return StaffRolesView(page)
    elif page == 3:  # Channels
        return ChannelsView(page)
    elif page == 4:  # Review
        return BaseSetupView(page, True)
    return BaseSetupView(page)

# Update setup message
async def update_setup_message(interaction: discord.Interaction, guild_id: int, deferred: bool = False):
    if guild_id not in active_setups:
        return
    
    setup_data = active_setups[guild_id]
    page = setup_data["current_page"]
    config = setup_data["config"]
    message = setup_data["message"]
    
    embed = create_page_embed(page, config, interaction.guild)
    view = create_page_view(page, interaction.guild)
    
    if deferred:
        await message.edit(embed=embed, view=view)
    else:
        await interaction.response.edit_message(embed=embed, view=view)

# Handle button interactions
@bot.event
async def on_interaction(interaction: discord.Interaction):
    if interaction.type != discord.InteractionType.component:
        return
    
    guild_id = interaction.guild_id
    if guild_id not in active_setups:
        return
    
    setup_data = active_setups[guild_id]
    
    # Check if this interaction is for our setup message
    if interaction.message.id != setup_data["message"].id:
        return
    
    # Check if the user is the one who started the setup
    if interaction.user.id != setup_data["user_id"]:
        await interaction.response.send_message("Only the user who started the setup can interact with it.", ephemeral=True)
        return
    
    custom_id = interaction.data["custom_id"]
    
    # Handle navigation buttons
    if custom_id == "nav_prev":
        if setup_data["current_page"] > 0:
            setup_data["current_page"] -= 1
            await update_setup_message(interaction, guild_id)
    
    elif custom_id == "nav_next":
        if setup_data["current_page"] < 4:
            setup_data["current_page"] += 1
            await update_setup_message(interaction, guild_id)
    
    # Handle save button
    elif custom_id == "save_config":
        # Save the configuration to database
        config = setup_data["config"]
        config.guild_id = guild_id
        config.save_to_db()
        
        # Create completion embed
        complete_embed = discord.Embed(
            title="✅ Configuration Saved",
            description="Your league configuration has been saved successfully!",
            color=discord.Color.green()
        )
        
        await interaction.response.edit_message(embed=complete_embed, view=None)
        
        # Remove from active setups
        del active_setups[guild_id]
    
    # Handle league type selection
    elif custom_id.startswith("league_"):
        league_type = custom_id[7:]  # Remove "league_" prefix
        setup_data["config"].league_type = league_type
        await update_setup_message(interaction, guild_id)
    
    # Handle role setting buttons
    elif custom_id.startswith("set_") and custom_id.endswith("_role"):
        role_type = custom_id[4:]  # Remove "set_" prefix
        modal = RoleInputModal(role_type)
        await interaction.response.send_modal(modal)
    
    # Handle channel setting buttons
    elif custom_id.startswith("set_") and custom_id.endswith("_channel"):
        channel_type = custom_id[4:]  # Remove "set_" prefix
        modal = ChannelInputModal(channel_type)
        await interaction.response.send_modal(modal)

# Setup command
@bot.tree.command(name="setup_league", description="Set up your league configuration")
@app_commands.default_permissions(administrator=True)
async def setup_league(interaction: discord.Interaction):
    # Initialize setup for this guild
    guild_id = interaction.guild_id
    
    # Check if there's already an active setup
    if guild_id in active_setups:
        await interaction.response.send_message("There's already an active setup for this server. Please finish or cancel it first.", ephemeral=True)
        return
    
    # Create new setup
    config = LeagueConfig(guild_id)  # Load existing config if available
    active_setups[guild_id] = {
        "user_id": interaction.user.id,
        "config": config,
        "current_page": 0,
        "message": None
    }
    
    # Create initial embed and view
    embed = create_page_embed(0, config, interaction.guild)
    view = create_page_view(0, interaction.guild)
    
    # Send the initial message
    await interaction.response.send_message(embed=embed, view=view)
    
    # Store the message for future updates
    original_message = await interaction.original_response()
    active_setups[guild_id]["message"] = original_message
    
    # Setup timeout
    bot.loop.create_task(setup_timeout(guild_id, original_message.id))

# Timeout function for setup
async def setup_timeout(guild_id: int, message_id: int):
    await asyncio.sleep(900)  # 15 minutes timeout
    
    # Check if setup is still active
    if guild_id in active_setups and active_setups[guild_id]["message"].id == message_id:
        try:
            timeout_embed = discord.Embed(
                title="⏰ Setup Timeout",
                description="The setup wizard has timed out due to inactivity.",
                color=discord.Color.red()
            )
            await active_setups[guild_id]["message"].edit(embed=timeout_embed, view=None)
        except:
            pass
        
        # Remove from active setups
        del active_setups[guild_id]

# Fetch config command
@bot.tree.command(name="get_league_config", description="Display current league configuration")
@app_commands.default_permissions(administrator=True)
async def get_league_config(interaction: discord.Interaction):
    config = LeagueConfig(interaction.guild.id)
    
    embed = discord.Embed(
        title="League Configuration",
        description=f"Type: {config.league_type.capitalize()}",
        color=discord.Color.blue()
    )
    
    # Management roles
    roles_info = (
        f"• Franchise Owner: {get_mention(interaction.guild, config.franchise_owner_role)}\n"
        f"• General Manager: {get_mention(interaction.guild, config.gm_role)}\n"
        f"• Head Coach: {get_mention(interaction.guild, config.hc_role)}\n"
        f"• Assistant Coach: {get_mention(interaction.guild, config.ac_role)}"
    )
    embed.add_field(name="Management Roles", value=roles_info, inline=False)
    
    # Staff roles
    staff_info = (
        f"• Streamer: {get_mention(interaction.guild, config.streamer_role)}\n"
        f"• Referee: {get_mention(interaction.guild, config.referee_role)}\n"
        f"• Free Agent: {get_mention(interaction.guild, config.free_agent_role)}"
    )
    embed.add_field(name="Staff & Free Agent Roles", value=staff_info, inline=False)
    
    # Channels
    channels_info = (
        f"• Transaction: {get_mention(interaction.guild, config.transaction_channel, False)}\n"
        f"• Game Time: {get_mention(interaction.guild, config.gametime_channel, False)}"
    )
    embed.add_field(name="Channels", value=channels_info, inline=False)
    
    await interaction.response.send_message(embed=embed)

@bot.event
async def on_ready():
    print(f"Logged in as {bot.user.name} (ID: {bot.user.id})")
    
    # Initialize database
    init_db()
    
    # Sync commands with Discord
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")

@bot.tree.command(name="disband", description="Remove team roles from members")
@app_commands.describe(
    team_role="Optional: Specific team role to disband (admin only)",
    league_type="Optional: Specify the league type to disband (admin only)"
)
async def disband(
    interaction: discord.Interaction, 
    team_role: Optional[discord.Role] = None,
    league_type: Optional[str] = None
):
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if config exists
        cursor.execute("SELECT franchise_owner_role, gm_role, league_type FROM league_config WHERE guild_id = ?", (str(interaction.guild.id),))
        config = cursor.fetchone()
        
        if not config:
            conn.close()
            return await interaction.response.send_message(
                'Server is not set up yet. Please use /setup_manual first.',
                ephemeral=True
            )
        
        # Get franchise owner and GM role IDs
        franchise_owner_role_id = config[0]
        gm_role_id = config[1]
        current_league_type = config[2] if config[2] else "generic"
        
        # If league_type is specified, use that instead of the current one
        target_league_type = league_type if league_type else current_league_type
        
        # Check if user has permission to use this command
        member = interaction.guild.get_member(interaction.user.id)
        has_fo_role = franchise_owner_role_id and any(role.id == int(franchise_owner_role_id) for role in member.roles)
        has_gm_role = gm_role_id and any(role.id == int(gm_role_id) for role in member.roles)
        has_admin_perms = member.guild_permissions.administrator
        
        if not (has_fo_role or has_gm_role or has_admin_perms):
            conn.close()
            return await interaction.response.send_message(
                'You need to be a Franchise Owner, General Manager, or have administrator permissions to use this command.',
                ephemeral=True
            )
        
        # If a specific team role or league type is provided, only admins can use it
        if (team_role or league_type) and not has_admin_perms:
            conn.close()
            return await interaction.response.send_message(
                'Only administrators can disband a specific team or league type.',
                ephemeral=True
            )
        
        # Start message
        message_parts = []
        if team_role:
            message_parts.append(f"team role: {team_role.name}")
        elif league_type:
            message_parts.append(f"all teams in league type: {target_league_type}")
        else:
            message_parts.append("all team roles")
            
        await interaction.response.send_message(
            f'Disbanding {" and ".join(message_parts)}. This may take a moment...',
            ephemeral=False
        )
        
        # Get roles to remove
        role_ids_to_remove = []
        
        if team_role:
            # Check if this is a valid team role
            cursor.execute("SELECT role_id FROM league_teams WHERE role_id = ? AND guild_id = ?", 
                          (str(team_role.id), str(interaction.guild.id)))
            if cursor.fetchone():
                role_ids_to_remove = [str(team_role.id)]
            else:
                conn.close()
                return await interaction.followup.send(
                    f'The role {team_role.name} is not registered as a team role in the database.',
                    ephemeral=True
                )
        else:
            # Get all team roles for the target league type
            if league_type:
                cursor.execute("SELECT role_id FROM league_teams WHERE guild_id = ? AND league_type = ?", 
                              (str(interaction.guild.id), target_league_type))
            else:
                cursor.execute("SELECT role_id FROM league_teams WHERE guild_id = ?", 
                              (str(interaction.guild.id),))
                
            team_roles = cursor.fetchall()
            role_ids_to_remove = [role[0] for role in team_roles]
            
            # If not specifying a team, also include config roles
            if not team_role and not league_type:
                cursor.execute("SELECT franchise_owner_role, gm_role, hc_role, ac_role FROM league_config WHERE guild_id = ?", 
                              (str(interaction.guild.id),))
                config = cursor.fetchone()
                config_role_ids = [role_id for role_id in config if role_id]
                role_ids_to_remove.extend(config_role_ids)
        
        # Remove roles from members
        count = 0
        for member in interaction.guild.members:
            roles_to_remove = []
            for role in member.roles:
                if str(role.id) in role_ids_to_remove:
                    roles_to_remove.append(role)
            
            if roles_to_remove:
                await member.remove_roles(*roles_to_remove)
                count += 1
        
        # Remove from database
        if team_role:
            cursor.execute("DELETE FROM league_teams WHERE role_id = ? AND guild_id = ?", 
                          (str(team_role.id), str(interaction.guild.id)))
        elif league_type:
            cursor.execute("DELETE FROM league_teams WHERE guild_id = ? AND league_type = ?", 
                          (str(interaction.guild.id), target_league_type))
        else:
            cursor.execute("DELETE FROM league_teams WHERE guild_id = ?", 
                          (str(interaction.guild.id),))
        
        conn.commit()
        conn.close()
        
        # Construct response message
        response_parts = []
        if team_role:
            response_parts.append(f"Successfully removed team role from {count} members. Team {team_role.name} has been removed from the database.")
        elif league_type:
            response_parts.append(f"Successfully removed {target_league_type} league team roles from {count} members.")
        else:
            response_parts.append(f"Successfully removed team roles from {count} members.")
            
        await interaction.followup.send(
            " ".join(response_parts),
            ephemeral=False
        )
    except Exception as error:
        print(f"Error disbanding team: {error}")
        traceback.print_exc()
        try:
            conn.close()
        except:
            pass
        await interaction.followup.send(
            'There was an error while disbanding the team. Please try again later.',
            ephemeral=True
        )
from datetime import datetime

@bot.tree.command(name="franchise_owners", description="List all franchise owners and their teams")
async def franchise_owners(interaction: discord.Interaction):
    conn = None
    try:
        conn = sqlite3.connect('league_config.db')  # Using the same DB file
        cursor = conn.cursor()
        
        # First check if the league_teams table exists, create it if not
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS league_teams (
            role_id TEXT PRIMARY KEY,
            guild_id TEXT,
            team_name TEXT,
            emoji TEXT,
            league_type TEXT DEFAULT 'generic'
        )''')
        conn.commit()
        
        # Get franchise owner role from configs table instead of league_config
        cursor.execute("SELECT franchise_owner_role FROM configs WHERE guild_id = ?", (interaction.guild.id,))
        config = cursor.fetchone()
        
        if not config or not config[0]:
            return await interaction.response.send_message(
                'Franchise owner role is not set up. Please use /setup_league first.',
                ephemeral=True
            )
        
        franchise_owner_role_id = config[0]
        
        # Get all teams from league_teams table
        cursor.execute("""
        SELECT role_id, team_name, emoji FROM league_teams 
        WHERE guild_id = ?
        """, (str(interaction.guild.id),))
        teams = cursor.fetchall()
        
        # If no teams found in league_teams, try the legacy teams table
        if not teams:
            # Try to get teams from the legacy teams table
            try:
                cursor.execute("""
                SELECT role_id, team_name, emoji FROM teams 
                WHERE guild_id = ?
                """, (str(interaction.guild.id),))
                teams = cursor.fetchall()
            except sqlite3.OperationalError:
                # teams table may not exist
                pass
        
        # Handle empty teams case
        if not teams:
            return await interaction.response.send_message(
                'No teams found in the database. Please create teams first using /search_teams or /add_team.',
                ephemeral=True
            )
            
        # Get franchise owners and their teams
        try:
            fo_role = interaction.guild.get_role(int(franchise_owner_role_id))
            if not fo_role:
                return await interaction.response.send_message(
                    'Franchise owner role not found. The role may have been deleted.',
                    ephemeral=True
                )
        except (ValueError, TypeError):
            return await interaction.response.send_message(
                'Invalid franchise owner role ID. Please use /setup_league to set it correctly.',
                ephemeral=True
            )
        
        # Collect owner data first
        owner_data = []
        for member in fo_role.members:
            member_teams = []
            for team in teams:
                try:
                    team_role = interaction.guild.get_role(int(team[0]))
                    if team_role and team_role in member.roles:
                        # Make sure we handle null emoji values
                        emoji = team[2] if team[2] else ""
                        team_name = team[1] if team[1] else "Unnamed Team"
                        member_teams.append(f"{emoji} {team_name}")
                except (ValueError, TypeError):
                    # Skip teams with invalid role IDs
                    continue
            
            if member_teams:
                owner_data.append((member.display_name, "\n".join(member_teams)))
        
        # Handle case where no owners are found
        if not owner_data:
            embed = discord.Embed(
                title="Franchise Owners",
                description="No franchise owners found with team roles.",
                color=discord.Color.blue()
            )
            await interaction.response.send_message(embed=embed, ephemeral=False)
            return
        
        # Create paginated embeds
        # First page
        first_embed = discord.Embed(
            title="Franchise Owners (Page 1)",
            description=f"List of franchise owners and their teams ({len(owner_data)} total)",
            color=discord.Color.blue()
        )
        
        # Add up to 25 fields to first embed
        page_size = 25
        for name, value in owner_data[:page_size]:
            first_embed.add_field(name=name, value=value, inline=False)
        
        # Send first response
        await interaction.response.send_message(embed=first_embed, ephemeral=False)
        
        # If we have more data, create additional embeds as needed
        if len(owner_data) > page_size:
            # Calculate how many additional pages we need
            remaining_data = owner_data[page_size:]
            
            # Send additional pages as follow-up messages
            page_num = 2
            while remaining_data:
                current_page_data = remaining_data[:page_size]
                remaining_data = remaining_data[page_size:]
                
                next_embed = discord.Embed(
                    title=f"Franchise Owners (Page {page_num})",
                    description=f"List of franchise owners and their teams ({len(owner_data)} total)",
                    color=discord.Color.blue()
                )
                
                for name, value in current_page_data:
                    next_embed.add_field(name=name, value=value, inline=False)
                
                # Send as a follow-up message
                await interaction.followup.send(embed=next_embed, ephemeral=False)
                page_num += 1
                
    except Exception as error:
        print(f"Error listing franchise owners: {error}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    f'There was an error while listing franchise owners: {str(error)}',
                    ephemeral=True
                )
            else:
                await interaction.followup.send(
                    f'There was an error while listing franchise owners: {str(error)}',
                    ephemeral=True
                )
        except:
            print("Could not send error message")
    finally:
        if conn:
            conn.close()
@bot.tree.command(name="general_managers", description="List all general managers and their teams")
async def general_managers(interaction: discord.Interaction):
    conn = None
    try:
        # Initialize connection to both databases
        league_conn = sqlite3.connect('league_config.db')
        teams_conn = sqlite3.connect(DB_FILE)
        
        # First, get the GM role from the new configuration system
        config = LeagueConfig(interaction.guild.id)
        
        if not config.gm_role:
            return await interaction.response.send_message(
                'General manager role is not set up. Please use /setup_league first.',
                ephemeral=True
            )
        
        gm_role_id = config.gm_role
        
        # Get all teams from league_teams table
        cursor = teams_conn.cursor()
        cursor.execute("""
        SELECT role_id, team_name, emoji FROM league_teams 
        WHERE guild_id = ?
        """, (str(interaction.guild.id),))
        teams = cursor.fetchall()
        
        # If no teams in league_teams, try from teams table (for backward compatibility)
        if not teams:
            cursor.execute("""
            SELECT role_id, team_name, emoji FROM teams 
            WHERE guild_id = ?
            """, (str(interaction.guild.id),))
            teams = cursor.fetchall()
        
        # Handle empty teams case
        if not teams:
            return await interaction.response.send_message(
                'No teams found in the database. Please create teams first using /add_team or /auto_setup.',
                ephemeral=True
            )
            
        # Get GM role from guild
        try:
            gm_role = interaction.guild.get_role(int(gm_role_id))
            if not gm_role:
                return await interaction.response.send_message(
                    'General manager role not found. The role may have been deleted or not set correctly in /setup_league.',
                    ephemeral=True
                )
        except ValueError:
            return await interaction.response.send_message(
                'Invalid general manager role ID. Please use /setup_league to set it correctly.',
                ephemeral=True
            )
        
        # Collect GM data first
        gm_data = []
        for member in gm_role.members:
            member_teams = []
            for team in teams:
                try:
                    team_role = interaction.guild.get_role(int(team[0]))
                    if team_role and team_role in member.roles:
                        # Make sure we handle null emoji values
                        emoji = team[2] if team[2] else ""
                        team_name = team[1] if team[1] else "Unnamed Team"
                        member_teams.append(f"{emoji} {team_name}")
                except (ValueError, TypeError):
                    # Skip teams with invalid role IDs
                    continue
            
            if member_teams:
                gm_data.append((member.display_name, "\n".join(member_teams)))
        
        # Handle case where no GMs are found
        if not gm_data:
            embed = discord.Embed(
                title="General Managers",
                description="No general managers found with team roles.",
                color=discord.Color.green(),
                timestamp=datetime.utcnow()
            )
            await interaction.response.send_message(embed=embed, ephemeral=False)
            return
        
        # Create paginated embeds with improved pagination
        embeds = []
        page_size = 25
        total_pages = (len(gm_data) + page_size - 1) // page_size  # Ceiling division
        
        for page_num in range(total_pages):
            start_idx = page_num * page_size
            end_idx = min(start_idx + page_size, len(gm_data))
            current_page_data = gm_data[start_idx:end_idx]
            
            page_embed = discord.Embed(
                title=f"General Managers (Page {page_num + 1}/{total_pages})",
                description=f"List of general managers and their teams ({len(gm_data)} total)",
                color=discord.Color.green(),
                timestamp=datetime.utcnow()
            )
            
            for name, value in current_page_data:
                page_embed.add_field(name=name, value=value, inline=False)
            
            embeds.append(page_embed)
        
        # Send the first embed with a view for pagination if needed
        if len(embeds) > 1:
            view = PaginationView(embeds)
            await interaction.response.send_message(embed=embeds[0], view=view, ephemeral=False)
        else:
            await interaction.response.send_message(embed=embeds[0], ephemeral=False)
                
    except Exception as error:
        print(f"Error listing general managers: {error}")
        await interaction.response.send_message(
            f'There was an error while listing general managers: {str(error)}',
            ephemeral=True
        )
    finally:
        if 'league_conn' in locals() and league_conn:
            league_conn.close()
        if 'teams_conn' in locals() and teams_conn:
            teams_conn.close()

# Pagination view for multiple pages of content
class PaginationView(discord.ui.View):
    def __init__(self, embeds):
        super().__init__(timeout=180)
        self.embeds = embeds
        self.current_page = 0
        self.total_pages = len(embeds)
        
        # Update button states based on initial page
        self.update_buttons()
    
    def update_buttons(self):
        # Get the buttons by their custom_id
        prev_button = discord.utils.get(self.children, custom_id="prev_page")
        next_button = discord.utils.get(self.children, custom_id="next_page")
        
        # Update disabled state
        if prev_button:
            prev_button.disabled = self.current_page == 0
        if next_button:
            next_button.disabled = self.current_page == self.total_pages - 1
    
    @discord.ui.button(label="Previous", style=discord.ButtonStyle.secondary, custom_id="prev_page")
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_page = max(0, self.current_page - 1)
        self.update_buttons()
        await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
    
    @discord.ui.button(label="Next", style=discord.ButtonStyle.secondary, custom_id="next_page")
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_page = min(self.total_pages - 1, self.current_page + 1)
        self.update_buttons()
        await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)

@bot.tree.command(name="add_team", description="Add a new team to the server")
@app_commands.describe(
    team_role="The role for the team",
    team_name="The name of the team",
    league_type="The type of league this team belongs to (generic, football, basketball)",
    emoji="The emoji for the team"
)
@app_commands.choices(league_type=[
    app_commands.Choice(name="Generic", value="generic"),
    app_commands.Choice(name="Football", value="football"),
    app_commands.Choice(name="Basketball", value="basketball")
])
async def add_team(interaction: discord.Interaction, team_role: discord.Role, team_name: str, 
                  league_type: app_commands.Choice[str], emoji: str):
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if team already exists with this role in this guild
        cursor.execute('''
        SELECT team_name FROM league_teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        existing_team = cursor.fetchone()
        if existing_team:
            return await interaction.followup.send(
                f'This role is already assigned to team "{existing_team[0]}". Please use a different role.',
                ephemeral=True
            )
        
        # Insert new team into league_teams
        cursor.execute('''
        INSERT INTO league_teams (role_id, guild_id, team_name, emoji, league_type) 
        VALUES (?, ?, ?, ?, ?)
        ''', (str(team_role.id), str(interaction.guild.id), team_name, emoji, league_type.value))
        
        # For backward compatibility with older code that might use the teams table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS teams (
            role_id TEXT,
            guild_id TEXT,
            team_name TEXT,
            league TEXT,
            emoji TEXT,
            PRIMARY KEY (role_id, guild_id)
        )
        ''')
        
        cursor.execute('''
        INSERT INTO teams (role_id, guild_id, team_name, league, emoji) 
        VALUES (?, ?, ?, ?, ?)
        ''', (str(team_role.id), str(interaction.guild.id), team_name, league_type.value, emoji))
        
        # If it's a football team, also add to nfl_teams for backward compatibility
        if league_type.value == "football":
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS nfl_teams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                role_id TEXT NOT NULL,
                guild_id TEXT NOT NULL,
                team_name TEXT NOT NULL,
                emoji TEXT NOT NULL
            )
            ''')
            
            cursor.execute('''
            INSERT INTO nfl_teams (role_id, guild_id, team_name, emoji) 
            VALUES (?, ?, ?, ?)
            ''', (str(team_role.id), str(interaction.guild.id), team_name, emoji))
        
        conn.commit()
        
        # Use followup instead of response since we deferred
        await interaction.followup.send(
            f'Team {team_name} {emoji} has been added to {league_type.name} league successfully!',
            ephemeral=True
        )
    except sqlite3.IntegrityError as integrity_error:
        print(f"Database integrity error adding team: {integrity_error}")
        await interaction.followup.send(
            f'This role or team name is already in use. Please try again with a different role or team name.',
            ephemeral=True
        )
    except Exception as error:
        print(f"Error adding team: {error}")
        traceback.print_exc()  # Added traceback for better debugging
        await interaction.followup.send(
            f'There was an error adding the team: {error}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()
@bot.tree.command(name="sign", description="Sign a player to your team")
@app_commands.describe(player="The player to sign")
async def sign(interaction: discord.Interaction, player: discord.User):
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        # Check if guild exists before proceeding
        if not interaction.guild:
            return await interaction.followup.send(
                'Cannot execute this command: unable to access guild information.',
                ephemeral=True
            )
        
        # Get the member more reliably - fetch if necessary
        member = interaction.guild.get_member(interaction.user.id)
        if not member:
            try:
                # Attempt to fetch the member from the API if not in cache
                member = await interaction.guild.fetch_member(interaction.user.id)
            except discord.errors.NotFound:
                return await interaction.followup.send(
                    'Cannot verify your permissions: member information not found.',
                    ephemeral=True
                )
        
        # Check if JSON configuration exists first (new method)
        config_file_path = f"league_configs/{interaction.guild.id}.json"
        config_dict = None
        
        if os.path.exists(config_file_path):
            try:
                with open(config_file_path, "r") as f:
                    config_dict = json.load(f)
                print(f"Loaded JSON config: {config_dict}")
            except Exception as e:
                print(f"Error loading JSON config: {e}")
        
        # If JSON config not found, fall back to database
        if not config_dict:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            # Check if config tables exist
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS league_config (
                guild_id TEXT PRIMARY KEY,
                franchise_owner_role TEXT,
                gm_role TEXT,
                hc_role TEXT,
                ac_role TEXT,
                free_agent_role TEXT,
                transaction_channel TEXT,
                gametime_channel TEXT,
                referee_role TEXT,
                streamer_role TEXT,
                league_type TEXT DEFAULT "generic"
            )
            ''')
            
            # Legacy tables for backward compatibility
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS server_config (
                guild_id TEXT PRIMARY KEY,
                franchise_owner_role TEXT,
                gm_role TEXT,
                hc_role TEXT,
                ac_role TEXT,
                free_agent_role TEXT,
                transaction_channel TEXT
            )
            ''')
            
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS nfl_config (
                guild_id TEXT PRIMARY KEY,
                franchise_owner_role TEXT,
                gm_role TEXT,
                hc_role TEXT,
                ac_role TEXT,
                free_agent_role TEXT,
                transaction_channel TEXT
            )
            ''')
            
            # First check the new league_config table
            cursor.execute("SELECT * FROM league_config WHERE guild_id = ?", (str(interaction.guild.id),))
            config = cursor.fetchone()
            
            # If not found, try server_config table
            if not config:
                cursor.execute("SELECT * FROM server_config WHERE guild_id = ?", (str(interaction.guild.id),))
                config = cursor.fetchone()
                
                # If still not found, try the old nfl_config table
                if not config:
                    cursor.execute("SELECT * FROM nfl_config WHERE guild_id = ?", (str(interaction.guild.id),))
                    config = cursor.fetchone()
                    
                    if not config:
                        # Create a default config if none exists
                        cursor.execute('''
                        INSERT INTO league_config (
                            guild_id, franchise_owner_role, gm_role, hc_role, ac_role, 
                            free_agent_role, transaction_channel, league_type
                        ) VALUES (?, NULL, NULL, NULL, NULL, NULL, NULL, "generic")
                        ''', (str(interaction.guild.id),))
                        conn.commit()
                        
                        cursor.execute("SELECT * FROM league_config WHERE guild_id = ?", (str(interaction.guild.id),))
                        config = cursor.fetchone()
                        
                        # If still not found after trying to create one
                        if not config:
                            if conn:
                                conn.close()
                            return await interaction.followup.send(
                                'Server is not set up yet. Please use /setup_league first.',
                                ephemeral=True
                            )
            
            # Get column names to properly extract values regardless of table
            cursor.execute("PRAGMA table_info(league_config)")
            columns = [column[1] for column in cursor.fetchall()]
            
            # Create a dictionary for easier access based on which table we found
            if len(config) == 7:  # Original nfl_config or server_config has 7 columns
                config_dict = {
                    'franchise_owner_role': config[1],
                    'gm_role': config[2], 
                    'hc_role': config[3], 
                    'ac_role': config[4],
                    'free_agent_role': config[5],
                    'transaction_channel': config[6],
                    'gametime_channel': None,
                    'referee_role': None,
                    'streamer_role': None,
                    'league_type': 'generic'
                }
            else:  # league_config has more columns
                # Map by column names instead of positions since league_config has more columns
                config_dict = {}
                for i, col_name in enumerate(columns):
                    if i < len(config):  # Ensure we don't go out of bounds
                        config_dict[col_name] = config[i]
                        
            # Close the database connection after we've loaded the config
            if conn:
                conn.close()
                conn = None
        
        # Permission check for team management roles            
        has_permission = False
        
        # Add admin check first - safely check if member has guild_permissions
        if hasattr(member, 'guild_permissions') and member.guild_permissions and member.guild_permissions.administrator:
            has_permission = True
        
        # Check role permissions if not admin
        if not has_permission:
            # Get the configured role IDs - properly handle null/None values
            franchise_owner_role_id = config_dict.get('franchise_owner_role')
            gm_role_id = config_dict.get('gm_role')
            hc_role_id = config_dict.get('hc_role')
            ac_role_id = config_dict.get('ac_role')
            
            # Convert None to empty string for safer comparison
            franchise_owner_role_id = str(franchise_owner_role_id) if franchise_owner_role_id else ""
            gm_role_id = str(gm_role_id) if gm_role_id else ""
            hc_role_id = str(hc_role_id) if hc_role_id else ""
            ac_role_id = str(ac_role_id) if ac_role_id else ""
            
            for role in member.roles:
                role_id_str = str(role.id)
                
                if (role_id_str == franchise_owner_role_id or 
                    role_id_str == gm_role_id or 
                    role_id_str == hc_role_id or 
                    role_id_str == ac_role_id):
                    has_permission = True
                    break
        
        if not has_permission:
            return await interaction.followup.send(
                'You do not have permission to sign players. You need to have one of these roles: Franchise Owner, General Manager, Head Coach, or Assistant Coach.',
                ephemeral=True
            )
            
        # Get the team of the user - first try with the generic teams table
        user_team = None
        
        # Connect to database for team lookup
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Make sure the teams table exists
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS teams (
            role_id TEXT NOT NULL,
            guild_id TEXT NOT NULL,
            team_name TEXT NOT NULL,
            league TEXT NOT NULL DEFAULT 'NFL',
            emoji TEXT NOT NULL
        )
        ''')
        
        for role in member.roles:
            cursor.execute("SELECT * FROM teams WHERE role_id = ? AND guild_id = ?", 
                           (str(role.id), str(interaction.guild.id)))
            team = cursor.fetchone()
            if team:
                # Make sure team array has enough elements to prevent index errors
                if len(team) >= 5:
                    user_team = {
                        'roleId': team[0],
                        'guildId': team[1],
                        'teamName': team[2],
                        'league': team[3],
                        'emoji': team[4]
                    }
                    break
        
        # If not found, try with the old nfl_teams table
        if not user_team:
            for role in member.roles:
                cursor.execute("SELECT * FROM nfl_teams WHERE role_id = ? AND guild_id = ?", 
                               (str(role.id), str(interaction.guild.id)))
                team = cursor.fetchone()
                if team:
                    # Check if we have enough elements (role_id, guild_id, team_name, emoji)
                    if len(team) >= 4:
                        user_team = {
                            'roleId': team[0],
                            'guildId': team[1],
                            'teamName': team[2],
                            'league': config_dict.get('league_type', 'NFL'),  # Use config league type
                            'emoji': team[4] if len(team) > 4 else "🏈"  # Default emoji if missing
                        }
                        break
        
        if not user_team:
            # Check if any teams exist in the database at all
            cursor.execute("SELECT COUNT(*) FROM teams WHERE guild_id = ?", (str(interaction.guild.id),))
            teams_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM nfl_teams WHERE guild_id = ?", (str(interaction.guild.id),))
            nfl_teams_count = cursor.fetchone()[0]
            
            if teams_count == 0 and nfl_teams_count == 0:
                if conn:
                    conn.close()
                return await interaction.followup.send(
                    'No teams have been set up in the database. Please run /auto_setup first.',
                    ephemeral=True
                )
            
            if conn:
                conn.close()
            return await interaction.followup.send(
                'You are not associated with any team or the team role cannot be found. Make sure you have the correct team role.',
                ephemeral=True
            )
        
        # Double-check that the team role exists in the server
        team_role = interaction.guild.get_role(int(user_team['roleId']))
        if not team_role:
            if conn:
                conn.close()
            return await interaction.followup.send(
                f"Error: Team role not found in the server. The database may contain outdated information. Role ID: {user_team['roleId']}",
                ephemeral=True
            )
        
        # Check if player is already on a team
        player_member = interaction.guild.get_member(player.id)
        if not player_member:
            try:
                # Attempt to fetch the player member from the API if not in cache
                player_member = await interaction.guild.fetch_member(player.id)
            except discord.errors.NotFound:
                if conn:
                    conn.close()
                return await interaction.followup.send(
                    f'Could not find {player.display_name} in this server.',
                    ephemeral=True
                )
            
        # Check teams table
        player_on_team = False
        for role in player_member.roles:
            cursor.execute("SELECT * FROM teams WHERE role_id = ? AND guild_id = ?", 
                           (str(role.id), str(interaction.guild.id)))
            if cursor.fetchone():
                player_on_team = True
                break
        
        # Check nfl_teams table as fallback if not found in teams table
        if not player_on_team:
            for role in player_member.roles:
                cursor.execute("SELECT * FROM nfl_teams WHERE role_id = ? AND guild_id = ?", 
                               (str(role.id), str(interaction.guild.id)))
                if cursor.fetchone():
                    player_on_team = True
                    break
        
        if player_on_team:
            if conn:
                conn.close()
            return await interaction.followup.send(
                f'{player.display_name} is already on a team and cannot be signed.',
                ephemeral=True
            )
        
        # Get current time for transaction
        current_time = datetime.utcnow()
        
        # Create the transaction embed with timestamp
        sign_embed = discord.Embed(
            title=f"{interaction.guild.name} - Player Signing",
            description=f"{user_team['emoji']} {team_role.mention} signed {player.mention}",
            color=discord.Color.green(),
            timestamp=current_time
        )
        
        # Format the footer to include the time
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S UTC")
        sign_embed.set_footer(
            text=f"Signed by {interaction.user.display_name} | {formatted_time}", 
            icon_url=interaction.user.display_avatar.url
        )
        
        # Send the transaction embed to the transaction channel
        if config_dict.get('transaction_channel'):
            try:
                transaction_channel = interaction.guild.get_channel(int(config_dict['transaction_channel']))
                if not transaction_channel:
                    # Try to fetch the channel if not in cache
                    transaction_channel = await interaction.guild.fetch_channel(int(config_dict['transaction_channel']))
                if transaction_channel:
                    await transaction_channel.send(embed=sign_embed)
            except Exception as channel_error:
                # Log the error but don't send it to the user
                print(f"Error sending to transaction channel: {channel_error}")
        
        # Remove free agent role and add team role
        try:
            # Remove free agent role if it exists
            if config_dict.get('free_agent_role'):
                try:
                    free_agent_role_id = int(config_dict['free_agent_role'])
                    free_agent_role = interaction.guild.get_role(free_agent_role_id)
                    if not free_agent_role:
                        # Role not in cache, try to ensure role data is up to date
                        await interaction.guild.fetch_roles()
                        free_agent_role = interaction.guild.get_role(free_agent_role_id)
                    
                    if free_agent_role and free_agent_role in player_member.roles:
                        await player_member.remove_roles(free_agent_role)
                except Exception as role_error:
                    # Log the error but continue
                    print(f"Error removing free agent role: {role_error}")
            
            # Add team role
            await player_member.add_roles(team_role)
        except Exception as role_error:
            if conn:
                conn.close()
            return await interaction.followup.send(
                f'Error modifying roles. Please check bot permissions and try again. Error: {str(role_error)}',
                ephemeral=True
            )
        
        # Store transaction in database
        try:
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                team_id TEXT,
                player_id TEXT,
                transaction_type TEXT,
                executor_id TEXT,
                timestamp TEXT
            )
            ''')
            
            cursor.execute('''
            INSERT INTO transactions (guild_id, team_id, player_id, transaction_type, executor_id, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                str(interaction.guild.id),
                user_team['roleId'],
                str(player.id),
                'sign',
                str(interaction.user.id),
                formatted_time
            ))
            
            conn.commit()
        except Exception as db_error:
            # Log the error but continue
            print(f"Error recording transaction in database: {db_error}")
        
        if conn:
            conn.close()
        await interaction.followup.send(
            f"Successfully signed {player.display_name} to your team!",
            ephemeral=False
        )
        
    except Exception as error:
        # Log the full error for debugging
        print(f"Error signing player: {error}")
        try:
            if conn:
                conn.close()
        except:
            pass
        # Send a simplified error message to the user
        await interaction.followup.send(
            f'There was an error while signing the player: {str(error)}',
            ephemeral=True
        )
import datetime
@bot.tree.command(name="release", description="Release a player from your team")
@app_commands.describe(player="The player to release")
async def release(interaction: discord.Interaction, player: discord.User):
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    try:
        # Make sure we're in a guild context
        if not interaction.guild:
            return await interaction.followup.send(
                'This command can only be used in a server.',
                ephemeral=True
            )
            
        guild_id = interaction.guild.id
        config = None
        
        # First try to get config from the league_config.db (new setup method)
        conn = sqlite3.connect('league_config.db')
        cursor = conn.cursor()
        
        # Check if guild has config in the new database
        cursor.execute('SELECT * FROM configs WHERE guild_id = ?', (guild_id,))
        config_row = cursor.fetchone()
        
        if config_row:
            # Create config object from the database row
            config = LeagueConfig(guild_id)
        else:
            # Fall back to legacy JSON or old database methods
            # Try to load config from JSON file (intermediate method)
            json_config_path = f"league_configs/{guild_id}.json"
            if os.path.exists(json_config_path):
                with open(json_config_path, "r") as f:
                    json_config = json.load(f)
                    
                # Create a LeagueConfig object and populate it from JSON
                config = LeagueConfig()
                config.guild_id = guild_id
                config.league_type = json_config.get('league_type', 'generic')
                config.franchise_owner_role = int(json_config.get('franchise_owner_role')) if json_config.get('franchise_owner_role') else None
                config.gm_role = int(json_config.get('gm_role')) if json_config.get('gm_role') else None
                config.hc_role = int(json_config.get('hc_role')) if json_config.get('hc_role') else None
                config.ac_role = int(json_config.get('ac_role')) if json_config.get('ac_role') else None
                config.free_agent_role = int(json_config.get('free_agent_role')) if json_config.get('free_agent_role') else None
                config.referee_role = int(json_config.get('referee_role')) if json_config.get('referee_role') else None
                config.streamer_role = int(json_config.get('streamer_role')) if json_config.get('streamer_role') else None
                config.transaction_channel = int(json_config.get('transaction_channel')) if json_config.get('transaction_channel') else None
                config.gametime_channel = int(json_config.get('gametime_channel')) if json_config.get('gametime_channel') else None
            else:
                # Try to get from legacy database
                legacy_db_path = DB_FILE  # Assuming DB_FILE is defined elsewhere
                if os.path.exists(legacy_db_path):
                    legacy_conn = sqlite3.connect(legacy_db_path)
                    legacy_cursor = legacy_conn.cursor()
                    
                    # Try to find config in various legacy tables
                    legacy_tables = ['league_config', 'server_config', 'nfl_config']
                    legacy_config = None
                    
                    for table in legacy_tables:
                        try:
                            legacy_cursor.execute(f"SELECT * FROM {table} WHERE guild_id = ?", (str(guild_id),))
                            legacy_row = legacy_cursor.fetchone()
                            if legacy_row:
                                # Get the column names
                                legacy_cursor.execute(f"PRAGMA table_info({table})")
                                columns = [column[1] for column in legacy_cursor.fetchall()]
                                
                                # Create a dictionary with the config values
                                legacy_config = {}
                                for i, col_name in enumerate(columns):
                                    if i < len(legacy_row):
                                        legacy_config[col_name] = legacy_row[i]
                                
                                break  # Found a config, no need to check other tables
                        except sqlite3.OperationalError:
                            # Table doesn't exist, continue to next one
                            continue
                    
                    legacy_conn.close()
                    
                    if legacy_config:
                        # Create a LeagueConfig object and populate it from legacy config
                        config = LeagueConfig()
                        config.guild_id = guild_id
                        config.league_type = legacy_config.get('league_type', 'generic')
                        config.franchise_owner_role = int(legacy_config.get('franchise_owner_role')) if legacy_config.get('franchise_owner_role') else None
                        config.gm_role = int(legacy_config.get('gm_role')) if legacy_config.get('gm_role') else None
                        config.hc_role = int(legacy_config.get('hc_role')) if legacy_config.get('hc_role') else None
                        config.ac_role = int(legacy_config.get('ac_role')) if legacy_config.get('ac_role') else None
                        config.free_agent_role = int(legacy_config.get('free_agent_role')) if legacy_config.get('free_agent_role') else None
                        
                        # Some legacy tables might not have these fields
                        if 'referee_role' in legacy_config:
                            config.referee_role = int(legacy_config.get('referee_role')) if legacy_config.get('referee_role') else None
                        if 'streamer_role' in legacy_config:
                            config.streamer_role = int(legacy_config.get('streamer_role')) if legacy_config.get('streamer_role') else None
                        
                        config.transaction_channel = int(legacy_config.get('transaction_channel')) if legacy_config.get('transaction_channel') else None
                        
                        if 'gametime_channel' in legacy_config:
                            config.gametime_channel = int(legacy_config.get('gametime_channel')) if legacy_config.get('gametime_channel') else None
                        
                        # Save this config to the new database for future use
                        config.save_to_db()
        
        # Close the main database connection
        conn.close()
        
        if not config:
            return await interaction.followup.send(
                'Server is not set up yet. Please use /setup_league first.',
                ephemeral=True
            )
        
        # Get the user's member object - Fix the error by retrieving member differently and checking if it exists
        member = interaction.guild.get_member(interaction.user.id)
        if not member:
            # Try to fetch the member if it's not in the cache
            try:
                member = await interaction.guild.fetch_member(interaction.user.id)
            except discord.errors.NotFound:
                return await interaction.followup.send(
                    'Could not find your user in this server. Please try again later.',
                    ephemeral=True
                )
        
        has_permission = False
        
        # Admin check first - use member.guild_permissions safely
        if member and member.guild_permissions and member.guild_permissions.administrator:
            has_permission = True
        
        # Get staff role IDs
        staff_roles = {
            'franchise_owner_role': config.franchise_owner_role,
            'gm_role': config.gm_role,
            'hc_role': config.hc_role,
            'ac_role': config.ac_role
        }
        
        # Check if user has any of the staff roles
        user_staff_role = None
        if member:
            for role_name, role_id in staff_roles.items():
                if role_id and any(role.id == role_id for role in member.roles):
                    has_permission = True
                    user_staff_role = role_name
                    break
        
        if not has_permission:
            return await interaction.followup.send(
                'You do not have permission to release players. You need to be an Administrator, Franchise Owner, General Manager, Head Coach, or Assistant Coach.',
                ephemeral=True
            )
        
        # Connect to database to check team information
        try:
            teams_db = sqlite3.connect(DB_FILE)  # Assuming DB_FILE is defined elsewhere
            teams_cursor = teams_db.cursor()
            
            # Get the team of the user
            user_team = None
            
            for role in member.roles:
                # Check generic teams table first
                teams_cursor.execute("SELECT * FROM teams WHERE role_id = ? AND guild_id = ?", 
                                   (str(role.id), str(guild_id)))
                team = teams_cursor.fetchone()
                if team:
                    user_team = {
                        'roleId': team[0],
                        'guildId': team[1],
                        'teamName': team[2],
                        'league': team[3],
                        'emoji': team[4]
                    }
                    break
            
            # If not found, try with the old nfl_teams table
            if not user_team:
                for role in member.roles:
                    teams_cursor.execute("SELECT * FROM nfl_teams WHERE role_id = ? AND guild_id = ?", 
                                       (str(role.id), str(guild_id)))
                    team = teams_cursor.fetchone()
                    if team:
                        user_team = {
                            'roleId': team[0],
                            'guildId': team[1],
                            'teamName': team[2],
                            'league': 'NFL',  # Assuming NFL for old table
                            'emoji': team[3]
                        }
                        break
            
            teams_db.close()
            
            if not user_team:
                return await interaction.followup.send(
                    'You are not associated with any team.',
                    ephemeral=True
                )
        except Exception as db_error:
            return await interaction.followup.send(
                f'Error accessing team database: {db_error}',
                ephemeral=True
            )
        
        # Check if player is on the user's team
        player_member = interaction.guild.get_member(player.id)
        if not player_member:
            # Try to fetch the player if not in cache
            try:
                player_member = await interaction.guild.fetch_member(player.id)
            except discord.errors.NotFound:
                return await interaction.followup.send(
                    f'Could not find {player.display_name} in this server.',
                    ephemeral=True
                )
            
        if not any(role.id == int(user_team['roleId']) for role in player_member.roles):
            return await interaction.followup.send(
                f'{player.display_name} is not on your team and cannot be released.',
                ephemeral=True
            )
        
        # Create the transaction embed
        # Include the staff role that initiated the release
        staff_role_name = user_staff_role.replace('_role', '').replace('_', ' ').title() if user_staff_role else "Staff"
        
        release_embed = discord.Embed(
            title=f"{interaction.guild.name} - Player Release",
            description=f"{user_team['emoji']} <@&{user_team['roleId']}> released <@{player.id}> to free agency",
            color=discord.Color.red()
            # timestamp removed to fix the error
        )
        release_embed.set_footer(text=f"Released by {interaction.user.display_name} ({staff_role_name})", icon_url=interaction.user.display_avatar.url)
        
        # Send the transaction embed to the transaction channel
        if config.transaction_channel:
            transaction_channel = interaction.guild.get_channel(config.transaction_channel)
            if transaction_channel:
                await transaction_channel.send(embed=release_embed)
        
        # Remove the team role from the player and add free agent role
        await player_member.remove_roles(discord.Object(id=int(user_team['roleId'])))
        if config.free_agent_role:
            try:
                await player_member.add_roles(discord.Object(id=config.free_agent_role))
            except Exception as role_error:
                print(f"Error adding free agent role: {role_error}")
                # Continue execution, as this is not critical
        
        await interaction.followup.send(
            f"Successfully released {player.display_name} from your team!",
            ephemeral=False
        )
    except Exception as error:
        print(f"Error releasing player: {error}")
        await interaction.followup.send(
            f'There was an error while releasing the player: {error}. Please try again later.',
            ephemeral=True
        )
@bot.tree.command(name="auto_setup", description="Automatically configure basic league settings by detecting relevant roles and channels")
async def auto_setup(interaction: discord.Interaction):
    try:
        # First, verify the user has admin permissions
        if not interaction.user.guild_permissions.administrator:
            return await interaction.response.send_message(
                'You need administrator permissions to use this command.',
                ephemeral=True
            )
        
        await interaction.response.defer(ephemeral=True)
        
        # Get all roles and channels from the server
        roles = interaction.guild.roles
        channels = interaction.guild.text_channels
        
        # Dictionary to store found configuration elements
        config = {
            'guild_id': str(interaction.guild.id),
            'franchise_owner_role': '',
            'gm_role': '',
            'hc_role': '',
            'ac_role': '',
            'free_agent_role': '',
            'referee_role': '',
            'streamer_role': '',
            'transaction_channel': '',
            'gametime_channel': '',
            'league_type': 'generic'  # Default
        }
        
        # Role patterns to look for
        role_patterns = {
            'franchise_owner_role': ['franchise owner', 'franchise', 'team owner', 'owner'],
            'gm_role': ['general manager', 'gm', 'general'],
            'hc_role': ['head coach', 'hc', 'coach'],
            'ac_role': ['assistant coach', 'ac', 'assistant'],
            'free_agent_role': ['free agent', 'free', 'unsigned player'],
            'referee_role': ['referee', 'ref', 'official', 'umpire'],
            'streamer_role': ['streamer', 'broadcaster', 'caster']
        }
        
        # Channel patterns to look for
        channel_patterns = {
            'transaction_channel': ['transaction', 'trade', 'signing', 'roster'],
            'gametime_channel': ['game time', 'gametime', 'schedule', 'matchup']
        }
        
        # Find matching roles
        roles_found = {}
        for role in roles:
            if role.is_default():
                continue
            
            role_name = role.name.lower()
            for config_key, patterns in role_patterns.items():
                for pattern in patterns:
                    if pattern in role_name:
                        roles_found[config_key] = {
                            'id': str(role.id),
                            'name': role.name
                        }
                        config[config_key] = str(role.id)
                        break
        
        # Find matching channels
        channels_found = {}
        for channel in channels:
            channel_name = channel.name.lower()
            for config_key, patterns in channel_patterns.items():
                for pattern in patterns:
                    if pattern in channel_name:
                        channels_found[config_key] = {
                            'id': str(channel.id),
                            'name': channel.name
                        }
                        config[config_key] = str(channel.id)
                        break
        
        # Try to detect league type based on roles or channels
        sports_keywords = {
            'football': ['football', 'nfl', 'quarterback', 'touchdown'],
            'basketball': ['basketball', 'nba', 'dribble', 'hoop'],
            'baseball': ['baseball', 'mlb', 'pitcher', 'homerun']
        }
        
        for role in roles:
            role_name = role.name.lower()
            for sport, keywords in sports_keywords.items():
                for keyword in keywords:
                    if keyword in role_name:
                        config['league_type'] = sport
                        break
                if config['league_type'] != 'generic':
                    break
            if config['league_type'] != 'generic':
                break
                
        # Save configuration to database
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if config exists for this guild
        cursor.execute("SELECT * FROM league_config WHERE guild_id = ?", (str(interaction.guild.id),))
        row = cursor.fetchone()
        
        if row:
            # Build dynamic update query
            set_parts = []
            values = []
            
            # Get column names from the table
            cursor.execute("PRAGMA table_info(league_config)")
            columns = [col[1] for col in cursor.fetchall()]
            
            # Create set clause for each config item that matches a column
            for col in columns:
                if col in config and col != 'guild_id':
                    set_parts.append(f"{col} = ?")
                    values.append(config.get(col, ''))
            
            # Add the WHERE condition value
            values.append(str(interaction.guild.id))
            
            # Execute the update
            cursor.execute(f"UPDATE league_config SET {', '.join(set_parts)} WHERE guild_id = ?", values)
        else:
            # Build dynamic insert query
            columns = ['guild_id']
            placeholders = ['?']
            values = [str(interaction.guild.id)]
            
            for key, value in config.items():
                if key != 'guild_id':
                    columns.append(key)
                    placeholders.append('?')
                    values.append(value)
            
            # Execute the insert
            cursor.execute(
                f"INSERT INTO league_config ({', '.join(columns)}) VALUES ({', '.join(placeholders)})",
                values
            )
        
        conn.commit()
        conn.close()
        
        # Build response message
        embed = discord.Embed(
            title="🏆 Auto Setup Complete",
            description="I've automatically configured your league based on your server setup.",
            color=0x2ECC71
        )
        
        # Add roles section
        roles_description = ""
        for key, role_data in roles_found.items():
            role_display_name = key.replace('_role', '').replace('_', ' ').title()
            roles_description += f"**{role_display_name}**: {role_data['name']}\n"
        
        if roles_description:
            embed.add_field(name="📋 Roles Detected", value=roles_description, inline=False)
        else:
            embed.add_field(name="📋 Roles", value="No matching roles were found.", inline=False)
        
        # Add channels section
        channels_description = ""
        for key, channel_data in channels_found.items():
            channel_display_name = key.replace('_channel', '').replace('_', ' ').title()
            channels_description += f"**{channel_display_name}**: {channel_data['name']}\n"
        
        if channels_description:
            embed.add_field(name="💬 Channels Detected", value=channels_description, inline=False)
        else:
            embed.add_field(name="💬 Channels", value="No matching channels were found.", inline=False)
        
        # Add league type
        embed.add_field(name="🏅 League Type", value=config['league_type'].capitalize(), inline=False)
        
        # Add next steps
        embed.add_field(
            name="Next Steps",
            value="1. Use `/setup` to review and edit your configuration\n"
                  "2. Use `/search_teams` to set up your league teams",
            inline=False
        )
        
        await interaction.followup.send(embed=embed, ephemeral=True)
            
    except Exception as error:
        print(f"Error during auto setup: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error during auto setup: {str(error)}',
            ephemeral=True
        )

@bot.tree.command(name="search_teams", description="Search and set up team roles for your league")
async def search_teams(interaction: discord.Interaction):
    try:
        # Verify the user has admin permissions
        if not interaction.user.guild_permissions.administrator:
            return await interaction.response.send_message(
                'You need administrator permissions to use this command.',
                ephemeral=True
            )
        
        await interaction.response.defer(ephemeral=True)
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Create the teams table first with consistent schema
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS league_teams (
            role_id TEXT PRIMARY KEY,
            guild_id TEXT,
            team_name TEXT,
            emoji TEXT,
            league_type TEXT DEFAULT 'generic'
        )''')
        conn.commit()
        
        # Dictionary of sports leagues and their teams
        league_teams = {
            "football": {
                # NFL Teams
                "arizona cardinals": "ArizonaCardinals",
                "atlanta falcons": "AtlantaFalcons",
                "baltimore ravens": "BaltimoreRavens",
                "buffalo bills": "BuffaloBills",
                "carolina panthers": "CarolinaPanthers",
                "chicago bears": "ChicagoBears",
                "cincinnati bengals": "CincinnatiBengals",
                "cleveland browns": "ClevelandBrowns",
                "dallas cowboys": "DallasCowboys",
                "denver broncos": "DenverBroncos",
                "detroit lions": "DetroitLions",
                "green bay packers": "GreenBayPackers",
                "houston texans": "HoustonTexans",
                "indianapolis colts": "IndianapolisColts",
                "jacksonville jaguars": "JacksonvilleJaguars",
                "kansas city chiefs": "KansasCityChiefs",
                "las vegas raiders": "LasVegasRaiders",
                "los angeles chargers": "LosAngelesChargers",
                "los angeles rams": "LosAngelesRams",
                "miami dolphins": "MiamiDolphins",
                "minnesota vikings": "MinnesotaVikings",
                "new england patriots": "NewEnglandPatriots",
                "new orleans saints": "NewOrleansSaints",
                "new york giants": "NewYorkGiants",
                "new york jets": "NewYorkJets",
                "philadelphia eagles": "PhiladelphiaEagles",
                "pittsburgh steelers": "PittsburghSteelers",
                "san francisco 49ers": "SanFrancisco49ers",
                "seattle seahawks": "SeattleSeahawks",
                "tampa bay buccaneers": "TampaBayBuccaneers",
                "tennessee titans": "TennesseeTitans",
                "washington commanders": "WashingtonCommanders",
                
                # Ultimate Football Teams - Capitalized for role name searching
                "Alabama Black Bears": "AlabamaBlackBears",
                "Arizona Firebirds": "ArizonaFirebirds",
                "Atlantis Tridents": "AtlantisTrients",
                "Barton Bruisers": "BartonBruisers",
                "Birmingham Bluebirds": "BirminghamBluebirds",
                "Canton Bulldogs": "CantonBulldogs",
                "Charlotte Monarchs": "CharlotteMonarchs",
                "Colorado Blizzards": "ColoradoBlizzards",
                "Dallas Dragons": "DallasDragons",
                "Honolulu Volcanoes": "HonoluluVolcanoes",
                "Houston Hornets": "HoustonHornets",
                "Korblox Kobras": "KorbloxKobras",
                "Las Vegas Jackpots": "LasVegasJackpots",
                "Los Angeles Tigers": "LosAngelesTigers",
                "Lua Lions": "LuaLions",
                "Mexico City Aztecs": "MexicoCityAztecs",
                "Miami Sunshine": "MiamiSunshine",
                "Minnesota Huskies": "MinnesotaHuskies",
                "Nashville Nightmares": "NashvilleNightmares",
                "Nevada Miners": "NevadaMiners",
                "New England Muskateers": "NewEnglandMuskateers",
                "New York Knights": "NewYorkKnights",
                "Oklahoma Storm Chasers": "OklahomaStormChasers",
                "Orlando Lightning": "OrlandoLightning",
                "Pemberely Punishers": "PemberelyPunishers",
                "Philadelphia Liberties": "PhiladelphiaLiberties",
                "Roblox Warriors": "RobloxWarriors",
                "Salt Lake City Stallions": "SaltLakeCityStallions",
                "San Diego Cruisers": "SanDiegoCruisers",
                "San Francisco Comets": "SanFranciscoComets",
                "Santa Fe Outlaws": "SantaFeOutlaws",
                "Seattle Evergreens": "SeattleEvergreens"
            },
            "basketball": {
                "atlanta hawks": "AtlantaHawks",
                "boston celtics": "BostonCeltics",
                "brooklyn nets": "BrooklynNets",
                "charlotte hornets": "CharlotteHornets",
                "chicago bulls": "ChicagoBulls",
                "cleveland cavaliers": "ClevelandCavaliers",
                "dallas mavericks": "DallasMavericks",
                "denver nuggets": "DenverNuggets",
                "detroit pistons": "DetroitPistons",
                "golden state warriors": "GoldenStateWarriors",
                "houston rockets": "HoustonRockets",
                "indiana pacers": "IndianaPacers",
                "los angeles clippers": "LosAngelesClippers",
                "los angeles lakers": "LosAngelesLakers",
                "memphis grizzlies": "MemphisGrizzlies",
                "miami heat": "MiamiHeat",
                "milwaukee bucks": "MilwaukeeBucks",
                "minnesota timberwolves": "MinnesotaTimberwolves",
                "new orleans pelicans": "NewOrleansPelicans",
                "new york knicks": "NewYorkKnicks",
                "oklahoma city thunder": "OklahomaCityThunder",
                "orlando magic": "OrlandoMagic",
                "philadelphia 76ers": "Philadelphia76ers",
                "phoenix suns": "PhoenixSuns",
                "portland trail blazers": "PortlandTrailBlazers",
                "sacramento kings": "SacramentoKings",
                "san antonio spurs": "SanAntonioSpurs",
                "toronto raptors": "TorontoRaptors",
                "utah jazz": "UtahJazz",
                "washington wizards": "WashingtonWizards"
            },
            "custom": {
                # This will be populated with custom teams
            },
            "generic": {
                # This will be populated with custom teams
            }
        }
        
        # Rest of the function remains the same
        # Get current league type
        cursor.execute("SELECT league_type FROM league_config WHERE guild_id = ?", (str(interaction.guild.id),))
        result = cursor.fetchone()
        league_type = "generic"
        if result and result[0]:
            league_type = result[0]
        
        # Get team mapping based on league type
        team_mapping = league_teams.get(league_type, league_teams["generic"])
        
        # Look for team roles in the guild
        team_roles_found = []
        generic_team_roles = []
        
        # Check for teams based on league type
        for role in interaction.guild.roles:
            role_name = role.name
            role_name_lower = role_name.lower()
            
            # For non-generic leagues, check against predefined teams
            if league_type != "generic":
                for team_name, emoji_name in team_mapping.items():
                    # Modified to compare case-sensitively for Ultimate Football teams
                    if team_name in role_name or team_name.lower() in role_name_lower:
                        # Find matching emoji if it exists
                        emoji = None
                        for guild_emoji in interaction.guild.emojis:
                            if emoji_name.lower() in guild_emoji.name.lower():
                                emoji = str(guild_emoji)
                                break
                        
                        # If no custom emoji found, use a default text emoji
                        if not emoji:
                            if league_type == "football":
                                emoji = "🏈"
                            elif league_type == "basketball":
                                emoji = "🏀"
                            else:
                                emoji = "🏆"
                        
                        # Use the case as provided in the dictionary for the team name
                        team_display_name = team_name
                        
                        # Add to found teams list
                        team_roles_found.append({
                            "role": role,
                            "team_name": team_display_name,
                            "emoji": emoji,
                            "league_type": league_type
                        })
                        break
            
            # For generic league, look for roles that might be teams
            elif not role.is_default() and "team" in role_name_lower:
                # Use role name as team name
                team_name = role.name
                emoji = "🏆"  # Default emoji for generic teams
                
                generic_team_roles.append({
                    "role": role,
                    "team_name": team_name,
                    "emoji": emoji,
                    "league_type": "generic"
                })
        
        # If no predefined teams found, use generic teams
        if not team_roles_found and generic_team_roles:
            team_roles_found = generic_team_roles
        
        # If no team roles found at all
        if not team_roles_found:
            conn.close()
            return await interaction.followup.send(
                f'No team roles were found for {league_type.title()} league. Please make sure you have roles named after teams.',
                ephemeral=True
            )
        
        # Add the teams to the database
        teams_added = 0
        for team in team_roles_found:
            try:
                # Check if team already exists
                cursor.execute("SELECT role_id FROM league_teams WHERE role_id = ? AND guild_id = ?", 
                             (str(team["role"].id), str(interaction.guild.id)))
                if not cursor.fetchone():
                    # Insert into league_teams table
                    cursor.execute('''
                    INSERT INTO league_teams (role_id, guild_id, team_name, emoji, league_type) 
                    VALUES (?, ?, ?, ?, ?)
                    ''', (str(team["role"].id), str(interaction.guild.id), team["team_name"], team["emoji"], team["league_type"]))
                    
                    teams_added += 1
            except sqlite3.IntegrityError:
                # Skip teams that violate integrity constraints (already exist)
                continue
        
        conn.commit()
        
        # Now check for franchise owners and GMs
        owner_info = None
        gm_info = None
        
        # Check if franchise owner and GM roles are set
        cursor.execute('''
        SELECT franchise_owner_role, gm_role FROM league_config WHERE guild_id = ?
        ''', (str(interaction.guild.id),))
        config = cursor.fetchone()
        
        if config and config[0]:  # If franchise owner role is set
            try:
                franchise_owner_role_id = config[0]
                fo_role = interaction.guild.get_role(int(franchise_owner_role_id))
                
                if fo_role and fo_role.members:
                    owner_info = discord.Embed(
                        title="Franchise Owners",
                        description="List of franchise owners and their teams",
                        color=discord.Color.blue()
                    )
                    
                    owners_found = False
                    for member in fo_role.members:
                        member_teams = []
                        for team in team_roles_found:
                            if team["role"] in member.roles:
                                member_teams.append(f"{team['emoji']} {team['team_name']}")
                        
                        if member_teams:
                            owners_found = True
                            owner_info.add_field(
                                name=member.display_name,
                                value="\n".join(member_teams),
                                inline=False
                            )
                    
                    if not owners_found:
                        owner_info.description = "No franchise owners found with team roles."
            except Exception as e:
                print(f"Error processing franchise owners: {e}")
        
        if config and config[1]:  # If GM role is set
            try:
                gm_role_id = config[1]
                gm_role = interaction.guild.get_role(int(gm_role_id))
                
                if gm_role and gm_role.members:
                    gm_info = discord.Embed(
                        title="General Managers",
                        description="List of general managers and their teams",
                        color=discord.Color.green()
                    )
                    
                    gms_found = False
                    for member in gm_role.members:
                        member_teams = []
                        for team in team_roles_found:
                            if team["role"] in member.roles:
                                member_teams.append(f"{team['emoji']} {team['team_name']}")
                        
                        if member_teams:
                            gms_found = True
                            gm_info.add_field(
                                name=member.display_name,
                                value="\n".join(member_teams),
                                inline=False
                            )
                    
                    if not gms_found:
                        gm_info.description = "No general managers found with team roles."
            except Exception as e:
                print(f"Error processing general managers: {e}")
        
        conn.close()
        
        # Create team search results embed
        teams_embed = discord.Embed(
            title=f"🏆 Team Search Results - {league_type.title()} League",
            description=f"Found {len(team_roles_found)} team roles and added {teams_added} new teams to the database.",
            color=0x3498DB
        )
        
        # Add team list (group in batches of 10 to avoid too many fields)
        team_chunks = [team_roles_found[i:i+10] for i in range(0, len(team_roles_found), 10)]
        
        for i, chunk in enumerate(team_chunks):
            team_list = ""
            for team in chunk:
                team_list += f"{team['emoji']} {team['team_name']}\n"
            
            teams_embed.add_field(
                name=f"Teams {i*10+1}-{i*10+len(chunk)}",
                value=team_list,
                inline=True
            )
        
        # Send team search results
        await interaction.followup.send(embed=teams_embed, ephemeral=True)
        
        # Send franchise owners embed if exists
        if owner_info:
            await interaction.channel.send(embed=owner_info)
        
        # Send general managers embed if exists
        if gm_info:
            await interaction.channel.send(embed=gm_info)
            
    except Exception as error:
        print(f"Error during team search: {error}")
        traceback.print_exc()
        try:
            if conn:
                conn.close()
        except:
            pass
        await interaction.followup.send(
            f'There was an error during team search: {str(error)}',
            ephemeral=True
        )
from discord import app_commands, Interaction, SelectOption, ButtonStyle, ui
from discord.ui import Button, View, Select
import discord
from datetime import datetime, timedelta
import pytz
import sqlite3

DB_FILE = "league_bot.db"

@bot.tree.command(name="schedule", description="Schedule a game with an opponent")
@app_commands.describe(
    opponent_team="Mention the opponent team role",
    time="Enter the time (e.g., 8:00)",
    am_or_pm="Select AM or PM",
    day="Select the day for the game",
    timezone="Select the timezone for the game"
)
@app_commands.choices(
    am_or_pm=[
        app_commands.Choice(name="AM", value="AM"),
        app_commands.Choice(name="PM", value="PM")
    ],
    day=[
        app_commands.Choice(name="Today", value="today"),
        app_commands.Choice(name="Tomorrow", value="tomorrow"),
        app_commands.Choice(name="In 2 days", value="in_2_days"),
        app_commands.Choice(name="In 3 days", value="in_3_days")
    ],
    timezone=[
        app_commands.Choice(name="Eastern", value="US/Eastern"),
        app_commands.Choice(name="Central", value="US/Central"),
        app_commands.Choice(name="Mountain", value="US/Mountain"),
        app_commands.Choice(name="Pacific", value="US/Pacific")
    ]
)
async def schedule(
    interaction: Interaction, 
    opponent_team: discord.Role, 
    time: str, 
    am_or_pm: app_commands.Choice[str],
    day: app_commands.Choice[str],
    timezone: app_commands.Choice[str]
):
    # Get config from database
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Get column names
        cursor.execute(f"PRAGMA table_info(league_config)")
        columns = [col[1] for col in cursor.fetchall()]
        
        # Get config
        cursor.execute("SELECT * FROM league_config WHERE guild_id = ?", (str(interaction.guild.id),))
        config = cursor.fetchone()
        
        if not config:
            await interaction.response.send_message("Server not configured. Use /setup_manual first.", ephemeral=True)
            return
        
        # Extract roles from config
        config_dict = {columns[i]: config[i] for i in range(len(columns))}
        
        franchise_owner_role = config_dict.get('franchise_owner_role')
        gm_role = config_dict.get('gm_role')
        hc_role = config_dict.get('hc_role')
        ac_role = config_dict.get('ac_role')
        gametime_channel = config_dict.get('gametime_channel')
        referee_role = config_dict.get('referee_role')
        streamer_role = config_dict.get('streamer_role')
        
        # Check if user has one of the allowed roles
        allowed_role_ids = [franchise_owner_role, gm_role, hc_role, ac_role]
        user_role_ids = [str(role.id) for role in interaction.user.roles]
        
        if not any(role_id in user_role_ids for role_id in allowed_role_ids if role_id):
            await interaction.response.send_message("You don't have the required role to use this command.", ephemeral=True)
            return
        
        if not gametime_channel:
            await interaction.response.send_message("Game time channel not configured. Use /setup_manual first.", ephemeral=True)
            return
        
        # Get user's team role - improved to prevent non-team roles from being identified as team roles
        user_team_role = None
        user_team_emoji = None
        
        # Check all tables for team assignments (since auto_setup uses different tables)
        tables_to_check = ['teams', 'league_teams', 'nfl_teams']
        
        for role in interaction.user.roles:
            role_id_str = str(role.id)
            
            # Skip roles that are in the allowed_role_ids list to avoid misidentification
            if role_id_str in allowed_role_ids:
                continue
                
            # Skip referee and streamer roles to avoid misidentification    
            if role_id_str in [referee_role, streamer_role]:
                continue
            
            # Check in each table
            for table in tables_to_check:
                emoji_column = "emoji"
                
                if table == "nfl_teams":
                    # Check the nfl_teams table (used by auto_setup)
                    cursor.execute(f"SELECT team_name, {emoji_column} FROM {table} WHERE role_id = ? AND guild_id = ?", 
                                  (role_id_str, str(interaction.guild.id)))
                    result = cursor.fetchone()
                    
                    if result:
                        user_team_role = role
                        user_team_emoji = result[1] if result[1] else ":shield:"
                        break
                else:
                    # Check the teams and league_teams tables
                    cursor.execute(f"SELECT team_name, {emoji_column} FROM {table} WHERE role_id = ? AND guild_id = ?", 
                                  (role_id_str, str(interaction.guild.id)))
                    result = cursor.fetchone()
                    
                    if result:
                        user_team_role = role
                        user_team_emoji = result[1] if result[1] else ":shield:"
                        break
            
            if user_team_role:
                break
        
        if not user_team_role:
            # Fallback: check roles with "team" in the name but not "referee" or "streamer"
            user_team_role = next((role for role in interaction.user.roles 
                                if "team" in role.name.lower() 
                                and "referee" not in role.name.lower() 
                                and "streamer" not in role.name.lower()), None)
            user_team_emoji = ":shield:"
            
        if not user_team_role:
            await interaction.response.send_message("You don't have a team role assigned.", ephemeral=True)
            return
        
        # Get opponent team emoji from any of the tables
        opponent_team_emoji = None
        opponent_role_id = str(opponent_team.id)
        
        # Try to find the opponent in any of the team tables
        for table in tables_to_check:
            emoji_column = "emoji"
            
            if table == "nfl_teams":
                cursor.execute(f"SELECT {emoji_column} FROM {table} WHERE role_id = ? AND guild_id = ?", 
                              (opponent_role_id, str(interaction.guild.id)))
            else:
                cursor.execute(f"SELECT {emoji_column} FROM {table} WHERE role_id = ? AND guild_id = ?", 
                              (opponent_role_id, str(interaction.guild.id)))
                
            result = cursor.fetchone()
            if result:
                opponent_team_emoji = result[0] if result[0] else ":shield:"
                break
        
        # If we still don't have an emoji, check if the role name contains a team name
        if not opponent_team_emoji:
            # NFL teams are common in auto_setup, check if role name contains team name
            nfl_team_names = [
                "Cardinals", "Falcons", "Ravens", "Bills", "Panthers", "Bears", "Bengals", "Browns", 
                "Cowboys", "Broncos", "Lions", "Packers", "Texans", "Colts", "Jaguars", "Chiefs", 
                "Raiders", "Chargers", "Rams", "Dolphins", "Vikings", "Patriots", "Saints", "Giants", 
                "Jets", "Eagles", "Steelers", "49ers", "Seahawks", "Buccaneers", "Titans", "Commanders"
            ]
            
            for team_name in nfl_team_names:
                if team_name.lower() in opponent_team.name.lower():
                    opponent_team_emoji = ":football:"
                    break
        
        # Default emoji if not found
        if not opponent_team_emoji:
            opponent_team_emoji = ":shield:"
        
        # Parse the time
        try:
            hour, minute = map(int, time.split(':'))
            
            # Convert to 24-hour format
            if am_or_pm.value == "PM" and hour != 12:
                hour += 12
            elif am_or_pm.value == "AM" and hour == 12:
                hour = 0
                
            # Calculate the game date
            days_mapping = {
                "today": 0,
                "tomorrow": 1,
                "in_2_days": 2,
                "in_3_days": 3
            }
            
            today = datetime.now()
            game_date = today + timedelta(days=days_mapping[day.value])
            game_datetime = game_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # Use the selected timezone
            user_timezone = pytz.timezone(timezone.value)
            game_datetime = user_timezone.localize(game_datetime)
            
            # Convert game time to all timezones
            timezones = ["US/Eastern", "US/Central", "US/Mountain", "US/Pacific"]
            time_display = ""
            for tz in timezones:
                tz_datetime = game_datetime.astimezone(pytz.timezone(tz))
                time_display += f"{tz_datetime.strftime('%I:%M %p')} {tz.split('/')[-1]}\n"
            
            # Calculate the countdown time
            now = datetime.now(pytz.utc)
            delta = game_datetime.astimezone(pytz.utc) - now
            days = delta.days
            hours, remainder = divmod(delta.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            countdown = f"{days}d {hours}h {minutes}m"
            
            # Get game channel
            game_channel_id = gametime_channel.strip("<>#") if gametime_channel else None
            if not game_channel_id:
                await interaction.response.send_message("Game time channel not configured.", ephemeral=True)
                return
            
            game_channel = interaction.guild.get_channel(int(game_channel_id))
            if not game_channel:
                await interaction.response.send_message("Game time channel not found.", ephemeral=True)
                return
            
            # Create the message
            message_content = (
                f"**{interaction.guild.name}**\n"
                f"{user_team_emoji} {user_team_role.mention} vs {opponent_team_emoji} {opponent_team.mention}\n"
                f":clock: {time_display}:stopwatch: {countdown}\n"
                f"🕶 No referees assigned\n"
                f"🎥 No streamer assigned"
            )
            
            # Create and send the game message with buttons
            class GameView(View):
                def __init__(self):
                    super().__init__(timeout=None)  # No timeout for game buttons
                    self.referees = []
                    self.streamer = None
                
                @discord.ui.button(label="Referee", style=ButtonStyle.primary)
                async def referee_button(self, ref_interaction: Interaction, button: Button):
                    # Check if user has referee role
                    if referee_role and str(referee_role) not in [str(role.id) for role in ref_interaction.user.roles]:
                        await ref_interaction.response.send_message("You don't have the referee role.", ephemeral=True)
                        return
                    
                    # Toggle referee status
                    if ref_interaction.user.id in self.referees:
                        # Remove referee
                        self.referees.remove(ref_interaction.user.id)
                        
                        # Update referee line in message
                        if not self.referees:
                            ref_text = "🕶 No referees assigned"
                        else:
                            ref_mentions = " ".join([f"<@{ref_id}>" for ref_id in self.referees])
                            ref_text = f"🕶 {ref_mentions}"
                        
                        message_lines = ref_interaction.message.content.split('\n')
                        for i, line in enumerate(message_lines):
                            if "🕶" in line:
                                message_lines[i] = ref_text
                        
                        await ref_interaction.message.edit(content='\n'.join(message_lines))
                        await ref_interaction.response.send_message("You've been removed as a referee.", ephemeral=True)
                    else:
                        # Add referee (max 2)
                        if len(self.referees) < 2:
                            self.referees.append(ref_interaction.user.id)
                            
                            # Update referee line in message
                            ref_mentions = " ".join([f"<@{ref_id}>" for ref_id in self.referees])
                            ref_text = f"🕶 {ref_mentions}"
                            
                            message_lines = ref_interaction.message.content.split('\n')
                            for i, line in enumerate(message_lines):
                                if "🕶" in line:
                                    message_lines[i] = ref_text
                            
                            await ref_interaction.message.edit(content='\n'.join(message_lines))
                            await ref_interaction.response.send_message("You've been assigned as a referee.", ephemeral=True)
                        else:
                            await ref_interaction.response.send_message("There are already 2 referees assigned.", ephemeral=True)
                
                @discord.ui.button(label="Streamer", style=ButtonStyle.primary)
                async def streamer_button(self, stream_interaction: Interaction, button: Button):
                    # Check if user has streamer role
                    if streamer_role and str(streamer_role) not in [str(role.id) for role in stream_interaction.user.roles]:
                        await stream_interaction.response.send_message("You don't have the streamer role.", ephemeral=True)
                        return
                    
                    # Toggle streamer status
                    if self.streamer == stream_interaction.user.id:
                        # Remove streamer
                        self.streamer = None
                        
                        # Update streamer line in message
                        streamer_text = f"🎥 No streamer assigned"
                        
                        message_lines = stream_interaction.message.content.split('\n')
                        for i, line in enumerate(message_lines):
                            if "🎥" in line:
                                message_lines[i] = streamer_text
                        
                        await stream_interaction.message.edit(content='\n'.join(message_lines))
                        await stream_interaction.response.send_message("You've been removed as the streamer.", ephemeral=True)
                    else:
                        # Set user as streamer
                        self.streamer = stream_interaction.user.id
                        
                        # Update streamer line in message
                        streamer_text = f"🎥 <@{self.streamer}>"
                        
                        message_lines = stream_interaction.message.content.split('\n')
                        for i, line in enumerate(message_lines):
                            if "🎥" in line:
                                message_lines[i] = streamer_text
                        
                        await stream_interaction.message.edit(content='\n'.join(message_lines))
                        await stream_interaction.response.send_message("You've been assigned as the streamer.", ephemeral=True)
            
            game_view = GameView()
            
            # Send the game message
            await game_channel.send(content=message_content, view=game_view)
            await interaction.response.send_message(f"Game scheduled successfully in {game_channel.mention}!", ephemeral=True)
            
        except Exception as e:
            await interaction.response.send_message(f"Error: {str(e)}", ephemeral=True)
            
    except Exception as e:
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)
    finally:
        # Make sure to close the connection
        if conn:
            conn.close()
@bot.tree.command(name="remove_team", description="Remove a team from the server")
@app_commands.describe(
    team_role="The role of the team you want to remove"
)
async def remove_team(interaction: discord.Interaction, team_role: discord.Role):
    # Verify the user has admin permissions
    if not interaction.user.guild_permissions.administrator:
        return await interaction.response.send_message(
            'You need administrator permissions to use this command.',
            ephemeral=True
        )
    
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if the team exists in our database
        cursor.execute('''
        SELECT team_name, league_type FROM league_teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        team_info = cursor.fetchone()
        if not team_info:
            # If not found in league_teams, check in teams table as fallback
            cursor.execute('''
            SELECT team_name, league FROM teams 
            WHERE role_id = ? AND guild_id = ?
            ''', (str(team_role.id), str(interaction.guild.id)))
            
            team_info = cursor.fetchone()
            if not team_info:
                return await interaction.followup.send(
                    f'No team found with role {team_role.mention}. Please verify the role is linked to a team.',
                    ephemeral=True
                )
        
        team_name, league_type = team_info
        
        # Remove from league_teams table
        cursor.execute('''
        DELETE FROM league_teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        # Remove from teams table
        cursor.execute('''
        DELETE FROM teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        # If it was a football/NFL team, also remove from nfl_teams
        if league_type.lower() in ("football", "nfl"):
            cursor.execute('''
            DELETE FROM nfl_teams 
            WHERE role_id = ? AND guild_id = ?
            ''', (str(team_role.id), str(interaction.guild.id)))
        
        conn.commit()
        
        await interaction.followup.send(
            f'Team "{team_name}" with role {team_role.mention} has been successfully removed!',
            ephemeral=True
        )
    except Exception as error:
        print(f"Error removing team: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error removing the team: {error}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()

@bot.tree.command(name="edit_team", description="Edit an existing team's information")
@app_commands.describe(
    team_role="The role of the team you want to edit",
    new_name="The new name for the team (optional)",
    new_emoji="The new emoji for the team (optional)",
    new_league_type="The new league type for the team (optional)"
)
@app_commands.choices(new_league_type=[
    app_commands.Choice(name="Generic", value="generic"),
    app_commands.Choice(name="Football", value="football"),
    app_commands.Choice(name="Basketball", value="basketball"),
    app_commands.Choice(name="NFL", value="nfl")  # Added NFL as an explicit option
])
async def edit_team(
    interaction: discord.Interaction, 
    team_role: discord.Role, 
    new_name: str = None, 
    new_emoji: str = None,
    new_league_type: app_commands.Choice[str] = None
):
    # Verify the user has admin permissions
    if not interaction.user.guild_permissions.administrator:
        return await interaction.response.send_message(
            'You need administrator permissions to use this command.',
            ephemeral=True
        )
    
    # Check if at least one parameter to edit was provided
    if not new_name and not new_emoji and not new_league_type:
        return await interaction.response.send_message(
            'You must provide at least one property to update (name, emoji, or league type).',
            ephemeral=True
        )
    
    # Defer the response immediately to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # First, check if the team exists in league_teams
        cursor.execute('''
        SELECT team_name, emoji, league_type FROM league_teams 
        WHERE role_id = ? AND guild_id = ?
        ''', (str(team_role.id), str(interaction.guild.id)))
        
        team_info = cursor.fetchone()
        if not team_info:
            # If not found in league_teams, check in teams table as fallback
            cursor.execute('''
            SELECT team_name, emoji, league FROM teams 
            WHERE role_id = ? AND guild_id = ?
            ''', (str(team_role.id), str(interaction.guild.id)))
            
            team_info = cursor.fetchone()
            if not team_info:
                return await interaction.followup.send(
                    f'No team found with role {team_role.mention}. Please verify the role is linked to a team.',
                    ephemeral=True
                )
        
        current_name, current_emoji, current_league = team_info
        
        # Determine values to update
        name_to_use = new_name if new_name else current_name
        emoji_to_use = new_emoji if new_emoji else current_emoji
        
        # Normalize league type for consistent handling
        league_to_use = new_league_type.value.lower() if new_league_type else current_league.lower()
        
        # Map NFL to football for consistency if needed
        if league_to_use == "nfl":
            stored_league_type = "football" 
        else:
            stored_league_type = league_to_use
        
        # Track if we're changing league type
        changing_league = new_league_type and new_league_type.value.lower() != current_league.lower()
        old_league = current_league.lower()
        
        # Update the league_teams table
        cursor.execute('''
        UPDATE league_teams
        SET team_name = ?, emoji = ?, league_type = ?
        WHERE role_id = ? AND guild_id = ?
        ''', (name_to_use, emoji_to_use, stored_league_type, str(team_role.id), str(interaction.guild.id)))
        
        # Update the teams table for compatibility
        cursor.execute('''
        UPDATE teams
        SET team_name = ?, emoji = ?, league = ?
        WHERE role_id = ? AND guild_id = ?
        ''', (name_to_use, emoji_to_use, stored_league_type, str(team_role.id), str(interaction.guild.id)))
        
        # Handle league type changes for NFL/football teams
        if changing_league:
            # If new league is football/NFL, add to nfl_teams
            if stored_league_type == "football":
                # Check if already exists in nfl_teams
                cursor.execute('''
                SELECT id FROM nfl_teams 
                WHERE role_id = ? AND guild_id = ?
                ''', (str(team_role.id), str(interaction.guild.id)))
                
                if cursor.fetchone():
                    # Update existing entry
                    cursor.execute('''
                    UPDATE nfl_teams
                    SET team_name = ?, emoji = ?
                    WHERE role_id = ? AND guild_id = ?
                    ''', (name_to_use, emoji_to_use, str(team_role.id), str(interaction.guild.id)))
                else:
                    # Insert new entry
                    cursor.execute('''
                    INSERT INTO nfl_teams (role_id, guild_id, team_name, emoji)
                    VALUES (?, ?, ?, ?)
                    ''', (str(team_role.id), str(interaction.guild.id), name_to_use, emoji_to_use))
            
            # If old league was football/NFL, remove from nfl_teams
            elif old_league in ("football", "nfl"):
                cursor.execute('''
                DELETE FROM nfl_teams
                WHERE role_id = ? AND guild_id = ?
                ''', (str(team_role.id), str(interaction.guild.id)))
        
        # If not changing league but changing name/emoji and league is football/NFL
        elif stored_league_type == "football" and (new_name or new_emoji):
            # Check if exists in nfl_teams
            cursor.execute('''
            SELECT id FROM nfl_teams 
            WHERE role_id = ? AND guild_id = ?
            ''', (str(team_role.id), str(interaction.guild.id)))
            
            if cursor.fetchone():
                # Update existing entry
                cursor.execute('''
                UPDATE nfl_teams
                SET team_name = ?, emoji = ?
                WHERE role_id = ? AND guild_id = ?
                ''', (name_to_use, emoji_to_use, str(team_role.id), str(interaction.guild.id)))
            else:
                # Insert new entry for compatibility with auto_setup
                cursor.execute('''
                INSERT INTO nfl_teams (role_id, guild_id, team_name, emoji)
                VALUES (?, ?, ?, ?)
                ''', (str(team_role.id), str(interaction.guild.id), name_to_use, emoji_to_use))
        
        conn.commit()
        
        # Build a message about what was updated
        updates = []
        if new_name:
            updates.append(f'name from "{current_name}" to "{new_name}"')
        if new_emoji:
            updates.append(f'emoji from {current_emoji} to {new_emoji}')
        if new_league_type:
            # Display NFL for football league type if that's what was selected
            display_league = "NFL" if new_league_type.value.lower() == "nfl" else new_league_type.value.capitalize()
            old_display_league = "NFL" if old_league == "nfl" else current_league.capitalize()
            updates.append(f'league type from "{old_display_league}" to "{display_league}"')
        
        update_message = ", ".join(updates)
        
        await interaction.followup.send(
            f'Team {name_to_use} {emoji_to_use} has been updated! Changed {update_message}.',
            ephemeral=True
        )
    except Exception as error:
        print(f"Error editing team: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error editing the team: {error}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()

# First, add this class definition somewhere before your commands
class TeamListPaginator(discord.ui.View):
    def __init__(self, embeds):
        super().__init__(timeout=300)  # 5-minute timeout
        self.embeds = embeds
        self.current_page = 0
        self.total_pages = len(embeds)
        
    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.gray)
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer()
    
    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.gray)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer()

# Make sure you have these imports at the top of your file
import discord
from discord import app_commands
from discord.ext import commands
import sqlite3
import traceback
from datetime import datetime

# Make sure your bot is initialized with member intents
# Example:
# intents = discord.Intents.default()
# intents.members = True
# bot = commands.Bot(command_prefix='!', intents=intents)

# TeamListPaginator class definition (if not defined elsewhere)
class TeamListPaginator(discord.ui.View):
    def __init__(self, embeds):
        super().__init__(timeout=180)
        self.embeds = embeds
        self.current_page = 0
        
    @discord.ui.button(label="Previous", style=discord.ButtonStyle.secondary)
    async def previous_button(self, interaction, button):
        if self.current_page > 0:
            self.current_page -= 1
            await interaction.response.edit_message(embed=self.embeds[self.current_page])
        else:
            await interaction.response.defer()
    
    @discord.ui.button(label="Next", style=discord.ButtonStyle.primary)
    async def next_button(self, interaction, button):
        if self.current_page < len(self.embeds) - 1:
            self.current_page += 1
            await interaction.response.edit_message(embed=self.embeds[self.current_page])
        else:
            await interaction.response.defer()

@bot.tree.command(name="list_teams", description="List all teams registered in the server")
@app_commands.describe(
    league_type="Filter teams by league type (optional)",
    public="Make the list visible to everyone (default: only you can see it)",
    sort_by="How to sort the teams (default: alphabetically)"
)
@app_commands.choices(league_type=[
    app_commands.Choice(name="All", value="all"),
    app_commands.Choice(name="Generic", value="generic"),
    app_commands.Choice(name="Football", value="football"),
    app_commands.Choice(name="Basketball", value="basketball"),
    app_commands.Choice(name="NFL", value="nfl")
])
@app_commands.choices(sort_by=[
    app_commands.Choice(name="Alphabetical", value="alpha"),
    app_commands.Choice(name="Member Count (High to Low)", value="members_desc"),
    app_commands.Choice(name="Member Count (Low to High)", value="members_asc")
])
async def list_teams(
    interaction: discord.Interaction, 
    league_type: app_commands.Choice[str] = None,
    public: bool = False,
    sort_by: app_commands.Choice[str] = None
):
    # Defer the response to prevent timeout
    await interaction.response.defer(ephemeral=not public)
    
    # Ensure the bot has loaded guild members (addressing the member count issue)
    if not interaction.guild.chunked:
        await interaction.guild.chunk(cache=True)
    
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Build the query based on league_type filter
        query = '''
        SELECT role_id, team_name, emoji, league_type FROM league_teams 
        WHERE guild_id = ?
        '''
        params = [str(interaction.guild.id)]
        
        if league_type and league_type.value != "all":
            # Handle NFL and football as equivalent for filtering
            if league_type.value.lower() == "nfl":
                query += " AND (LOWER(league_type) = 'football' OR LOWER(league_type) = 'nfl')"
            else:
                query += " AND LOWER(league_type) = ?"
                params.append(league_type.value.lower())
        
        # Add default sorting (alphabetical)
        query += " ORDER BY team_name ASC"
        
        cursor.execute(query, params)
        teams = cursor.fetchall()
        
        # If no teams found in league_teams, try the legacy teams table
        if not teams:
            # Adjust query for legacy table
            legacy_query = '''
            SELECT role_id, team_name, emoji, league FROM teams 
            WHERE guild_id = ?
            '''
            legacy_params = [str(interaction.guild.id)]
            
            if league_type and league_type.value != "all":
                if league_type.value.lower() == "nfl":
                    legacy_query += " AND (LOWER(league) = 'football' OR LOWER(league) = 'nfl')"
                else:
                    legacy_query += " AND LOWER(league) = ?"
                    legacy_params.append(league_type.value.lower())
            
            legacy_query += " ORDER BY team_name ASC"
            
            cursor.execute(legacy_query, legacy_params)
            teams = cursor.fetchall()
        
        if not teams:
            filter_text = ""
            if league_type and league_type.value != "all":
                filter_text = f" with league type '{league_type.name}'"
            return await interaction.followup.send(
                f'No teams found{filter_text}. Use /add_team or /auto_setup to add teams first.',
                ephemeral=not public
            )
        
        # Process teams and count members
        team_data = []
        for role_id, team_name, emoji, team_league in teams:
            try:
                # Ensure role_id is properly converted to integer
                role = interaction.guild.get_role(int(role_id))
                if role:
                    # Log for debugging
                    print(f"Found role {role.name} (ID: {role.id}) with {len(role.members)} members")
                    
                    member_count = len(role.members)
                    # Display NFL for football league type for consistency with auto_setup
                    display_league = "NFL" if team_league.lower() in ("football", "nfl") else team_league.capitalize()
                    team_data.append({
                        "role": role,
                        "name": team_name,
                        "emoji": emoji,
                        "league": display_league,
                        "member_count": member_count
                    })
                else:
                    print(f"Warning: Role with ID {role_id} not found for team {team_name}")
            except Exception as e:
                print(f"Error processing team {team_name}: {e}")
                traceback.print_exc()
                continue
        
        # Sort the teams based on sort_by parameter
        if sort_by:
            if sort_by.value == "members_desc":
                team_data.sort(key=lambda x: x["member_count"], reverse=True)
            elif sort_by.value == "members_asc":
                team_data.sort(key=lambda x: x["member_count"])
            # Default "alpha" is already applied in the SQL query
        
        # Create embeds with pagination (20 teams per page)
        embeds = []
        teams_per_page = 20
        total_teams = len(team_data)
        total_pages = (total_teams + teams_per_page - 1) // teams_per_page  # Ceiling division
        
        for page in range(total_pages):
            start_idx = page * teams_per_page
            end_idx = min(start_idx + teams_per_page, total_teams)
            page_teams = team_data[start_idx:end_idx]
            
            # Determine title based on filter
            if league_type and league_type.value != "all":
                title = f"{league_type.name} Teams"
            else:
                title = "All Teams"
            
            embed = discord.Embed(
                title=f"{title} ({total_teams})",
                description=f"Page {page+1}/{total_pages}",
                color=discord.Color.blue(),
                timestamp=datetime.now()
            )
            
            for team in page_teams:
                # Using a sensible member count display
                member_display = f"{team['member_count']} members"
                
                embed.add_field(
                    name=f"{team['emoji']} {team['name']} ({team['league']})",
                    value=f"{team['role'].mention} • {member_display}",
                    inline=False
                )
            
            embed.set_footer(text=f"Sorted by: {sort_by.name if sort_by else 'Alphabetical'}")
            embeds.append(embed)
        
        # Send with pagination if there are multiple pages
        if len(embeds) > 1:
            paginator = TeamListPaginator(embeds)
            await interaction.followup.send(embed=embeds[0], view=paginator, ephemeral=not public)
        else:
            await interaction.followup.send(embed=embeds[0], ephemeral=not public)
    
    except Exception as error:
        print(f"Error listing teams: {error}")
        traceback.print_exc()
        await interaction.followup.send(
            f'There was an error listing the teams: {error}',
            ephemeral=True
        )
    finally:
        if conn:
            conn.close()

bot.run(DISCORD_TOKEN)