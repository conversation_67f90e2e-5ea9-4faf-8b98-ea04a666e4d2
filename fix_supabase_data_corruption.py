#!/usr/bin/env python3
"""
Fix Supabase Data Corruption Script

This script fixes corrupted data in Supabase where:
1. Team names ended up in role_id fields instead of numeric Discord role IDs
2. Slot IDs ended up in role ID fields in slot_configs
3. Invalid data that should be cleaned up

This should be run to fix the corrupted Supabase data.
"""

import sqlite3
import os
import sys

def fix_supabase_data_corruption():
    """
    Fix the corrupted Supabase data by cleaning up invalid entries.
    
    Issues to fix:
    1. role_id contains team names instead of numeric Discord role IDs
    2. franchise_owner_role contains slot IDs instead of role IDs
    3. Other invalid data entries
    """
    
    # Check if we're using Supabase
    try:
        from supabase import create_client
        
        # These should be your actual Supabase credentials
        # Replace with your actual values or load from environment
        SUPABASE_URL = "https://ueheoabegwccfkholyku.supabase.co"  # Your project URL
        SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlaGVvYWJlZ3djY2ZraG9seWt1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDM0MDEsImV4cCI6MjA2NzUxOTQwMX0.puxraTE7UGm9wkfqQvTxIp7NAINODFhMOXmgFpRPBKo"   # Your actual key
        
        print("🔗 Connecting to Supabase with provided credentials...")
        print(f"📡 Supabase URL: {SUPABASE_URL}")
        print(f"🔑 Using provided authentication key")
        
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
    except ImportError:
        print("❌ Supabase package not available. Install with: pip install supabase")
        return
    except Exception as e:
        print(f"❌ Error connecting to Supabase: {e}")
        return
    
    print("🔧 Starting Supabase data corruption fix...")
    
    try:
        # 1. Fix league_teams table - remove entries with non-numeric role_id
        print("🏈 Cleaning league_teams table...")
        
        # Get all teams from Supabase
        teams_response = supabase.table('league_teams').select('*').execute()
        
        invalid_teams = []
        valid_teams = []
        
        for team in teams_response.data:
            role_id = team.get('role_id')
            
            # Check if role_id is numeric (valid Discord snowflake)
            if role_id and str(role_id).isdigit() and len(str(role_id)) >= 17:
                valid_teams.append(team)
                print(f"  ✅ Valid team: {team.get('team_name', 'Unknown')} (Role ID: {role_id})")
            else:
                invalid_teams.append(team)
                print(f"  ❌ Invalid team: {team.get('team_name', 'Unknown')} (Invalid Role ID: {role_id})")
        
        # Delete invalid teams
        if invalid_teams:
            print(f"🗑️  Deleting {len(invalid_teams)} invalid team entries...")
            for team in invalid_teams:
                try:
                    # Delete by using the team's unique identifiers
                    delete_response = supabase.table('league_teams').delete().eq('guild_id', team['guild_id']).eq('team_name', team['team_name']).execute()
                    print(f"  🗑️  Deleted: {team.get('team_name', 'Unknown')}")
                except Exception as e:
                    print(f"  ❌ Error deleting team {team.get('team_name', 'Unknown')}: {e}")
        
        # 2. Fix slot_configs table - remove entries with invalid role IDs
        print("⚙️ Cleaning slot_configs table...")
        
        # Get all slot configs from Supabase
        configs_response = supabase.table('slot_configs').select('*').execute()
        
        for config in configs_response.data:
            slot_id = config['slot_id']
            updates = {}
            
            # Check each role field
            role_fields = ['franchise_owner_role', 'general_manager_role', 'head_coach_role', 'assistant_coach_role', 'free_agent_role']
            
            for field in role_fields:
                role_id = config.get(field)
                if role_id:
                    # If it's not numeric or too short to be a Discord snowflake, set to NULL
                    if not str(role_id).isdigit() or len(str(role_id)) < 17:
                        updates[field] = None
                        print(f"  🔧 Clearing invalid {field} for slot {slot_id}: {role_id}")
            
            # Apply updates if any
            if updates:
                try:
                    update_response = supabase.table('slot_configs').update(updates).eq('slot_id', slot_id).execute()
                    print(f"  ✅ Updated slot config: {slot_id}")
                except Exception as e:
                    print(f"  ❌ Error updating slot config {slot_id}: {e}")
        
        # 3. Clean up any other tables with similar issues
        print("📋 Cleaning guild_settings table...")
        
        # Get all guild settings
        settings_response = supabase.table('guild_settings').select('*').execute()
        
        for setting in settings_response.data:
            guild_id = setting['guild_id']
            updates = {}
            
            # Check role fields in guild settings
            role_fields = ['admin_role', 'application_blacklist_role']
            
            for field in role_fields:
                role_id = setting.get(field)
                if role_id:
                    # If it's not numeric or too short to be a Discord snowflake, set to NULL
                    if not str(role_id).isdigit() or len(str(role_id)) < 17:
                        updates[field] = None
                        print(f"  🔧 Clearing invalid {field} for guild {guild_id}: {role_id}")
            
            # Apply updates if any
            if updates:
                try:
                    update_response = supabase.table('guild_settings').update(updates).eq('guild_id', guild_id).execute()
                    print(f"  ✅ Updated guild settings: {guild_id}")
                except Exception as e:
                    print(f"  ❌ Error updating guild settings {guild_id}: {e}")
        
        print("✅ Supabase data corruption fix completed!")
        print("\n📊 Summary:")
        print(f"  • Invalid teams removed: {len(invalid_teams)}")
        print(f"  • Valid teams preserved: {len(valid_teams)}")
        print(f"  • Slot configs cleaned: ✅")
        print(f"  • Guild settings cleaned: ✅")
        
        print("\n⚠️  Next steps:")
        print("1. Verify the cleanup worked by checking your Supabase dashboard")
        print("2. Re-run your bot's /autosetup command to recreate valid teams")
        print("3. Use /sync_data to pull the cleaned data back to your local bot")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔧 Supabase Data Corruption Fix Tool")
    print("=" * 50)
    print("This script will clean up corrupted data in your Supabase database.")
    print("Make sure to backup your data before running this!")
    print()
    
    # Ask for confirmation
    response = input("Do you want to proceed with the cleanup? (yes/no): ").lower().strip()
    if response in ['yes', 'y']:
        fix_supabase_data_corruption()
    else:
        print("❌ Cleanup cancelled.")
