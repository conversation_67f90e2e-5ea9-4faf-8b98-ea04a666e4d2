#!/usr/bin/env python3
"""
RosterFlow Discord Bot - Main Entry Point

This file starts the Discord bot by importing and running the main bot code.
All the bot functionality is in 'import sqlite3.py' (which has been fixed).

To run the bot:
1. Make sure you have all required dependencies installed:
   pip install discord.py sqlite3
   
2. Run this file:
   python main_bot.py

The bot will automatically:
- Connect to the database (SQLite by default)
- Create necessary tables if they don't exist
- Sync slash commands with Discord
- Start listening for commands and events
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("🚀 Starting RosterFlow Discord Bot...")
        print("📁 Loading bot from 'import sqlite3.py'...")
        
        # Import the bot - this will initialize everything
        # The file 'import sqlite3.py' contains all the bot logic, commands, and database setup
        import importlib.util
        spec = importlib.util.spec_from_file_location("bot_module", "import sqlite3.py")
        bot_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(bot_module)
        
        print("✅ Bot module loaded successfully!")
        print("🤖 Bot should now be running...")
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Make sure 'import sqlite3.py' exists in the same directory")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting bot: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
