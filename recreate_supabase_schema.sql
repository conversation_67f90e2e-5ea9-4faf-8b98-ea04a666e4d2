-- ============================================
-- COMPLETE SUPABASE TABLE RECREATION SCRIPT
-- ============================================
-- Run this in Supabase SQL Editor after deleting old tables
-- This ensures perfect schema alignment with the bot's expectations

-- Drop existing tables (if they exist) - Order matters for foreign key constraints
DROP TABLE IF EXISTS slot_demand_config CASCADE;
DROP TABLE IF EXISTS slot_command_settings CASCADE;
DROP TABLE IF EXISTS slot_configs CASCADE;
DROP TABLE IF EXISTS live_management_lists CASCADE;
DROP TABLE IF EXISTS disband_transactions CASCADE;
DROP TABLE IF EXISTS league_teams CASCADE;
DROP TABLE IF EXISTS slots CASCADE;
DROP TABLE IF EXISTS guild_settings CASCADE;

-- Drop any existing functions (this will also drop dependent triggers)
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- ============================================
-- 1. GUILD SETTINGS TABLE
-- ============================================
CREATE TABLE guild_settings (
    guild_id BIGINT PRIMARY KEY,
    admin_role BIGINT,
    log_channel BIGINT,
    application_channel BIGINT,
    suspended_role BIGINT,
    suspended_channel BIGINT,
    application_blacklist_role BIGINT,
    transaction_log_channel BIGINT,  -- Added for transaction logging
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS (Row Level Security)
ALTER TABLE guild_settings ENABLE ROW LEVEL SECURITY;

-- Create index for performance
CREATE INDEX idx_guild_settings_guild_id ON guild_settings(guild_id);

-- ============================================
-- 2. SLOTS TABLE
-- ============================================
CREATE TABLE slots (
    slot_id TEXT PRIMARY KEY,
    guild_id BIGINT NOT NULL,
    slot_name TEXT NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_slots_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE
);

-- Enable RLS
ALTER TABLE slots ENABLE ROW LEVEL SECURITY;

-- Create indexes
CREATE INDEX idx_slots_guild_id ON slots(guild_id);
CREATE INDEX idx_slots_slot_name ON slots(slot_name);
CREATE INDEX idx_slots_is_default ON slots(is_default);

-- ============================================
-- 3. LEAGUE TEAMS TABLE
-- ============================================
CREATE TABLE league_teams (
    id SERIAL PRIMARY KEY,
    guild_id BIGINT NOT NULL,
    slot_id TEXT NOT NULL,
    role_id BIGINT NOT NULL UNIQUE,  -- Discord role ID (must be unique)
    team_name TEXT NOT NULL,
    emoji TEXT DEFAULT '🏈',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_league_teams_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE,
    CONSTRAINT fk_league_teams_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate teams in same slot
    CONSTRAINT unique_team_per_slot UNIQUE (guild_id, slot_id, team_name)
);

-- Enable RLS
ALTER TABLE league_teams ENABLE ROW LEVEL SECURITY;

-- Create indexes
CREATE INDEX idx_league_teams_guild_id ON league_teams(guild_id);
CREATE INDEX idx_league_teams_slot_id ON league_teams(slot_id);
CREATE INDEX idx_league_teams_role_id ON league_teams(role_id);
CREATE INDEX idx_league_teams_team_name ON league_teams(team_name);

-- ============================================
-- 4. SLOT CONFIGS TABLE
-- ============================================
CREATE TABLE slot_configs (
    slot_id TEXT PRIMARY KEY,
    franchise_owner_role BIGINT,
    general_manager_role BIGINT,
    head_coach_role BIGINT,
    assistant_coach_role BIGINT,
    free_agent_role BIGINT,
    trade_channel BIGINT,
    transaction_channel BIGINT,
    score_channel BIGINT,
    weekly_games_channel BIGINT,  -- Added for weekly games
    streamer_role BIGINT,
    referee_role BIGINT,
    gametime_channel BIGINT,
    draft_channel BIGINT,
    pickup_host_role BIGINT,
    pickup_channel BIGINT,
    admin_role BIGINT,  -- Slot-specific admin role
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_slot_configs_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Enable RLS
ALTER TABLE slot_configs ENABLE ROW LEVEL SECURITY;

-- Create index
CREATE INDEX idx_slot_configs_slot_id ON slot_configs(slot_id);

-- ============================================
-- 5. SLOT COMMAND SETTINGS TABLE
-- ============================================
CREATE TABLE slot_command_settings (
    slot_id TEXT PRIMARY KEY,
    sign_enabled TEXT DEFAULT 'on' CHECK (sign_enabled IN ('on', 'off')),
    release_enabled TEXT DEFAULT 'on' CHECK (release_enabled IN ('on', 'off')),
    offer_enabled TEXT DEFAULT 'on' CHECK (offer_enabled IN ('on', 'off')),
    roster_cap INTEGER DEFAULT 25 CHECK (roster_cap > 0),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_slot_command_settings_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Enable RLS
ALTER TABLE slot_command_settings ENABLE ROW LEVEL SECURITY;

-- Create index
CREATE INDEX idx_slot_command_settings_slot_id ON slot_command_settings(slot_id);

-- ============================================
-- 6. SLOT DEMAND CONFIG TABLE
-- ============================================
CREATE TABLE slot_demand_config (
    slot_id TEXT PRIMARY KEY,
    demand_enabled TEXT DEFAULT 'off' CHECK (demand_enabled IN ('on', 'off')),
    qb_role BIGINT,
    rb_role BIGINT,
    wr_role BIGINT,
    te_role BIGINT,
    ol_role BIGINT,
    dl_role BIGINT,
    lb_role BIGINT,
    db_role BIGINT,
    k_role BIGINT,
    p_role BIGINT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_slot_demand_config_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Enable RLS
ALTER TABLE slot_demand_config ENABLE ROW LEVEL SECURITY;

-- Create index
CREATE INDEX idx_slot_demand_config_slot_id ON slot_demand_config(slot_id);

-- ============================================
-- 7. LIVE MANAGEMENT LISTS TABLE
-- ============================================
CREATE TABLE live_management_lists (
    id SERIAL PRIMARY KEY,
    guild_id BIGINT NOT NULL,
    channel_id BIGINT NOT NULL,
    slot_id TEXT NOT NULL,
    display_type TEXT DEFAULT 'both' CHECK (display_type IN ('owners', 'gms', 'both')),
    auto_update TEXT DEFAULT 'yes' CHECK (auto_update IN ('yes', 'no')),
    include_empty TEXT DEFAULT 'yes' CHECK (include_empty IN ('yes', 'no')),
    league_style TEXT DEFAULT 'nfl' CHECK (league_style IN ('nfl', 'college')),
    secondary_label TEXT DEFAULT 'AD',
    message_ids TEXT,  -- JSON array of message IDs
    last_updated TIMESTAMPTZ,
    active TEXT DEFAULT 'yes' CHECK (active IN ('yes', 'no')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_live_mgmt_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE,
    CONSTRAINT fk_live_mgmt_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate lists
    CONSTRAINT unique_live_mgmt_per_channel_slot UNIQUE (guild_id, channel_id, slot_id)
);

-- Enable RLS
ALTER TABLE live_management_lists ENABLE ROW LEVEL SECURITY;

-- Create indexes
CREATE INDEX idx_live_mgmt_guild_id ON live_management_lists(guild_id);
CREATE INDEX idx_live_mgmt_channel_id ON live_management_lists(channel_id);
CREATE INDEX idx_live_mgmt_slot_id ON live_management_lists(slot_id);
CREATE INDEX idx_live_mgmt_active ON live_management_lists(active);

-- ============================================
-- 8. DISBAND TRANSACTIONS TABLE
-- ============================================
CREATE TABLE disband_transactions (
    disband_id TEXT PRIMARY KEY,
    guild_id BIGINT NOT NULL,
    team_role_id BIGINT NOT NULL,
    team_name TEXT NOT NULL,
    user_id BIGINT NOT NULL,  -- User who initiated disband
    free_agent_role_id BIGINT,
    player_data TEXT NOT NULL,  -- JSON array of player data
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_disband_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE
);

-- Enable RLS
ALTER TABLE disband_transactions ENABLE ROW LEVEL SECURITY;

-- Create indexes
CREATE INDEX idx_disband_guild_id ON disband_transactions(guild_id);
CREATE INDEX idx_disband_team_role ON disband_transactions(team_role_id);
CREATE INDEX idx_disband_user_id ON disband_transactions(user_id);
CREATE INDEX idx_disband_created_at ON disband_transactions(created_at);

-- ============================================
-- 9. UPDATE TRIGGERS FOR TIMESTAMP MANAGEMENT
-- ============================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers to relevant tables
CREATE TRIGGER update_guild_settings_updated_at BEFORE UPDATE ON guild_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_slots_updated_at BEFORE UPDATE ON slots FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_league_teams_updated_at BEFORE UPDATE ON league_teams FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_slot_configs_updated_at BEFORE UPDATE ON slot_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_slot_command_settings_updated_at BEFORE UPDATE ON slot_command_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_slot_demand_config_updated_at BEFORE UPDATE ON slot_demand_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_live_management_lists_updated_at BEFORE UPDATE ON live_management_lists FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- 10. ROW LEVEL SECURITY POLICIES (OPTIONAL)
-- ============================================
-- Note: These are basic policies. Adjust based on your security requirements.

-- Guild settings: Only allow access to data for the specific guild
CREATE POLICY guild_settings_policy ON guild_settings FOR ALL USING (true);

-- Slots: Only allow access to slots for the specific guild
CREATE POLICY slots_policy ON slots FOR ALL USING (true);

-- League teams: Only allow access to teams for the specific guild
CREATE POLICY league_teams_policy ON league_teams FOR ALL USING (true);

-- Slot configs: Only allow access to configs for existing slots
CREATE POLICY slot_configs_policy ON slot_configs FOR ALL USING (true);

-- Slot command settings: Only allow access to settings for existing slots
CREATE POLICY slot_command_settings_policy ON slot_command_settings FOR ALL USING (true);

-- Slot demand config: Only allow access to demand config for existing slots
CREATE POLICY slot_demand_config_policy ON slot_demand_config FOR ALL USING (true);

-- Live management lists: Only allow access to lists for the specific guild
CREATE POLICY live_management_lists_policy ON live_management_lists FOR ALL USING (true);

-- Disband transactions: Only allow access to transactions for the specific guild
CREATE POLICY disband_transactions_policy ON disband_transactions FOR ALL USING (true);

-- ============================================
-- 11. VALIDATION CONSTRAINTS
-- ============================================

-- Add constraint to ensure role_id in league_teams is a valid Discord snowflake (17+ digits)
ALTER TABLE league_teams ADD CONSTRAINT valid_discord_role_id CHECK (role_id >= 10000000000000000);

-- Add constraint to ensure guild_id is a valid Discord snowflake
ALTER TABLE guild_settings ADD CONSTRAINT valid_discord_guild_id CHECK (guild_id >= 10000000000000000);

-- ============================================
-- 12. COMMENTS FOR DOCUMENTATION
-- ============================================

COMMENT ON TABLE guild_settings IS 'Stores guild-specific bot configuration settings';
COMMENT ON TABLE slots IS 'Defines different league slots within a guild';
COMMENT ON TABLE league_teams IS 'Stores team information for each slot';
COMMENT ON TABLE slot_configs IS 'Configuration settings specific to each slot';
COMMENT ON TABLE slot_command_settings IS 'Command enablement settings for each slot';
COMMENT ON TABLE slot_demand_config IS 'Position-based demand system configuration';
COMMENT ON TABLE live_management_lists IS 'Configuration for live updating management lists';
COMMENT ON TABLE disband_transactions IS 'Audit trail for team disbandment operations';

-- ============================================
-- SCRIPT COMPLETION
-- ============================================

-- Verify table creation
SELECT 
    table_schema,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'guild_settings',
    'slots', 
    'league_teams',
    'slot_configs',
    'slot_command_settings',
    'slot_demand_config',
    'live_management_lists',
    'disband_transactions'
)
ORDER BY table_name;

-- Show table columns and data types
SELECT 
    table_schema,
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name IN (
    'guild_settings',
    'slots', 
    'league_teams',
    'slot_configs',
    'slot_command_settings',
    'slot_demand_config',
    'live_management_lists',
    'disband_transactions'
)
ORDER BY table_name, ordinal_position;

-- ============================================
-- SUCCESS MESSAGE
-- ============================================
DO $$ 
BEGIN 
    RAISE NOTICE 'Database schema recreation completed successfully!';
    RAISE NOTICE 'All tables, indexes, constraints, and triggers have been created.';
    RAISE NOTICE 'The bot should now be able to sync without any schema issues.';
END $$;
