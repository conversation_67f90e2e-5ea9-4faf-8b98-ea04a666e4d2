## High-Level Documentation: League Management Discord Bot

### Overview

This project is a comprehensive Discord bot developed to manage league operations within Discord servers. It is designed primarily for sports leagues (like NFL, college football, or fictional leagues in Discord communities), but is extensible for other types of leagues. The bot is highly modular, supports multiple "slots" or league configurations per Discord server (guild), and brings Discord-based transaction management, team setup, application workflows, and contract handling into a unified bot experience.

---

### Key Features

#### 1. Multi-Slot Configuration
- **Slots** represent different league setups within the same server (e.g., different seasons, divisions, or parallel leagues).
- Each slot has independent team lists, staff roles, communication channels, demand systems, roster and money caps, and other configurations.

#### 2. Database Abstraction
- Offers seamless operation with either **SQLite** (local file) or **Supabase** (cloud Postgres) as the backend.
- Migration scripts and schema checks ensure consistency across different database backends.
- Automatic setup and migration, including foreign key relationships and cascade deletes for data integrity.

#### 3. Interactive Setup Wizards
- **Global Setup**: Guides administrators through configuring server-wide roles and channels necessary for the bot's operation.
- **Slot Setup**: Per-slot wizard configures management staff, channels, game settings, demand systems, and more, complete with UI "pages" for each configuration area.
- **Autosetup**: Detects likely roles and channels based on naming conventions for rapid initial setup.

#### 4. Permission System
- Respects Discord's permission model but also supports custom admin roles set via bot configuration.
- Sensitive commands are restricted to admins or users with the correct role.

#### 5. Granular Logging and Change Audit
- All changes to guild or slot settings can be logged to a configurable channel.
- Detailed embeds capture what was changed, by whom, old and new values, and the context (slot/guild).

#### 6. Team and Contract Management
- Detects team roles automatically and prevents duplicates across slots.
- Supports adding/removing teams to slots, showing teams, and managing contracts (money/roster caps, durations, offers).
- Offers robust views for offering contracts, signing players, releasing players, and contract expiration workflows.
- Money Cap tracking and status reporting for financial league management.

#### 7. Player and Staff Applications
- Allows users to apply for league/team/staff positions within a slot.
- Applications are reviewed via interactive Discord messages.
- Includes a global blacklist for barring users from applications.

#### 8. Demand System (Auction/Fantasy Features)
- Extensive per-slot configuration for demand-based player roles (think auction/fantasy player tracking).
- Supports demand role assignment, special channels, and granular enabling/disabling.

#### 9. Debugging and Diagnostics
- Includes commands for auditing current settings and viewing unset or default configuration items.
- Commands for listing all slots, contracts, teams, and money cap status.
- Provides helpers for advanced troubleshooting (debug, reset, etc.).

#### 10. Safeguards and Consistency
- Confirmation views for destructive actions (slot/guild deletion and resets).
- Timed views for sensitive actions to avoid stale operations.

---

### Technical Structure

#### Database Handling
- All persistent data is managed through a robust abstraction layer (`DatabaseAdapter`) supporting both SQLite and Supabase.
- Helper/proxy classes offer compatibility with both databases for higher-level bot logic.
- SQL migration scripts enforce strong typing, foreign keys, and handle schema evolution.
- Upserts and idempotent-delayed initialization patterns are used for slot/command/configuration tables.

#### Discord UI/UX
- Utilizes Discord's `discord.ui` components extensively: custom select menus, modal dialogs, button views.
- Commands are implemented as application (slash) commands, compatible with Discord's latest features.
- All edits and actions are ephemeral (where needed) and respect Discord interaction flow.

#### Robustness and Error Handling
- Extensive use of try/except for commands and UI callbacks, with friendly messages for user errors and console logging for system errors.
- Graceful fallback and recovery: for example, if a required slot is missing, the bot tries to create a default automatically.
- All destructive actions (deletions, resets) require explicit user confirmation via UI.

---

### Notable Implementation Patterns

- **Separation of Settings:** Server-wide ("global") vs slot-specific settings, each with their own configuration, logging, and UI.
- **Helper Functions:** Utilities for permission checks, logging, database access, and normalization of names.
- **Async Initialization:** Many views load their initial state asynchronously for responsiveness.
- **Field Chunking:** Commands gracefully handle Discord's embed field limits by splitting output across multiple embeds when needed.
- **Schema Evolution:** Code is annotated and structured to handle legacy-to-new migrations.

---

### Extensibility

- **New Sports/Leagues:** Can be extended for new sets of teams, contracts, or configurations.
- **Application Forms:** Modal-based applications can be customized or expanded based on league type.
- **Integration:** Google Sheets support (if enabled), and prepared for future automation.
- **Cloud-First:** Supabase/Postgres compatibility for persistent, scalable deployments.

---

### Summary

**This codebase provides a full framework for Discord-based league management**, covering everything from server onboarding and setup to day-to-day slot/team/contract operations, with a focus on interactive configuration, permission safety, and transparent logging. It is suitable for communities running complex, multiplayer, season-based leagues (especially for gaming or sports) and is robust against the challenges of role/team duplication, mass configuration changes, and multi-role environments. The code is modular, maintainable, and highly configurable to suit a wide range of Discord server needs.