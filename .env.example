# Example .env file for Discord Bot with Supabase
# Copy this file to .env and fill in your actual values

# ======================
# DISCORD CONFIGURATION
# ======================
DISCORD_BOT_TOKEN=your_discord_bot_token_here

# ======================
# DATABASE CONFIGURATION
# ======================
# Set to 'supabase' for cloud database or 'sqlite' for local database
DATABASE_TYPE=supabase

# Supabase Configuration (only needed if DATABASE_TYPE=supabase)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your_supabase_anon_or_service_role_key_here

# ======================
# OPTIONAL CONFIGURATION
# ======================
# Google Sheets Configuration (if using sheets features)
# GOOGLE_SERVICE_ACCOUNT_INFO={"type": "service_account", ...}

# ======================
# NOTES
# ======================
# 1. Never commit this file to version control
# 2. For production, use environment variables instead of this file
# 3. The bot will automatically fall back to SQLite if Supabase fails
# 4. Make sure you've run the migration script in Supabase before using it
