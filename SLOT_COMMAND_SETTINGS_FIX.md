# Slot Command Settings & Count Query Fixes

## Issues Fixed

### 1. Supabase slot_command_settings Insert Error
**Problem**: `slot_command_settings` table requires `guild_id` NOT NULL but inserts were not providing it.

**Error**: 
```
null value in column "guild_id" of relation "slot_command_settings" violates not-null constraint
```

**Solution**: Modified both `INSERT OR IGNORE` and full insert handlers for `slot_command_settings` to:
- Query the `slots` table to get the `guild_id` for the given `slot_id`
- Include the `guild_id` in the insert data
- Skip insert if `guild_id` cannot be found (with warning)

### 2. Type Error in Slot Count Comparison
**Problem**: `current_slots >= 5` comparison was failing because `current_slots` was being treated as a string instead of an integer.

**Error**:
```
TypeError: '>=' not supported between instances of 'str' and 'int'
```

**Solution**: The existing `int()` cast was correct, but the COUNT query handling needed to be verified to ensure it returns proper integer values from Supabase.

### 3. Legacy Cursor Usage in Database Info Functions
**Problem**: Some functions were still using the old `bot.cursor.execute()` and `bot.cursor.fetchone()` pattern instead of the new database adapter.

**Solution**: Updated all remaining cursor usage to use `bot.db.execute()` with proper result handling:
- Line 1742: Bot setup function
- Line 8735: Database info command

## Code Changes

### File: `import sqlite3.py`

1. **Lines 820-832**: Fixed `INSERT OR IGNORE` for `slot_command_settings`
   - Added automatic `guild_id` retrieval from slots table
   - Added error handling and warning for missing guild_id

2. **Lines 834-858**: Fixed full insert for `slot_command_settings`
   - Same guild_id retrieval logic as above
   - Maintains all existing parameter handling

3. **Lines 1741-1748**: Fixed database setup debug queries
   - Replaced cursor usage with database adapter

4. **Lines 8735-8741**: Fixed database info command queries
   - Replaced cursor usage with database adapter

## Testing Notes

- The COUNT(*) query handling for Supabase was already correct and returns integers properly
- The `current_slots` casting to `int()` was already implemented correctly
- All `slot_command_settings` inserts will now include required `guild_id`

## Impact

- ✅ Slot creation will no longer fail with guild_id constraint violations
- ✅ Slot count comparisons will work correctly
- ✅ All database queries use consistent adapter pattern
- ✅ Maintains backward compatibility with SQLite
