class AvailableTeamsPaginator(discord.ui.View):
    def __init__(self, embeds: List[discord.Embed]):
        super().__init__(timeout=300) # 5-minute timeout
        self.embeds = embeds
        self.current_page = 0
        self.total_pages = len(embeds)
        self.update_buttons()

    def update_buttons(self):
        # Change button style to primary for better visibility
        self.children[0].style = discord.ButtonStyle.primary if self.current_page > 0 else discord.ButtonStyle.gray
        self.children[0].disabled = (self.current_page == 0) # Previous button

        self.children[1].style = discord.ButtonStyle.primary if self.current_page < self.total_pages - 1 else discord.ButtonStyle.gray
        self.children[1].disabled = (self.current_page == self.total_pages - 1) # Next button

    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.gray, custom_id="available_teams_prev")
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
            self.update_buttons()
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer() # Acknowledge if already on first page
            
    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.gray, custom_id="available_teams_next")
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.update_buttons()
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer() # Acknowledge if already on last page

    async def on_timeout(self):
        if self.message:
            for item in self.children:
                item.disabled = True
            try:
                await self.message.edit(view=self)
            except discord.HTTPException:
                pass # Ignore if message already deleted

@bot.tree.command(name="available_teams", description="Check teams based on player count.")
@app_commands.describe(
    player_count="The number of players to compare against (optional).",
    comparison_type="How to compare the player count (optional, default: less than or equal to).",
    public="Make the list visible to everyone (default: only you can see it)."
)
@app_commands.choices(comparison_type=[
    app_commands.Choice(name="Equal to", value="equal"),
    app_commands.Choice(name="Less than", value="less"),
    app_commands.Choice(name="Greater than", value="greater"),
    app_commands.Choice(name="Less than or Equal to", value="less_or_equal"),
    app_commands.Choice(name="Greater than or Equal to", value="greater_or_equal")
])
async def available_teams(
    interaction: discord.Interaction,
    player_count: Optional[int] = 0,
    comparison_type: str = "less_or_equal", # Corrected default to string literal
    public: bool = False
):
    await interaction.response.defer(ephemeral=not public)

    guild = interaction.guild
    if not guild:
        await interaction.followup.send("This command can only be used in a server.", ephemeral=True)
        return

    if not guild.chunked:
        await guild.chunk(cache=True)

    league_conn = None
    try:
        league_conn = sqlite3.connect('league_config.db')
        league_cursor = league_conn.cursor()

        roster_cap = get_roster_cap_from_command_settings(guild.id, bot)
        if roster_cap is None:
            roster_cap = 25
            await interaction.followup.send("Warning: Could not fetch roster cap from settings. Defaulting to 25. Please ensure your bot's transaction_bot.db is set up.", ephemeral=True)

        all_teams_data = []
        league_cursor.execute("SELECT role_id, team_name, emoji FROM league_teams WHERE guild_id = ? ORDER BY team_name ASC", (guild.id,))
        all_teams_data = league_cursor.fetchall()

        if not all_teams_data:
            try:
                league_cursor.execute("SELECT role_id, team_name, emoji FROM teams WHERE guild_id = ? ORDER BY team_name ASC", (guild.id,))
                all_teams_data = league_cursor.fetchall()
            except sqlite3.OperationalError:
                pass

        if not all_teams_data:
            await interaction.followup.send("No teams registered in the bot's database for this server. Use `/add_team` or `/detect_teams` to add them.", ephemeral=True)
            return

        filtered_teams = []
        all_guild_members = guild.members

        for team_data_row in all_teams_data:
            team_role_id_db, team_name_db, team_emoji_db = team_data_row
            team_role_obj = guild.get_role(int(team_role_id_db))
            if not team_role_obj:
                continue

            current_team_member_count = 0
            for member in all_guild_members:
                if team_role_obj in member.roles:
                    current_team_member_count += 1

            match_criteria = False

            if comparison_type == "equal":
                match_criteria = (current_team_member_count == player_count)
            elif comparison_type == "less":
                match_criteria = (current_team_member_count < player_count)
            elif comparison_type == "greater":
                match_criteria = (current_team_member_count > player_count)
            elif comparison_type == "less_or_equal":
                match_criteria = (current_team_member_count <= player_count)
            elif comparison_type == "greater_or_equal":
                match_criteria = (current_team_member_count >= player_count)
            
            if match_criteria:
                filtered_teams.append({
                    "name": team_name_db,
                    "emoji": team_emoji_db,
                    "member_count": current_team_member_count
                })

        if not filtered_teams:
            comparison_name_map = {
                "equal": "Equal to",
                "less": "Less than",
                "greater": "Greater than",
                "less_or_equal": "Less than or Equal to",
                "greater_or_equal": "Greater than or Equal to"
            }
            display_comparison_name = comparison_name_map.get(comparison_type, comparison_type)
            
            status_message = f"No teams found matching the criteria (player count {display_comparison_name} {player_count}) that are registered in the bot's database."
            await interaction.followup.send(status_message, ephemeral=True)
            return

        filtered_teams.sort(key=lambda x: (x['member_count'], x['name'].lower()))

        teams_per_page = 15
        total_teams = len(filtered_teams)
        total_pages = math.ceil(total_teams / teams_per_page) if total_teams > 0 else 1

        embeds = []
        for i in range(total_pages):
            start_index = i * teams_per_page
            end_index = min((i + 1) * teams_per_page, total_teams)
            current_page_teams = filtered_teams[start_index:end_index]

            comparison_name_map = {
                "equal": "Equal to",
                "less": "Less than",
                "greater": "Greater than",
                "less_or_equal": "Less than or Equal to",
                "greater_or_equal": "Greater than or Equal to"
            }
            display_comparison_name = comparison_name_map.get(comparison_type, comparison_type)

            embed_title_text = f"Teams with Player Count {display_comparison_name} {player_count}"
            if player_count == 0 and comparison_type == "equal":
                embed_title_text = "Teams with 0 Players"

            embed = discord.Embed(
                title=f"📋 {embed_title_text}",
                description=f"Page {i+1}/{total_pages} ({total_teams} total teams listed)",
                color=discord.Color.green(),
                timestamp=DateTime.now(TimeZone.utc)
            )
            if guild.icon:
                embed.set_author(name=guild.name, icon_url=guild.icon.url)
            
            for team in current_page_teams:
                team_display_name = f"{team['emoji']}{team['name']}" if team['emoji'] else f"**{team['name']}**"
                embed.add_field(
                    name=f"Team: {team_display_name}",
                    value=f"Players: {team['member_count']}/{roster_cap}",
                    inline=False
                )
            embeds.append(embed)

        if len(embeds) > 1:
            paginator = AvailableTeamsPaginator(embeds)
            message = await interaction.followup.send(embed=embeds[0], view=paginator, ephemeral=not public)
            paginator.message = message
        else:
            await interaction.followup.send(embed=embeds[0], ephemeral=not public)

    except Exception as e:
        print(f"Error in available_teams command: {e}")
        traceback.print_exc()
        await interaction.followup.send(f"An unexpected error occurred: {str(e)}", ephemeral=True)
    finally:
        if league_conn:
            league_conn.close()

class CoachCheckPaginator(discord.ui.View):
    def __init__(self, embeds: List[discord.Embed]):
        super().__init__(timeout=300) # 5-minute timeout
        self.embeds = embeds
        self.current_page = 0
        self.total_pages = len(embeds)
        self.update_buttons()

    def update_buttons(self):
        # Change button style to primary for better visibility
        self.children[0].style = discord.ButtonStyle.primary if self.current_page > 0 else discord.ButtonStyle.gray
        self.children[0].disabled = (self.current_page == 0) # Previous button

        self.children[1].style = discord.ButtonStyle.primary if self.current_page < self.total_pages - 1 else discord.ButtonStyle.gray
        self.children[1].disabled = (self.current_page == self.total_pages - 1) # Next button

    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.gray, custom_id="coach_check_prev")
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
            self.update_buttons()
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer() # Acknowledge if already on first page
            
    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.gray, custom_id="coach_check_next")
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.update_buttons()
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
        else:
            await interaction.response.defer() # Acknowledge if already on last page

    async def on_timeout(self):
        if self.message:
            for item in self.children:
                item.disabled = True
            try:
                await self.message.edit(view=self)
            except discord.HTTPException:
                pass # Ignore if message already deleted

@bot.tree.command(name="coach_check", description="Check what teams have or don't have franchise owners.")
@app_commands.describe(
    view_type="What type of teams to display.",
    public="Make the list visible to everyone (default: only you can see it)."
)
@app_commands.choices(view_type=[
    app_commands.Choice(name="Teams with Franchise Owners", value="has_owner"),
    app_commands.Choice(name="Teams without Franchise Owners", value="no_owner")
])
async def coach_check(
    interaction: discord.Interaction,
    view_type: str = "no_owner", # Corrected default to string literal
    public: bool = False
):
    await interaction.response.defer(ephemeral=not public)

    guild = interaction.guild
    if not guild:
        await interaction.followup.send("This command can only be used in a server.", ephemeral=True)
        return

    if not guild.chunked:
        await guild.chunk(cache=True)

    league_conn = None
    trans_conn = None
    try:
        league_conn = sqlite3.connect('league_config.db')
        league_cursor = league_conn.cursor()
        
        trans_conn = sqlite3.connect('transaction_bot.db')
        trans_cursor = trans_conn.cursor()

        trans_cursor.execute("SELECT franchise_owner_role FROM server_config WHERE server_id = ?", (guild.id,))
        fo_role_id_data = trans_cursor.fetchone()
        
        if not fo_role_id_data or fo_role_id_data[0] is None:
            await interaction.followup.send("The Franchise Owner role is not configured for this server. Please set it up using `/setup`.", ephemeral=True)
            return
        
        franchise_owner_role = guild.get_role(fo_role_id_data[0])
        if not franchise_owner_role:
            await interaction.followup.send(f"The configured Franchise Owner role (ID: {fo_role_id_data[0]}) was not found in the server. Please reconfigure.", ephemeral=True)
            return

        all_teams_data = []
        league_cursor.execute("SELECT role_id, team_name, emoji FROM league_teams WHERE guild_id = ? ORDER BY team_name ASC", (guild.id,))
        all_teams_data = league_cursor.fetchall()

        if not all_teams_data:
            try:
                league_cursor.execute("SELECT role_id, team_name, emoji FROM teams WHERE guild_id = ? ORDER BY team_name ASC", (guild.id,))
                all_teams_data = league_cursor.fetchall()
            except sqlite3.OperationalError:
                pass

        if not all_teams_data:
            await interaction.followup.send("No teams registered in the bot's database for this server. Use `/add_team` or `/detect_teams` to add them.", ephemeral=True)
            return

        filtered_teams = []
        all_guild_members = guild.members

        for team_data_row in all_teams_data:
            team_role_id_db, team_name_db, team_emoji_db = team_data_row
            team_role_obj = guild.get_role(int(team_role_id_db))
            if not team_role_obj:
                continue

            team_has_owner = False
            owner_member = None
            
            for member in all_guild_members:
                if team_role_obj in member.roles and franchise_owner_role in member.roles:
                    owner_member = member
                    team_has_owner = True
                    break

            if (view_type == "has_owner" and team_has_owner) or \
               (view_type == "no_owner" and not team_has_owner):
                filtered_teams.append({
                    "name": team_name_db,
                    "emoji": team_emoji_db,
                    "owner": owner_member
                })

        if not filtered_teams:
            status_message = ""
            if view_type == "has_owner":
                status_message = "No teams found with Franchise Owners based on your selection. Please ensure Franchise Owners have both their team role and the configured Franchise Owner role."
            else:
                if all_teams_data:
                    status_message = "All registered teams currently have a Franchise Owner."
                else:
                    status_message = "No teams are registered in the bot's database to check. Use `/add_team` or `/detect_teams` to add them."
            await interaction.followup.send(status_message, ephemeral=True)
            return

        filtered_teams.sort(key=lambda x: x['name'].lower())

        teams_per_page = 15
        total_teams = len(filtered_teams)
        total_pages = math.ceil(total_teams / teams_per_page) if total_teams > 0 else 1

        embeds = []
        for i in range(total_pages):
            start_index = i * teams_per_page
            end_index = min((i + 1) * teams_per_page, total_teams)
            current_page_teams = filtered_teams[start_index:end_index]

            embed_title_text = "Teams with Franchise Owners" if view_type == "has_owner" else "Teams without Franchise Owners"
            
            embed_color = discord.Color.brand_green() if view_type == "has_owner" else discord.Color.red()

            embed = discord.Embed(
                title=f"📊 {embed_title_text}",
                description=f"Page {i+1}/{total_pages} ({total_teams} total teams listed)",
                color=embed_color,
                timestamp=DateTime.now(TimeZone.utc)
            )
            if guild.icon:
                embed.set_author(name=guild.name, icon_url=guild.icon.url)
            
            for team in current_page_teams:
                display_string = ""
                owner_mention_value = ""
                
                if view_type == "has_owner" and team['owner']:
                    owner_mention_value = team['owner'].mention
                
                if team['emoji']:
                    display_string = f"{team['emoji']} {team['name']}"
                else:
                    display_string = f"**{team['name']}**"
                
                embed.add_field(
                    name=display_string, 
                    value=owner_mention_value if owner_mention_value else "\u200b",
                    inline=False
                )
            embeds.append(embed)

        if len(embeds) > 1:
            paginator = CoachCheckPaginator(embeds)
            message = await interaction.followup.send(embed=embeds[0], view=paginator, ephemeral=not public)
            paginator.message = message
        else:
            await interaction.followup.send(embed=embeds[0], ephemeral=not public)

    except Exception as e:
        print(f"Error in coach_check command: {e}")
        traceback.print_exc()
        await interaction.followup.send(f"An unexpected error occurred: {str(e)}", ephemeral=True)
    finally:
        if league_conn:
            league_conn.close()
        if trans_conn:
            trans_conn.close()

import discord
from discord import app_commands
import sqlite3
import traceback
from typing import Optional
from datetime import datetime as DateTime, timezone as TimeZone # For embed timestamp

# Re-using common helper functions (assuming they are defined elsewhere in the bot's script)
# If not, they would need to be included or their logic inlined.

def get_emoji_url(emoji_str: Optional[str]) -> Optional[str]:
    """Extract custom emoji ID and convert to URL if possible."""
    if not emoji_str:
        return None
    custom_emoji_match = re.match(r'<a?:[\w]+:(\d+)>', emoji_str)
    if custom_emoji_match:
        emoji_id = custom_emoji_match.group(1)
        return f"https://cdn.discordapp.com/emojis/{emoji_id}.png"
    return None

def get_server_config(guild_id):
    """Get server configuration from the correct database table dynamically."""
    conn = None
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT * FROM server_config 
        WHERE server_id = ?
        ''', (guild_id,))
        
        row = cursor.fetchone()
        if row:
            columns = [description[0] for description in cursor.description]
            config_dict = dict(zip(columns, row))
            return config_dict
        return None
    except sqlite3.Error as e:
        print(f"Database error in get_server_config: {e}")
        return None
    finally:
        if 'conn' in locals():
            conn.close()

async def has_bot_management_permission_global(interaction: discord.Interaction) -> bool:
    """
    Checks if the user has server administrator permissions OR the configured admin_role (from server_config).
    This uses a fresh connection to transaction_bot.db.
    """
    admin_role_id = None
    conn = None
    try:
        conn = sqlite3.connect('transaction_bot.db')
        cursor = conn.cursor()
        cursor.execute("SELECT admin_role FROM server_config WHERE server_id = ?", (interaction.guild_id,))
        config_row = cursor.fetchone()
        if config_row and config_row[0] is not None:
            admin_role_id = config_row[0]
    except sqlite3.Error as e:
        print(f"Database error fetching admin_role for global permission check: {e}")
        # Fallback to admin only if DB fails
        return interaction.user.guild_permissions.administrator
    finally:
        if conn:
            conn.close()

    if interaction.user.guild_permissions.administrator:
        return True
    if admin_role_id and interaction.guild:
        admin_role_obj = interaction.guild.get_role(admin_role_id)
        if admin_role_obj and admin_role_obj in interaction.user.roles:
            return True
    return False


@bot.tree.command(name="appoint", description="Appoint a player as franchise owner of a team")
@app_commands.describe(
    team_role="The team role",
    player="The player to appoint as franchise owner",
    emoji="The team emoji (optional). Provide to register/update team emoji."
)
async def appoint(
    interaction: discord.Interaction,
    team_role: discord.Role,
    player: discord.User,
    emoji: Optional[str] = None # Make emoji parameter optional
):
    # Permission check: Only administrators or bot managers can use this command
    if not await has_bot_management_permission_global(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)

    league_conn = None
    transaction_conn = None
    try:
        # Connect to both databases
        league_conn = sqlite3.connect('league_config.db')
        league_cursor = league_conn.cursor()
        
        transaction_conn = sqlite3.connect('transaction_bot.db')
        transaction_cursor = transaction_conn.cursor()

        # Create league_teams table if it doesn't exist
        league_cursor.execute('''
        CREATE TABLE IF NOT EXISTS league_teams (
            role_id TEXT NOT NULL,
            guild_id TEXT NOT NULL,
            team_name TEXT NOT NULL,
            emoji TEXT, 
            PRIMARY KEY (role_id, guild_id)
        )
        ''')
        league_conn.commit() # Commit table creation to ensure it exists

        # Get franchise owner role from config
        config_dict = get_server_config(interaction.guild.id)

        if not config_dict or not config_dict.get('franchise_owner_role'):
            return await interaction.followup.send(
                "Franchise owner role not configured. Please use `/setup` first.",
                ephemeral=True
            )

        franchise_role_id = config_dict['franchise_owner_role']
        franchise_role = interaction.guild.get_role(franchise_role_id)
        if not franchise_role:
            return await interaction.followup.send(
                f"Configured franchise owner role (ID: {franchise_role_id}) not found in server.",
                ephemeral=True
            )

        # Check if team exists in database and retrieve its current emoji and name
        league_cursor.execute("""
            SELECT emoji, team_name FROM league_teams 
            WHERE role_id = ? AND guild_id = ?
        """, (str(team_role.id), str(interaction.guild.id)))
        existing_team_info = league_cursor.fetchone() # This will be (emoji_str, team_name_str) or None

        display_emoji = None # This variable will hold the emoji to be used in the embed and messages
        feedback_message = "" # Message to provide specific feedback to the user

        if emoji: # User provided an emoji in the command
            if existing_team_info:
                # Team exists, update its name and emoji in the database
                league_cursor.execute('''
                    UPDATE league_teams
                    SET team_name = ?, emoji = ?
                    WHERE role_id = ? AND guild_id = ?
                ''', (team_role.name, emoji, str(team_role.id), str(interaction.guild.id)))
                feedback_message = f"Updated {team_role.mention}'s name to '{team_role.name}' and emoji to {emoji}."
            else:
                # Team does not exist, insert new team with the provided emoji
                league_cursor.execute('''
                    INSERT INTO league_teams 
                    (role_id, guild_id, team_name, emoji)
                    VALUES (?, ?, ?, ?)
                ''', (str(team_role.id), str(interaction.guild.id), team_role.name, emoji))
                feedback_message = f"Registered {team_role.mention} as a team with emoji {emoji}."
            league_conn.commit() # Commit changes to the league_config.db
            display_emoji = emoji # Use the provided emoji for the embed
        else: # User did NOT provide an emoji with the command
            if existing_team_info:
                # Team exists, use its existing emoji from the database
                display_emoji = existing_team_info[0] # Get the emoji string from the fetched data
                feedback_message = f"Using existing team information for {team_role.mention}."
            else:
                # Team does not exist and no emoji was provided, so it cannot be appointed
                # and team info won't be displayed correctly without an emoji.
                league_conn.close()
                transaction_conn.close()
                return await interaction.followup.send(
                    f"The role {team_role.mention} is not registered as a team. To register it, please provide an emoji with the command (e.g., `/appoint team_role: @MyTeam player: @User emoji: 🏈`).",
                    ephemeral=True
                )
        
        # Get member object for the player to be appointed
        member_to_appoint = interaction.guild.get_member(player.id) # Renamed to avoid 'player' being confused with 'player' user argument
        if not member_to_appoint:
            return await interaction.followup.send(
                "Could not find the specified player in the server.",
                ephemeral=True
            )

        # Add the team role and the franchise owner role to the player
        try:
            # Check if player already has the role
            if team_role not in member_to_appoint.roles:
                await member_to_appoint.add_roles(team_role, reason=f"Appointed FO of {team_role.name}")
            if franchise_role not in member_to_appoint.roles:
                await member_to_appoint.add_roles(franchise_role, reason=f"Appointed FO of {team_role.name}")
            
            # Remove player from any other team roles if they are moving teams for the FO role
            # This is a common requirement in league bots to prevent a player being on multiple teams.
            # Fetch all team roles for the guild
            league_cursor.execute("SELECT role_id FROM league_teams WHERE guild_id = ?", (str(interaction.guild.id),))
            all_team_role_ids = [int(row[0]) for row in league_cursor.fetchall()]
            
            for role_id in all_team_role_ids:
                if role_id == team_role.id: # Skip the new team's role
                    continue
                other_team_role_obj = interaction.guild.get_role(role_id)
                if other_team_role_obj and other_team_role_obj in member_to_appoint.roles:
                    await member_to_appoint.remove_roles(other_team_role_obj, reason=f"Appointed FO of {team_role.name} - removed from old team")
                    print(f"Removed {member_to_appoint.display_name} from old team role {other_team_role_obj.name}.")

        except discord.Forbidden:
            # More specific error message for Forbidden error
            await interaction.followup.send(
                f"I lack permissions to add the {team_role.name} or {franchise_role.name} role to {player.mention}. "
                "Please check my role hierarchy (my highest role must be above the roles I assign) and ensure I have 'Manage Roles' permission.",
                ephemeral=True
            )
            return
        except Exception as e:
            print(f"Error adding roles to player: {e}")
            traceback.print_exc()
            return await interaction.followup.send(f"An unexpected error occurred while assigning roles: {e}", ephemeral=True)

        # Create embed for the transaction channel
        # Use team_role.color for the embed color, defaulting to a specific gold color
        embed_color = team_role.color if team_role.color != discord.Color.default() else discord.Color.gold()
        
        # Construct the description, including emoji and formatted mentions
        description = (
            f"**Appointment**\n\n"
            f"{player.mention} `{player.display_name}` has been appointed **Franchise Owner** of the "
            f"{display_emoji + ' ' if display_emoji else ''}{team_role.mention} by "
            f"{interaction.user.mention} `{interaction.user.display_name}`!"
        )
        
        transaction_embed = discord.Embed(
            title=f"{team_role.name} Transaction (Appointment)", # Dynamic team name in title
            description=description,
            color=embed_color, # Use determined color
            timestamp=DateTime.now(TimeZone.utc)
        )

        # Set emoji as thumbnail
        emoji_url = get_emoji_url(display_emoji)
        if emoji_url:
            transaction_embed.set_thumbnail(url=emoji_url)

        # Set author to server logo
        if interaction.guild.icon:
            transaction_embed.set_author(name=interaction.guild.name, icon_url=interaction.guild.icon.url)
        
        # Set the footer of the embed with the appointer's information
        transaction_embed.set_footer(
            text=f"Appointed by {interaction.user.display_name}",
            icon_url=interaction.user.display_avatar.url
        )

        # Send the embed to the configured transaction channel if it exists
        transaction_channel_id = config_dict.get('transaction_channel') # Use config_dict.get
        if transaction_channel_id: # Check if transaction_channel ID is available from config
            transaction_channel = interaction.guild.get_channel(int(transaction_channel_id))
            if transaction_channel and isinstance(transaction_channel, discord.TextChannel):
                try:
                    await transaction_channel.send(embed=transaction_embed)
                except discord.Forbidden:
                    print(f"Bot lacks permission to send to transaction channel {transaction_channel_id}")
                    await interaction.followup.send(f"Successfully appointed {player.mention}, but could not send transaction log to channel {transaction_channel.mention} (missing permissions).", ephemeral=True)
                except Exception as e:
                    print(f"Error sending to transaction channel: {e}")
                    traceback.print_exc()
                    await interaction.followup.send(f"Successfully appointed {player.mention}, but an error occurred while sending transaction log: {e}", ephemeral=True)
            else:
                await interaction.followup.send(f"Successfully appointed {player.mention}, but configured transaction channel (ID: {transaction_channel_id}) not found or not a text channel.", ephemeral=True)
        else:
            await interaction.followup.send(f"Successfully appointed {player.mention}, but no transaction channel is configured.", ephemeral=True)

        # Send a final success message to the user who invoked the command
        await interaction.followup.send(
            f"{feedback_message}\nSuccessfully appointed {player.mention} as Franchise Owner of {team_role.mention}!",
            ephemeral=True
        )

    except Exception as error:
        # Log any errors that occur during the command execution
        print(f"Error in appoint command: {error}")
        traceback.print_exc() # Print full traceback for debugging
        await interaction.followup.send(
            f"An error occurred while appointing the franchise owner: {error}",
            ephemeral=True
        )
    finally:
        # Ensure database connections are closed even if an error occurs
        if league_conn:
            league_conn.close()
        if transaction_conn:
            transaction_conn.close()
