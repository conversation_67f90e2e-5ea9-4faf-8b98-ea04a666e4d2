import sqlite3
import os

def add_missing_columns():
    """Add missing columns to SQLite database"""
    db_path = 'transaction_bot.db'
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found")
        return False

    print("🔧 Adding missing columns to SQLite database...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    success = True
    
    try:
        # List of columns to add
        columns_to_add = [
            # For guild_settings
            ("guild_settings", "transaction_log_channel", "INTEGER"),
            # For slot_configs
            ("slot_configs", "weekly_games_channel", "INTEGER"),
            ("slot_configs", "score_channel", "INTEGER"),
            ("slot_configs", "created_at", "TEXT"),
            ("slot_configs", "updated_at", "TEXT"),
            # For teams
            ("teams", "guild_id", "INTEGER"),
        ]
        
        for table, column, col_type in columns_to_add:
            try:
                cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {col_type}")
                print(f"✅ Added {column} to {table}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    print(f"ℹ️ Column {column} already exists in {table}")
                else:
                    print(f"❌ Error adding {column} to {table}: {e}")
                    success = False
        
        conn.commit()
        print("✅ Database schema updated successfully")
        
        # Verify columns exist
        print("\n🔍 Verifying columns...")
        for table, column, _ in columns_to_add:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            has_column = any(col[1] == column for col in columns)
            status = "✅" if has_column else "❌"
            print(f"{status} {table}.{column}: {has_column}")
            
    except Exception as e:
        print(f"❌ Error updating schema: {e}")
        success = False
        conn.rollback()
    finally:
        conn.close()
    
    return success

if __name__ == "__main__":
    if add_missing_columns():
        print("\n✅ Schema migration completed successfully!")
    else:
        print("\n❌ Schema migration failed!")
