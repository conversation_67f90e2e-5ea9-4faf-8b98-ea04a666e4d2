# =========================
# Database Compatibility Fixes for Supabase Integration
# =========================

"""
This file contains fixes to ensure proper Supabase integration and database compatibility.
Key fixes include:
1. Replace direct bot.cursor.execute() calls with bot.db.execute() calls
2. Add proper error handling for Supabase API failures
3. Fix data type conversions between SQLite and Supabase
4. Add connection retry logic
5. Improve query parsing for complex SQL statements
"""

import sqlite3
import traceback
import time
import json
from typing import Optional, Any, Dict, List, Tuple, Union

class ImprovedDatabaseAdapter:
    """Enhanced database adapter with better Supabase support and error handling"""
    
    def __init__(self, db_type='sqlite', supabase_url=None, supabase_key=None):
        self.db_type = db_type
        self.supabase_client = None
        self.sqlite_conn = None
        self.sqlite_cursor = None
        self.retry_count = 3
        self.retry_delay = 1.0
        
        print(f"🗄️ Initializing database adapter: {self.db_type}")
        
        if self.db_type == 'supabase':
            self.setup_supabase(supabase_url, supabase_key)
        else:
            self.setup_sqlite()
    
    def setup_supabase(self, url, key):
        """Setup Supabase connection with proper error handling"""
        if not url or not key:
            print("⚠️ Supabase credentials missing, falling back to SQLite")
            self.db_type = 'sqlite'
            self.setup_sqlite()
            return
        
        try:
            from supabase import create_client
            self.supabase_client = create_client(url, key)
            # Test connection
            self.supabase_client.table('guild_settings').select('*').limit(1).execute()
            print("✅ Supabase connection established")
        except ImportError:
            print("⚠️ Supabase package not installed, falling back to SQLite")
            self.db_type = 'sqlite'
            self.setup_sqlite()
        except Exception as e:
            print(f"⚠️ Supabase connection failed: {e}, falling back to SQLite")
            self.db_type = 'sqlite'
            self.setup_sqlite()
    
    def setup_sqlite(self):
        """Setup SQLite connection"""
        try:
            self.sqlite_conn = sqlite3.connect('transaction_bot.db', timeout=30.0)
            self.sqlite_cursor = self.sqlite_conn.cursor()
            print("✅ SQLite connection established")
        except Exception as e:
            print(f"❌ SQLite connection failed: {e}")
            raise
    
    def execute_with_retry(self, query: str, params: tuple = None):
        """Execute query with retry logic for Supabase"""
        for attempt in range(self.retry_count):
            try:
                return self.execute(query, params)
            except Exception as e:
                if attempt == self.retry_count - 1:
                    raise
                print(f"⚠️ Database operation failed (attempt {attempt + 1}/{self.retry_count}): {e}")
                time.sleep(self.retry_delay * (attempt + 1))
    
    def execute(self, query: str, params: tuple = None):
        """Execute query with improved error handling"""
        try:
            if self.db_type == 'supabase':
                return self.execute_supabase(query, params)
            else:
                return self.execute_sqlite(query, params)
        except Exception as e:
            print(f"❌ Database error: {e}")
            print(f"   Query: {query}")
            print(f"   Params: {params}")
            traceback.print_exc()
            raise
    
    def execute_sqlite(self, query: str, params: tuple = None):
        """Execute SQLite query with error handling"""
        try:
            if params:
                self.sqlite_cursor.execute(query, params)
            else:
                self.sqlite_cursor.execute(query)
            return self.sqlite_cursor.fetchall()
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                time.sleep(0.1)
                return self.execute_sqlite(query, params)
            raise
        except Exception as e:
            print(f"SQLite error: {e}")
            raise
    
    def execute_supabase(self, query: str, params: tuple = None):
        """Execute Supabase query with improved parsing"""
        try:
            query_upper = query.upper().strip()
            
            # Handle different query types
            if query_upper.startswith('SELECT'):
                return self.handle_select_query_improved(query, params)
            elif query_upper.startswith('INSERT'):
                return self.handle_insert_query_improved(query, params)
            elif query_upper.startswith('UPDATE'):
                return self.handle_update_query_improved(query, params)
            elif query_upper.startswith('DELETE'):
                return self.handle_delete_query_improved(query, params)
            elif query_upper.startswith(('CREATE TABLE', 'ALTER TABLE', 'DROP TABLE')):
                # Skip DDL statements for Supabase
                print(f"Skipping DDL statement for Supabase: {query}")
                return []
            elif query_upper.startswith('PRAGMA'):
                # Skip SQLite-specific statements
                return []
            else:
                print(f"Unsupported query type: {query}")
                return []
                
        except Exception as e:
            print(f"Supabase execution error: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            raise
    
    def normalize_guild_id(self, guild_id: Any) -> str:
        """Normalize guild ID to string for consistent storage"""
        if guild_id is None:
            return None
        return str(guild_id)
    
    def normalize_role_id(self, role_id: Any) -> str:
        """Normalize role ID to string for consistent storage"""
        if role_id is None:
            return None
        return str(role_id)
    
    def handle_select_query_improved(self, query: str, params: tuple = None):
        """Improved SELECT query handling for Supabase"""
        query_lower = query.lower()
        
        # Parse table name
        table_name = self.extract_table_name(query_lower, 'from')
        if not table_name:
            print(f"Could not extract table name from query: {query}")
            return []
        
        try:
            table = self.supabase_client.table(table_name)
            
            # Handle COUNT queries
            if 'count(*)' in query_lower:
                result = table.select('*', count='exact').execute()
                return [(result.count,)]
            
            # Apply WHERE conditions
            table = self.apply_where_conditions(table, query_lower, params)
            
            # Execute query
            result = table.execute()
            
            # Convert result to match SQLite format
            return self.convert_supabase_result(result.data, table_name)
            
        except Exception as e:
            print(f"Error in SELECT query for table {table_name}: {e}")
            raise
    
    def extract_table_name(self, query_lower: str, keyword: str) -> Optional[str]:
        """Extract table name from SQL query"""
        try:
            keyword_pos = query_lower.find(f'{keyword} ')
            if keyword_pos == -1:
                return None
            
            # Extract everything after the keyword
            after_keyword = query_lower[keyword_pos + len(keyword) + 1:]
            
            # Find the table name (first word)
            table_name = after_keyword.split()[0]
            
            # Remove any SQL keywords or punctuation
            table_name = table_name.split(' ')[0].split('(')[0].strip()
            
            return table_name
        except:
            return None
    
    def apply_where_conditions(self, table, query_lower: str, params: tuple = None):
        """Apply WHERE conditions to Supabase query builder"""
        if not params or 'where' not in query_lower:
            return table
        
        param_index = 0
        
        # Handle guild_id conditions
        if 'where guild_id = ?' in query_lower:
            table = table.eq('guild_id', self.normalize_guild_id(params[param_index]))
            param_index += 1
        
        # Handle slot_id conditions
        if 'slot_id = ?' in query_lower:
            # Find which parameter is slot_id
            slot_param = params[param_index] if param_index < len(params) else None
            if slot_param:
                table = table.eq('slot_id', slot_param)
                param_index += 1
        
        # Handle role_id conditions
        if 'role_id = ?' in query_lower:
            role_param = params[param_index] if param_index < len(params) else None
            if role_param:
                table = table.eq('role_id', self.normalize_role_id(role_param))
                param_index += 1
        
        # Handle is_default conditions
        if 'is_default = ?' in query_lower:
            default_param = params[param_index] if param_index < len(params) else None
            if default_param is not None:
                table = table.eq('is_default', default_param)
                param_index += 1
        
        # Handle status conditions
        if 'status = ?' in query_lower:
            status_param = params[param_index] if param_index < len(params) else None
            if status_param:
                table = table.eq('status', status_param)
                param_index += 1
        
        return table
    
    def convert_supabase_result(self, data: List[Dict], table_name: str) -> List[Tuple]:
        """Convert Supabase result to SQLite-like tuple format"""
        if not data:
            return []
        
        # Define field orders for each table to match SQLite behavior
        field_orders = {
            'guild_settings': ['guild_id', 'admin_role', 'log_channel', 'application_channel', 
                             'suspended_role', 'suspended_channel', 'application_blacklist_role', 
                             'transaction_log_channel'],
            'slots': ['slot_id', 'guild_id', 'slot_name', 'description', 'is_default', 'weeks_per_season'],
            'slot_configs': ['slot_id', 'franchise_owner_role', 'general_manager_role', 'head_coach_role',
                           'assistant_coach_role', 'free_agent_role', 'transaction_channel', 'gametime_channel',
                           'referee_role', 'streamer_role', 'trade_channel', 'draft_channel', 'pickup_host_role',
                           'pickup_channel', 'score_channel'],
            'league_teams': ['id', 'role_id', 'slot_id', 'team_name', 'emoji'],
            'contracts': ['contract_id', 'guild_id', 'slot_id', 'player_id', 'team_role_id', 'contract_amount',
                         'contract_length', 'time_unit', 'start_date', 'end_date', 'status'],
            'slot_command_settings': ['slot_id', 'guild_id', 'sign_enabled', 'release_enabled', 'offer_enabled',
                                    'roster_cap', 'money_cap_enabled', 'money_cap_amount'],
            'slot_demand_config': ['slot_id', 'demands_enabled', 'demand_limit', 'demand_channel',
                                 'five_demands_role', 'four_demands_role', 'three_demands_role',
                                 'two_demands_role', 'one_demand_role', 'no_demands_role']
        }
        
        field_order = field_orders.get(table_name, list(data[0].keys()))
        
        result = []
        for row in data:
            tuple_row = tuple(row.get(field) for field in field_order)
            result.append(tuple_row)
        
        return result
    
    def handle_insert_query_improved(self, query: str, params: tuple = None):
        """Improved INSERT query handling"""
        query_lower = query.lower()
        table_name = self.extract_table_name(query_lower, 'into')
        
        if not table_name or not params:
            print(f"Invalid INSERT query: {query}")
            return []
        
        try:
            table = self.supabase_client.table(table_name)
            
            # Build data dictionary based on table structure
            data = self.build_insert_data(table_name, query_lower, params)
            
            # Handle INSERT OR IGNORE
            if 'insert or ignore' in query_lower:
                # Check if record exists first
                existing = self.check_existing_record(table_name, data)
                if existing:
                    return []
            
            # Execute insert
            result = table.insert(data).execute()
            return []
            
        except Exception as e:
            print(f"Error in INSERT query for table {table_name}: {e}")
            raise
    
    def build_insert_data(self, table_name: str, query_lower: str, params: tuple) -> dict:
        """Build data dictionary for INSERT operation"""
        data = {}
        
        # Define mappings for common table structures
        if table_name == 'guild_settings':
            fields = ['guild_id', 'admin_role', 'log_channel', 'application_channel', 
                     'suspended_role', 'suspended_channel', 'application_blacklist_role']
            for i, field in enumerate(fields):
                if i < len(params):
                    value = params[i]
                    if field == 'guild_id':
                        data[field] = self.normalize_guild_id(value)
                    elif value is not None:
                        data[field] = self.normalize_role_id(value) if 'role' in field or 'channel' in field else value
        
        elif table_name == 'slots':
            fields = ['slot_id', 'guild_id', 'slot_name', 'description', 'is_default', 'weeks_per_season']
            for i, field in enumerate(fields):
                if i < len(params) and params[i] is not None:
                    value = params[i]
                    if field == 'guild_id':
                        data[field] = self.normalize_guild_id(value)
                    else:
                        data[field] = value
        
        elif table_name == 'league_teams':
            fields = ['role_id', 'slot_id', 'team_name', 'emoji']
            for i, field in enumerate(fields):
                if i < len(params):
                    value = params[i]
                    if field == 'role_id':
                        data[field] = self.normalize_role_id(value)
                    else:
                        data[field] = value
        
        # Add more table mappings as needed
        
        return data
    
    def check_existing_record(self, table_name: str, data: dict) -> bool:
        """Check if record already exists (for INSERT OR IGNORE)"""
        try:
            table = self.supabase_client.table(table_name)
            
            # Use primary key or unique fields to check existence
            if table_name == 'guild_settings' and 'guild_id' in data:
                result = table.select('guild_id').eq('guild_id', data['guild_id']).execute()
                return len(result.data) > 0
            elif table_name == 'slots' and 'slot_id' in data:
                result = table.select('slot_id').eq('slot_id', data['slot_id']).execute()
                return len(result.data) > 0
            elif table_name == 'league_teams' and 'role_id' in data:
                result = table.select('role_id').eq('role_id', data['role_id']).execute()
                return len(result.data) > 0
            
            return False
        except:
            return False
    
    def handle_update_query_improved(self, query: str, params: tuple = None):
        """Improved UPDATE query handling"""
        query_lower = query.lower()
        table_name = self.extract_table_name(query_lower, 'update')
        
        if not table_name or not params:
            return []
        
        try:
            table = self.supabase_client.table(table_name)
            
            # Extract SET clause and WHERE clause
            set_data = self.extract_set_clause(query_lower, params)
            where_conditions = self.extract_where_clause(query_lower, params)
            
            # Apply WHERE conditions
            query_builder = table.update(set_data)
            for field, value in where_conditions.items():
                if field == 'guild_id':
                    query_builder = query_builder.eq(field, self.normalize_guild_id(value))
                elif field in ['role_id', 'team_role_id']:
                    query_builder = query_builder.eq(field, self.normalize_role_id(value))
                else:
                    query_builder = query_builder.eq(field, value)
            
            result = query_builder.execute()
            return []
            
        except Exception as e:
            print(f"Error in UPDATE query for table {table_name}: {e}")
            raise
    
    def extract_set_clause(self, query_lower: str, params: tuple) -> dict:
        """Extract SET clause data from UPDATE query"""
        set_data = {}
        
        # Simple parsing for common SET patterns
        if 'set ' in query_lower and params:
            # This is a simplified parser - you may need to enhance it
            if len(params) >= 1:
                # Assume first parameter is the value being set
                # You'll need to enhance this based on your specific queries
                set_data['value'] = params[0]
        
        return set_data
    
    def extract_where_clause(self, query_lower: str, params: tuple) -> dict:
        """Extract WHERE clause conditions from UPDATE query"""
        where_data = {}
        
        # Simple parsing for common WHERE patterns
        if 'where ' in query_lower and params:
            # Assume last parameter is typically the WHERE condition
            if 'guild_id = ?' in query_lower:
                where_data['guild_id'] = params[-1]
            elif 'slot_id = ?' in query_lower:
                where_data['slot_id'] = params[-1]
            elif 'role_id = ?' in query_lower:
                where_data['role_id'] = params[-1]
        
        return where_data
    
    def handle_delete_query_improved(self, query: str, params: tuple = None):
        """Improved DELETE query handling"""
        query_lower = query.lower()
        table_name = self.extract_table_name(query_lower, 'from')
        
        if not table_name:
            return []
        
        try:
            table = self.supabase_client.table(table_name)
            
            # Apply WHERE conditions for DELETE
            if params and 'where' in query_lower:
                if 'guild_id = ?' in query_lower:
                    table.delete().eq('guild_id', self.normalize_guild_id(params[0])).execute()
                elif 'slot_id = ?' in query_lower:
                    table.delete().eq('slot_id', params[0]).execute()
                elif 'role_id = ?' in query_lower:
                    table.delete().eq('role_id', self.normalize_role_id(params[0])).execute()
            
            return []
            
        except Exception as e:
            print(f"Error in DELETE query for table {table_name}: {e}")
            raise
    
    def commit(self):
        """Commit transaction (SQLite only)"""
        if self.db_type == 'sqlite' and self.sqlite_conn:
            self.sqlite_conn.commit()
    
    def close(self):
        """Close database connection"""
        if self.db_type == 'sqlite' and self.sqlite_conn:
            self.sqlite_conn.close()
    
    def fetchone(self):
        """SQLite compatibility method"""
        if self.db_type == 'sqlite':
            return self.sqlite_cursor.fetchone()
        return None
    
    def fetchall(self):
        """SQLite compatibility method"""
        if self.db_type == 'sqlite':
            return self.sqlite_cursor.fetchall()
        return []

# Additional utility functions for database migration and consistency checks

def check_supabase_tables(supabase_client):
    """Check if all required Supabase tables exist"""
    required_tables = [
        'guild_settings', 'slots', 'slot_configs', 'league_teams', 
        'contracts', 'transactions', 'applications', 'slot_command_settings',
        'slot_demand_config', 'gametimes', 'disband_transactions', 'live_management_lists'
    ]
    
    missing_tables = []
    for table in required_tables:
        try:
            supabase_client.table(table).select('*').limit(1).execute()
        except Exception as e:
            missing_tables.append(table)
            print(f"⚠️ Table '{table}' not found or accessible: {e}")
    
    if missing_tables:
        print(f"❌ Missing Supabase tables: {missing_tables}")
        print("Please run the migration script in your Supabase SQL Editor")
        return False
    
    print("✅ All required Supabase tables found")
    return True

def create_backup_before_migration(sqlite_path='transaction_bot.db'):
    """Create a backup of SQLite database before migration"""
    import shutil
    from datetime import datetime
    
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{sqlite_path}.backup_{timestamp}"
        shutil.copy2(sqlite_path, backup_path)
        print(f"✅ SQLite backup created: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"⚠️ Failed to create backup: {e}")
        return None

def validate_data_consistency(db_adapter):
    """Validate data consistency after migration"""
    try:
        # Check if basic tables have data
        guild_count = db_adapter.execute("SELECT COUNT(*) FROM guild_settings")
        slot_count = db_adapter.execute("SELECT COUNT(*) FROM slots")
        team_count = db_adapter.execute("SELECT COUNT(*) FROM league_teams")
        
        print(f"📊 Data validation:")
        print(f"   Guilds: {guild_count[0][0] if guild_count else 0}")
        print(f"   Slots: {slot_count[0][0] if slot_count else 0}")
        print(f"   Teams: {team_count[0][0] if team_count else 0}")
        
        return True
    except Exception as e:
        print(f"⚠️ Data validation failed: {e}")
        return False
