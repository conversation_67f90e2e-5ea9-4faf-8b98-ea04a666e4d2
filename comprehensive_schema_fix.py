import sqlite3
import os
import datetime

def log_message(message):
    """Log message to file and print"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp}] {message}"
    print(log_msg)
    
    # Also write to log file
    with open('schema_fix.log', 'a', encoding='utf-8') as f:
        f.write(log_msg + '\n')

def fix_database_schema():
    """Fix the database schema by adding missing columns"""
    log_message("🔧 Starting database schema fix...")
    
    db_path = 'transaction_bot.db'
    if not os.path.exists(db_path):
        log_message(f"❌ Database file {db_path} not found")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current schema
        log_message("🔍 Checking current database schema...")
        
        # Check slot_configs table
        cursor.execute("PRAGMA table_info(slot_configs)")
        slot_columns = {col[1]: col[2] for col in cursor.fetchall()}
        log_message(f"slot_configs has {len(slot_columns)} columns")
        
        # Check guild_settings table  
        cursor.execute("PRAGMA table_info(guild_settings)")
        guild_columns = {col[1]: col[2] for col in cursor.fetchall()}
        log_message(f"guild_settings has {len(guild_columns)} columns")
        
        # Check teams table
        cursor.execute("PRAGMA table_info(teams)")
        teams_columns = {col[1]: col[2] for col in cursor.fetchall()}
        log_message(f"teams has {len(teams_columns)} columns")
        
        # Add missing columns
        columns_to_add = [
            ("slot_configs", "weekly_games_channel", "INTEGER"),
            ("slot_configs", "score_channel", "INTEGER"), 
            ("slot_configs", "created_at", "TEXT"),
            ("slot_configs", "updated_at", "TEXT"),
            ("guild_settings", "transaction_log_channel", "INTEGER"),
            ("teams", "guild_id", "INTEGER"),
        ]
        
        added_count = 0
        for table, column, col_type in columns_to_add:
            if table == "slot_configs" and column not in slot_columns:
                try:
                    cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {col_type}")
                    log_message(f"✅ Added {column} to {table}")
                    added_count += 1
                except Exception as e:
                    log_message(f"❌ Failed to add {column} to {table}: {e}")
            elif table == "guild_settings" and column not in guild_columns:
                try:
                    cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {col_type}")
                    log_message(f"✅ Added {column} to {table}")
                    added_count += 1
                except Exception as e:
                    log_message(f"❌ Failed to add {column} to {table}: {e}")
            elif table == "teams" and column not in teams_columns:
                try:
                    cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {col_type}")
                    log_message(f"✅ Added {column} to {table}")
                    added_count += 1
                except Exception as e:
                    log_message(f"❌ Failed to add {column} to {table}: {e}")
            else:
                log_message(f"ℹ️ Column {column} already exists in {table}")
        
        # Commit changes
        conn.commit()
        log_message(f"✅ Database schema updated. Added {added_count} columns.")
        
        # Verify the final schema
        log_message("🔍 Verifying final schema...")
        
        # Re-check slot_configs
        cursor.execute("PRAGMA table_info(slot_configs)")
        final_slot_columns = [col[1] for col in cursor.fetchall()]
        log_message(f"slot_configs final columns: {final_slot_columns}")
        
        # Check for required columns
        required_slot_cols = ['weekly_games_channel', 'score_channel', 'created_at', 'updated_at']
        missing_slot = [col for col in required_slot_cols if col not in final_slot_columns]
        if missing_slot:
            log_message(f"❌ Still missing in slot_configs: {missing_slot}")
        else:
            log_message("✅ All required columns present in slot_configs")
        
        # Re-check guild_settings
        cursor.execute("PRAGMA table_info(guild_settings)")
        final_guild_columns = [col[1] for col in cursor.fetchall()]
        if 'transaction_log_channel' not in final_guild_columns:
            log_message("❌ Still missing transaction_log_channel in guild_settings")
        else:
            log_message("✅ transaction_log_channel present in guild_settings")
        
        conn.close()
        log_message("✅ Schema fix completed successfully!")
        return True
        
    except Exception as e:
        log_message(f"❌ Error during schema fix: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    # Clear previous log
    if os.path.exists('schema_fix.log'):
        os.remove('schema_fix.log')
    
    success = fix_database_schema()
    
    if success:
        log_message("🎉 Database is ready for bot operation!")
    else:
        log_message("💥 Database fix failed - check errors above")
    
    log_message("📋 Check schema_fix.log for detailed output")
