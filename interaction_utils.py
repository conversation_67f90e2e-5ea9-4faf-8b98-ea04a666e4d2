import discord
from discord import app_commands
from typing import Optional, Callable, Any, Coroutine

async def safe_defer(
    interaction: discord.Interaction,
    ephemeral: bool = False,
    think_msg: Optional[str] = None,
    followup_msg: Optional[str] = None
) -> bool:
    """
    Safely defer an interaction with timeout handling.
    Returns True if deferred successfully, False if interaction already failed.
    
    Args:
        interaction: The interaction to defer
        ephemeral: Whether the response should be ephemeral
        think_msg: Optional thinking message to send
        followup_msg: Optional follow-up message after deferring
    """
    try:
        # Check if interaction is already responded
        if interaction.response.is_done():
            return False
            
        # Send initial response
        if think_msg:
            await interaction.response.send_message(
                f"{think_msg}...",
                ephemeral=ephemeral,
                delete_after=5
            )
        else:
            await interaction.response.defer(
                ephemeral=ephemeral,
                thinking=bool(think_msg)
            )
            
        # Send followup if provided
        if followup_msg:
            await interaction.followup.send(
                followup_msg,
                ephemeral=ephemeral
            )
            
        return True
        
    except discord.errors.InteractionResponded:
        return False
    except discord.errors.NotFound:
        return False
    except Exception as e:
        print(f"Error deferring interaction: {e}")
        return False

async def safe_followup(
    interaction: discord.Interaction,
    content: str,
    ephemeral: bool = False,
    **kwargs
) -> Optional[discord.WebhookMessage]:
    """
    Safely send a followup message with error handling.
    Returns the message if sent successfully, None otherwise.
    """
    try:
        # Check if interaction is still valid
        if interaction.is_expired():
            return None
            
        return await interaction.followup.send(
            content=content,
            ephemeral=ephemeral,
            **kwargs
        )
    except Exception as e:
        print(f"Error sending followup: {e}")
        return None
