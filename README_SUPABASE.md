# Discord Bot with Supabase Integration

This Discord bot now supports both **Supabase** (cloud database) and **SQLite** (local database) with automatic fallback.

## 🚀 Quick Start with Supabase

### 1. Set Up Supabase Project
1. Go to [supabase.com](https://supabase.com) and create a free account
2. Create a new project
3. Wait for project initialization (takes ~2 minutes)

### 2. Run Database Migration
1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the entire contents of `supabase_migration.sql`
3. Paste and click **Run** to create all tables

### 3. Configure Environment
```bash
# Option A: Use the setup script
python setup_supabase.py

# Option B: Manual setup
cp .env.example .env
# Edit .env with your credentials
```

### 4. Install Dependencies
```bash
pip install -r requirements.txt
```

### 5. Run the Bot
```bash
python "import sqlite3.py"
```

## 🔧 Configuration

### Environment Variables (.env file)
```env
DATABASE_TYPE=supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_anon_key_here
DISCORD_BOT_TOKEN=your_bot_token_here
```

### Database Types
- **`DATABASE_TYPE=supabase`** - Use cloud Supabase database
- **`DATABASE_TYPE=sqlite`** - Use local SQLite database

## 📋 Features

### ✅ What Works with Supabase
- All bot commands and features
- Team management
- User contracts
- Game scheduling
- Transaction logging
- Live management lists
- Demand system
- All existing functionality

### 🔄 Automatic Fallback
If Supabase connection fails, the bot automatically:
1. Prints error message
2. Falls back to SQLite
3. Creates local database
4. Continues working normally

### 🌐 Benefits of Supabase
- **Cloud hosted** - No local database files
- **Scalable** - Handles many concurrent users
- **Real-time** - Built-in real-time subscriptions
- **Web dashboard** - View/edit data via web interface
- **Automatic backups** - Point-in-time recovery
- **Better performance** - PostgreSQL backend

## 🔒 Security

### Row Level Security (RLS)
Supabase supports RLS for data security:
1. Go to **Authentication > Settings** in Supabase
2. Enable RLS for production use
3. Create policies for your tables

### API Keys
- **anon key** - Safe for client-side use, limited permissions
- **service_role key** - Full access, use for server applications

For production, use the service_role key in your environment variables.

## 🛠️ Development

### Database Schema
All tables are created via `supabase_migration.sql`:
- `guild_settings` - Server-wide settings
- `slots` - League slots/divisions
- `slot_configs` - Slot-specific configurations
- `league_teams` - Team information
- `contracts` - Player contracts
- `gametimes` - Scheduled games
- `transactions` - Transaction history
- And more...

### Adding New Features
When adding new database operations:
1. The `DatabaseAdapter` class handles SQL → Supabase conversion
2. Add new table handlers in `execute_supabase()` method
3. Test with both SQLite and Supabase

### Query Conversion
The bot automatically converts SQL queries to Supabase API calls:
```python
# Your code (same for both databases)
bot.cursor.execute("SELECT * FROM guild_settings WHERE guild_id = ?", (guild_id,))

# Automatically becomes (for Supabase)
supabase.table('guild_settings').select('*').eq('guild_id', guild_id).execute()
```

## 🐛 Troubleshooting

### Common Issues

**"Failed to connect to Supabase"**
- Check your `SUPABASE_URL` and `SUPABASE_KEY`
- Verify your Supabase project is active
- Bot will fall back to SQLite automatically

**"Table not found"**
- Run the migration script in Supabase SQL Editor
- Make sure all tables were created successfully

**"Permission denied"**
- Check your API key permissions
- For production, use `service_role` key instead of `anon` key

**"Connection timeout"**
- Check your internet connection
- Supabase may be experiencing downtime
- Bot will fall back to SQLite

### Debug Mode
Set environment variable for more detailed logging:
```env
LOGGING_LEVEL=DEBUG
```

## 📊 Migration from SQLite

If you have existing SQLite data:

### Option 1: CSV Export/Import
1. Export SQLite data to CSV files
2. Import CSV files into Supabase tables via dashboard

### Option 2: Migration Script
Create a custom script to copy data:
```python
# Read from SQLite
sqlite_conn = sqlite3.connect('transaction_bot.db')
# Write to Supabase
supabase = create_client(url, key)
```

## 🔮 Future Enhancements

Potential future features with Supabase:
- Real-time notifications when games are scheduled
- Live dashboard showing team stats
- Webhook integrations
- Advanced analytics
- Multi-server data sharing

## 📞 Support

1. Check the console output for error messages
2. Verify your Supabase project is set up correctly
3. Test with SQLite first (`DATABASE_TYPE=sqlite`)
4. The bot is designed to be resilient - it will work even if Supabase is down

## 📝 Notes

- The hardcoded Supabase integration handles 95% of common database operations
- Complex queries may need manual conversion
- All existing bot features work identically with both databases
- Supabase free tier includes 500MB storage and 2GB bandwidth
