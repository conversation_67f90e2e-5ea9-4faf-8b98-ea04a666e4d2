# CRITICAL DATABASE SCHEMA FIX

## Problem
The bot is failing to sync with Supabase because the local SQLite database is missing several columns that exist in the Supabase schema:

### Missing Columns:
- `slot_configs.weekly_games_channel` (INTEGER)
- `slot_configs.score_channel` (INTEGER) 
- `slot_configs.created_at` (TEXT)
- `slot_configs.updated_at` (TEXT)
- `guild_settings.transaction_log_channel` (INTEGER)
- `teams.guild_id` (INTEGER)

## Solution Steps

### Option 1: Manual SQLite Commands
Run these commands in your SQLite database:

```sql
-- Connect to database
sqlite3 transaction_bot.db

-- Add missing columns
ALTER TABLE slot_configs ADD COLUMN weekly_games_channel INTEGER;
ALTER TABLE slot_configs ADD COLUMN score_channel INTEGER;
ALTER TABLE slot_configs ADD COLUMN created_at TEXT;
ALTER TABLE slot_configs ADD COLUMN updated_at TEXT;
ALTER TABLE guild_settings ADD COLUMN transaction_log_channel INTEGER;
ALTER TABLE teams ADD COLUMN guild_id INTEGER;

-- Verify schema
PRAGMA table_info(slot_configs);
PRAGMA table_info(guild_settings);
PRAGMA table_info(teams);

-- Exit
.quit
```

### Option 2: Python Script
Run the `comprehensive_schema_fix.py` script:

```bash
python comprehensive_schema_fix.py
```

### Option 3: Batch File
Run the `fix_and_run.bat` file:

```bash
fix_and_run.bat
```

## Expected Result
After running any of the above solutions, the bot should be able to sync with Supabase without the "no column named" errors.

## Verification
1. Start the bot
2. Check that sync completes without column errors
3. Verify that `/list_teams` and other commands work
4. Confirm no more "missing guild_id" warnings

## Status
- ✅ Sync logic updated with automatic column migration
- ✅ Fallback logic added for failed migrations
- ⚠️ Manual schema fix required (one-time)
- ⚠️ Bot restart required after schema fix

The bot should now handle future schema changes automatically, but this one-time manual fix is needed to align the local SQLite schema with Supabase.
